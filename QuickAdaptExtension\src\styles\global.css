@import url('https://fonts.googleapis.com/css2?family=Syncopate:wght@700&display=swap');
:root {
	--qadptfont-family: Poppins, Proxima Nova, arial, serif;
    --primarycolor: #5F9EA0;
    --ext-background: #F6EEEE;
    --border-color: #ccc;
    --white-color: #fff;
    --back-light-color:#EAE2E2;
	--button-border-radius: 12px;
}
/* Apply font only to extension elements with qadpt- prefix */
[id*="qadpt-"], [class*="qadpt-"], #my-react-drawer, #my-react-drawer * {
	font-family: var(--qadptfont-family) !important;
}

/* Exclude specific elements that should not inherit extension fonts */
[id*="qadpt-"] *:not(.qadpt-rte *):not(.qadpt-preview *):not(.fal, .far, .fad, .fas, .fab, i):not(.qadpt-jodit *):not(.mat-icon),
[class*="qadpt-"] *:not(.qadpt-rte *):not(.qadpt-preview *):not(.fal, .far, .fad, .fas, .fab, i):not(.qadpt-jodit *):not(.mat-icon),
#my-react-drawer *:not(.qadpt-rte *):not(.qadpt-preview *):not(.fal, .far, .fad, .fas, .fab, i):not(.qadpt-jodit *):not(.mat-icon) {
	font-family: var(--qadptfont-family) !important;
}

/* Remove body font override - this was causing the host website font issues */
/* body {
	margin: 0;
	font-family: var(--qadptfont-family) !important;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
} */

/* Apply extension styling only to extension container */
#my-react-drawer {
	font-family: var(--qadptfont-family) !important;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

@font-face {
	font-family: "Gotham Pro";
	font-style: normal;
	/* src: local("Gotham Pro"), local("Gotham Pro"), url("../assets/fonts/GothamPro.woff2") format("woff2"),
		url("../assets/fonts/GothamPro.woff") format("woff"); */
}

@font-face {
	font-family: "Proxima Nova";
	font-style: normal;
	src: local("Proxima Nova"), local("ProximaNova-Regular"),
		url("../assets/fonts/ProximaNova-Regular.woff2") format("woff2"),
		url("../assets/fonts/ProximaNova-Regular.woff") format("woff");
}

@font-face {
	font-family: "qadapt-icons";
	src: url("../assets/fonts/qadapt-icons.eot?qmcsfb");
	src: url("../assets/fonts/qadapt-icons.eot?qmcsfb#iefix") format("embedded-opentype"),
		url("../assets/fonts/qadapt-icons.ttf?qmcsfb") format("truetype"),
		url("../assets/fonts/qadapt-icons.woff?qmcsfb") format("woff"),
		url("../assets/fonts/qadapt-icons.svg?qmcsfb#qadapt-icons") format("svg");
	font-weight: normal;
	font-style: normal;
	font-display: block;
}

