// DrawerStore - Manages the state for the QuickAdapt extension
//@ts-nocheck
import { Button } from "@mui/material";
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { getAllGuideByOrgId } from "../services/GuideListServices";
import { Routes } from "react-router-dom";
import { RouteSharp } from "@mui/icons-material";
import React from "react";
import { selectedtemp } from "../components/drawer/Drawer";
import useHistoryStore from "./historyStore";
import { recordChange, extractStateForHistory, deepClone, applyHistoryEntry } from "../utils/historyUtils";

export const defaultAnnouncementImages = [
	"https://images.unsplash.com/photo-1526779259212-939e64788e3c?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8N3x8ZnJlZSUyMGltYWdlc3xlbnwwfHwwfHx8MA%3D%3D",
	"https://images.ctfassets.net/hrltx12pl8hq/28ECAQiPJZ78hxatLTa7Ts/2f695d869736ae3b0de3e56ceaca3958/free-nature-images.jpg?fit=fill&w=1200&h=630",
	"https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTUsbmTZu_uMrmJ0z--CrG-o1UIXytu1OCizQ&s",
	"https://media.istockphoto.com/id/1280385511/photo/colorful-background.jpg?s=612x612&w=0&k=20&c=kj0PRQlgvWLzA1-1me6iZp5mlwsZhC4QlcvIEb1J1bs="
  ];

export type TInteractionValue = "close" | "open-url" | "Previous" | "Next" | "Restart";
export type TButtonAction = {
	value: TInteractionValue;
	targetURL: string;
	tab: "same-tab" | "new-tab";
	interaction?: any;
};
export type TButton = {
	id: string;
	name: string;
	position: "start" | "end" | "center";
	type: "primary" | "secondary";
	isEditing: boolean;
	index: number;
	style: React.CSSProperties;
	actions?: TButtonAction;
	survey?: null;
};

export type ButtonContainer = {
	id: string;
	buttons: TButton[];
	style: React.CSSProperties;
	BackgroundColor?: string; // Add BackgroundColor property
};
type TSettingAnchorEl = {
	containerId: string;
	buttonId: string;
	value: null;
};
type TSettingAnchorElNew = {
	rteId: string;
	containerId: string;
	value: null;
};
type TImages = {
	id: string;
	url: any;
	altText: string;
	backgroundColor: string;
	objectFit: "cover" | "contain";
};
export type TImageContainer = {
	id: string;
	images: TImages[];
	style: React.CSSProperties;
	hyperlink?: string;
};
type TRTE = {
	id: string;
	text: string;
};
export const CANVAS_DEFAULT_VALUE: TCanvas = {
	position: "absolute",
	autoposition: false,
	xaxis: "1px",
	yaxis: "1px",
	width: "300px",
	padding: "2px",
	borderRadius: "8px",
	borderSize: "0px",
	borderColor: "transparent",
	backgroundColor: "#ffffff",
};
export const CANVAS_DEFAULT_VALUE_HOTSPOT: HCanvas = {
	position: "absolute",
	autoposition: false,
	xaxis: "100px",
	yaxis: "100px",
	width: "300px",
	padding: "12px",
	borderRadius: "8px",
	borderSize: "0px",
	borderColor: "transparent",
	backgroundColor: "#ffffff",
};
export const CANVAS_DEFAULT_VALUE_CHECKLIST: CCanvas = {
	width: "930",
	height: "450",
	cornerRadius: "12",
	primaryColor: "#5F9EA0",
	borderColor: "",
	backgroundColor: "#ffffff",
	openByDefault: false,
	hideAfterCompletion: true,
	borderWidth: "0",
	xaxis: "10",
	yaxis: "10",
};
export const LAUNCHER_DEFAULT_VALUE_CHECKLIST: CLauncher = {
	type: "Icon",
	icon: "",
	text: "Get Started",
	iconColor: "#fff",
	textColor: "#fff",
	launcherColor: "#5F9EA0",
	launcherposition: {
		left: false,
		right: true,
		xaxisOffset: "10",
		yaxisOffset: "10",
	},
	notificationBadge: false,
	notificationBadgeColor: "red",
	notificationTextColor: "#fff",
};
export const TITLESUBTITLE_DEFAULT_VALUE_CHECKLIST: CTitleSubTitle = {
	title: "Checklist Title",
	titleColor: "#333",
	titleBold: true,
	titleItalic: false,
	subTitle: "Context about the tasks in the checklist below users should prioritize completing.",
	subTitleColor: "#8D8D8D",
	subTitleBold: false,
	subTitleItalic: false,
};

export const CHEKPOINTITEM_DEFAULT_VALUE_CHECKLIST: CCheckPointItem = {
	id: crypto.randomUUID(),
	interaction: "111111111111",
	title: "aaaaaaaaa",
	description: "string",
	redirectURL: "url",
	icon: "Dadasd",
	supportingMedia: "dasdsa",
	mediaTitle: "mediati",
	mediaDescription: "mediadesc",
};

export const CHECKPOINT_DEFAULT_VALUE_CHECKLIST: CCheckPoint = {
	checkpointsList: [],
	checkpointTitles: "#333",
	checkpointsDescription: "#8D8D8D",
	checkpointsIcons: "#333",
	unlockCHeckpointInOrder: false,
	message: "complete items in order",
};
export const CANVAS_DEFAULT_VALUE_Banner: BCanvas = {
	position: "Cover Top",
	padding: "10",
	borderSize: "0",
	borderColor: "transparent",
	backgroundColor: "#f1f1f7",
};
export const HOTSPOT_DEFAULT_VALUE: Hotspots = {
	XPosition: "4",
	YPosition: "4",
	Type: "Question",
	Color: "yellow",
	Size: 16,
	PulseAnimation: true,
	stopAnimationUponInteraction: true,
	ShowUpon: "Hovering Hotspot",
	ShowByDefault: false,
};
export type Hotspots = {
	XPosition: string;
	YPosition: string;
	Type: string;
	Color: string;
	Size: string;
	PulseAnimation: boolean;
	stopAnimationUponInteraction: boolean;
	ShowUpon: string;
	ShowByDefault: boolean;
};
// export const Element_DEFAULT_VALUE: TElement = {};
// export type TElement = {
// 	position: string;
// 	autoposition: boolean;
// 	xaxis: string;
// 	yaxis: string;
// 	width: string;
// 	padding: string;
// 	borderradius: string;
// 	bordersize: string;
// 	bordercolor: string;
// 	backgroundcolor: string;
// };
export type TDesignelementclick = {
	ButtonId: string;
	ElementPath: string;
	NextStep: string;
	ButtonName: string;
};
export type progressclick = {
	progress: string;
	isDismiss: boolean;
	progressSelectedOption?: number;
	progressColor?: string;
};

export type TCanvas = {
	position: string;
	autoposition: boolean;
	xaxis: string;
	yaxis: string;
	width: string;
	padding: string;
	borderRadius: string;
	borderSize: string;
	borderColor: string;
	backgroundColor: string;
};
export type CCanvas = {
	height: string;
	width: string;
	cornerRadius: string;
	primaryColor: string;
	borderColor: string;
	backgroundColor: string;
	openByDefault: boolean;
	hideAfterCompletion: boolean;
	borderSize: string;
	xaxis?: string;
	yaxis?: string;
};
export type CLauncher = {
	type: any;
	icon: any;
	text: string;
	iconColor: string;
	textColor: string;
	launcherColor: string;
	launcherposition: {
		left: boolean;
		right: boolean;
		xaxisOffset: any;
		yaxisOffset: any;
	};
	notificationBadge: boolean;
	notificationBadgeColor: string;
	notificationTextColor: string;

};
export type CTitleSubTitle = {
	title: string;
	titleColor: string;
	titleBold: boolean;
	titleItalic: boolean;
	subTitle: string;
	subTitleColor: string;
	subTitleBold: boolean;
	subTitleItalic: boolean;

};

export type CCheckPointItem = {
	id: string;
	interaction: string;
	title: string;
	description: string;
	redirectURL: string;
	icon: any;
	supportingMedia: CSupporitngMedia;
	mediaTitle: string;
	mediaDescription: string;

};
export type CCheckPointList = CCheckPointItem[];

export type CCheckPoint = {
	checkpointsList: CCheckPointList;
	checkpointTitles: string;
	checkpointsDescription: string;
	checkpointsIcons: string;
	unlockCHeckpointInOrder: boolean;
	message: string;

};
export type BCanvas = {
	position: string;
	padding: string;
	borderSize: string;
	borderColor: string;
	backgroundColor: string;
};

export type HCanvas = {
	position: string;
	autoposition: boolean;
	xaxis: string;
	yaxis: string;
	width: string;
	padding: string;
	borderradius: string;
	bordersize: string;
	bordercolor: string;
	backgroundcolor: string;
};
type TRTEContainer = {
	id: string;
	rtes: TRTE[];
	style: React.CSSProperties;
};
interface DismissData {
	Actions: string | number | null;
	DisplayType: string;
	Color: string;
	DontShowAgain: boolean;
	dismisssel: boolean;
}
export interface Steps {
	id: string;
	name: string;
	stepCount: number;
	stepType: string;
	stepDescription: string;
}
export interface AnnouncementGuideMetaData {
	[x: string]: any;
	id: string;
	containers: TToolTipGuideContainer[];
	currentStep: number;
	stepName: string;
	stepDescription: string;
	stepType: string;
	stepTargetURL?: string;
	xpath: { value: string; position: { x: number; y: number } };
	canvas: TCanvas;
	hotspots: Hotspots;
	design: {
		gotoNext: TDesignelementclick;
		element: progressclick;
	};
}
export interface TooltipState {
	visible: boolean;
	text: string;
	position: {
		x: number;
		y: number;
	};
}
export interface HotspotState {
	visible: boolean;
	text: string;
	position: {
		x: number;
		y: number;
	};
}
interface StepData {
	id: string; // ID of the step
	imagesContainer: any[]; // Images container data for this step
	rtesContainer: any[]; // RTE container data for this step
	buttonsContainer: any[]; // Buttons container data for this step
}
export const IMG_CONTAINER_MIN_HEIGHT = 85;
export const IMG_CONTAINER_DEFAULT_HEIGHT = 170;
export const IMG_CONTAINER_MAX_HEIGHT = 210;
export const IMG_EXPONENT = 1;
export const IMG_STEP_VALUE = 25;
export const IMG_OBJECT_FIT = "contain";
const getRandomID = () => crypto.randomUUID();
export const BUTTON_DEFAULT_VALUE: TButton = {
	id: crypto.randomUUID(),
	name: "Got It",
	position: "center",
	type: "primary",
	isEditing: false,
	index: 0,
	style: {
		backgroundColor: "#5F9EA0",
		borderColor: "#70afaf",
		color: "#ffffff",
	},
	actions: {
		value: "close",
		targetURL: "",
		tab: "new-tab",
		interaction: null,
	},
	survey: null,
};
export const RTE_CONT_DEF_VALUE = {
	id: crypto.randomUUID(),
	rtes: [
		{
			id: crypto.randomUUID(), // Ensure each RTE has a unique ID
			text: "", // Initialize text if needed
		},
	],
	style: {
		backgroundColor: "#f0f0f0",
	},
};
export const IMG_CONT_DEF_VALUE = {
	id: crypto.randomUUID(),
	images: [],
	style: {
		backgroundColor: "transparent",
		height: IMG_CONTAINER_DEFAULT_HEIGHT,
	},
	hyperlink: undefined,
};
export const RTE_DEF_CONT_VALUE = {
	id: crypto.randomUUID(),
	textBoxRef: null,
	style: {
		backgroundColor: "transparent",
	},
};

export const BUTTON_CONT_DEF_VALUE_1: ButtonContainer = {
	id: crypto.randomUUID(),
	buttons: [BUTTON_DEFAULT_VALUE],

	style: {
		backgroundColor: "transparent",
	},
};

export const BUTTON_CONT_DEF_VALUE: ButtonContainer[] = [
	{
		id: crypto.randomUUID(),
		buttons: [
			{
				id: crypto.randomUUID(),
				name: "Got It",
				position: "center",
				type: "primary",
				isEditing: false,
				index: 0,
				style: {
					backgroundColor: "#5F9EA0",
					borderColor: "#70afaf",
					color: "#ffffff",
				},

				survey: null,
			},
		],
		style: {
			backgroundColor: "transparent",
		},
	},
];


export const DEF_XPATH = { value: "", PossibleElementPath: "", position: { x: 0, y: 0 } };
export const DEF_PLACEHOLDER_MSG = "Start typing here...";
export interface IRTEContainer {
	id: string;
	rteBoxValue: "";
	placeholder: DEF_PLACEHOLDER_MSG;
	style: React.CSSProperties;
}

type TToolTipGuideContainer =
	| (ButtonContainer & { type: "button" })
	| (IRTEContainer & { type: "rte" })
	| (TImageContainer & { type: "image" });

type THotspotGuideContainer =
	| (ButtonContainer & { type: "button" })
	| (IRTEContainer & { type: "rte" })
	| (TImageContainer & { type: "image" });


interface TChecklistGuideMetaData {
	id: string;
	checkpoints: CCheckPoint;
	TitleSubTitle: CTitleSubTitle;
	canvas: CCanvas;
	launcher: CLauncher;
}
interface TToolTipGuideMetaData {
	id: string;
	containers: TToolTipGuideContainer[];
	currentStep: number;
	stepName: string;
	stepDescription: string;
	stepType: string;
	stepId: string;
	stepTargetURL?: string;
	xpath: { value: string; PossibleElementPath: string; position: { x: number; y: number } };
	canvas: TCanvas;
	hotspots: Hotspots;
	design: {
		gotoNext: TDesignelementclick;
		element: progressclick;
	};
	// design: {
	// 	canvas: any;
	// 	elements: any;
	// 	overlay: any;
	// 	animation:any
	// };
}

// videoContainer: any;
// gifContainer: any;
// htmlContainer: any;
export interface HotspotContainer {
	id: string;
	hotspots: Hotspot[];
	style: React.CSSProperties;
}
interface THotspotGuideMetaData {
	id: string;
	containers: THotspotGuideContainer[];
	xpath: { value: string; position: { x: number; y: number } };
	canvas: HCanvas;
	hotspots: Hotspots;
}
interface HotspotPosition {
	XOffset: string;
	YOffset: string;
}

interface Hotspot {
	HotspotPosition: HotspotPosition;
	Type: string;
	Color: string;
	Size: string;
	PulseAnimation: boolean;
	StopAnimation: boolean;
	ShowUpon: string;
	ShowByDefault: boolean;
}

export type TSectionType = "image" | "button" | "rte" | "video" | "gif" | "html";
// Maximum allowed sections of each type
export const MAX_SECTIONS = {
	image: 3,
	button: 3,
	rte: 3,
};
export interface DrawerState {
	resetElementStyles: () => void;
	hasReachedLimit: (type: TSectionType) => boolean;
	// Batch update function to record multiple changes as a single history entry
	batchUpdate: (updateFn: () => void, actionType: string, description: string, targetId?: string, metadata?: any) => void;
	announcementJson: {
		GuideStep: Record<string, unknown>[];
	};
	bannerJson: {
		GuideStep: Record<string, unknown>[];
	};

	textArray: unknown[];
	setTextArray: (newArray: unknown[]) => void;

	cleanupDuplicateSteps: () => void;
	scrapedData: any; // Add appropriate type instead of 'any'
    setScrapedData: (data: any) => void;
    isScrapingActive: boolean;
    setIsScrapingActive: (isActive: boolean) => void;

	/* Checklist */
	titleColor: string;
	setTitleColor: (textColor: string) => void;
	iconColor: string;
	setIconColor: (iconColor: string) => void;
	launcherColor: string;
	setLauncherColor: (launcherColor: string) => void;
	setChecklistCanvasSetting: (params: Record<string, unknown>) => void;
	checkpointsPopup: boolean;
	setCheckPointsPopup: (checkpointsPopup) => void;
	checkpointsEditPopup: boolean;
	setCheckPointsEditPopup: (checkpointsEditPopup) => void;
	checkpointsAddPopup: boolean;
	setCheckPointsAddPopup: (checkpointsAddPopup: boolean) => void;
	checkpointTitleColor: string;
	setCheckpointTitleColor: (checkpointTitleColor: string) => void;
	checkpointTitleDescription: string;
	setCheckpointTitleDescription: (checkpointTitleDescription: string) => void;
	checkpointIconColor: string;
	setCheckpointIconColor: (checkpointIconColor: string) => void;
	unlockCheckPointInOrder: boolean;
	setUnlockCheckPointInOrder: (unlockCheckPointInOrder: string) => void;
	checkPointMessage: string;
	setCheckPointMessage: (checkPointMessage: string) => void;

	checklistTitle: string;
	setChecklistTitle: (checklistTitle: string) => void;
	checklistSubTitle: string;
	setChecklistSubTitle: (checklistSubTitle: string) => void;

	/* Checklist */

	hotspotWidth: string;
	setHotspotWidth: (hotspotWidth: string) => void;
	hotspotHeight: string;
	setHotspotHeight: (hotspotHeight: string) => void;
	hotspotBackgroundColor: string;
	setHotspotBackgroundColor: (hotspotBackgroundColor: string) => void;
	hotspotBorderRadius: string;
	setHotspotBorderRadius: (hotspotBorderRadius: string) => void;
	hotspotPosition: string;
	setHotspotPosition: (hotspotPosition: string) => void;
	hotspotOpacity: string;
	setHotspotOpacity: (hotspotOpacity: string) => void;
	hotspotBorderColor: string;
	setHotspotBorderColor: (hotspotBorderColor: string) => void;
	hotspotSize: string;
	setHotspotSize: (hotspotBorderColor: string) => void;
	btnidss: string;
	setbtnidss: (btnidss: string) => void;
	designPopup: boolean;
	isCollapsed: boolean;
	setIsCollapsed: (params: boolean) => void;
	isGuideInfoScreen: boolean;
	setIsGuideInfoScreen: (params: boolean) => void;
	setDesignPopup: (params: boolean) => void;
	bannerPopup: boolean;
	setBannerPopup: (params: boolean) => void;
	hotspotPopup: boolean;
	setHotspotPopup: (hotspotPopup: boolean) => void;
	titlePopup: boolean;
	setTitlePopup: (titlePopup: boolean) => void;
	autoPosition: boolean;
	setAutoPosition: (autoPosition: boolean) => void;
	loading: boolean;
	setLoading: (loading: boolean) => void;
	setAnnouncementJson: (params: Record<string, unknown>) => void;
	setBannerJson: (params: Record<string, unknown>) => void;
	setBannerCanvasSetting: (params: Record<string, unknown>) => void;
	setCanvasSetting: (params: Record<string, unknown>) => void;
	setHotspotSetting: (params: Record<string, unknown>) => void;
	hotspbgcolor: string;
	setHotspBgColor: (hotspbgcolor: string) => void;
	tcanvassettings: TCanvas[];
	hotspots: Hotspots[];
	isSaveClicked: boolean;
	setIsSaveClicked: (isSaveClicked: boolean) => void;
	cloneButtonContainer: (params: string) => void;
	deleteButtonContainer: (params: string) => void;
	updateButton: (containerId: string, buttonId: string, keyname: keyof TButton, value: TButton[keyof TButton]) => void;
	updateButtonInTooltip: (
		containerId: string,
		buttonId: string,
		keyname: keyof TButton,
		value: TButton[keyof TButton]
	) => void;
	progress: boolean;
	setProgress: (progress: boolean) => void;
	dropdownValue: string;
	setDropdownValue: (dropdownValue: string) => void;
	editClicked: boolean;
	setEditClicked: (editClicked: boolean) => void;
	pulseAnimationsH: boolean;
	setPulseAnimationsH: (pulseAnimationsH: boolean) => void;
	dismiss: boolean;
	setDismiss: (dismiss: boolean) => void;
	updateCanvasInTooltip: (value: TCanvas) => void;
	updateDesignelementInTooltip: (value: any) => void;
	updateprogressclick: (value: progressclick) => void;
	updatehotspots: (value: Hotspots) => void;

	updateContainer: (containerId: string, keyname: keyof TButton | keyof ButtonContainer, value: any) => void;
	updateTooltipBtnContainer: (containerId: string, keyname: keyof TButton | keyof ButtonContainer, value: any) => void;
	ziindex: boolean;
	selectedActions: string;
	setSelectActions: (selectedActions: string) => void;
	isPopoverOpen: boolean;
	setIsPopoverOpen: (params: boolean) => void;
	open: boolean;
	setOpen: (params: boolean) => void;
	isTooltipPopup: boolean;
	setIsTooltipPopup: (isTooltipPopup: boolean) => void;
	elementbuttonClick: boolean;
	SetElementButtonClick: (elementbuttonClick: boolean) => void;
	buttonClick: boolean;
	setButtonClick: (buttonClick: boolean) => void;
	elementSelected: boolean;
	stepData: StepData[];
	setElementSelected: (isTooltipPopup: boolean) => void;
	targetURL: string;
	setTargetURL: (targetURL: string) => void;
	ButtonsDropdown: string;
	setButtonsDropdown: (ButtonsDropdown: string) => void;
	selectedInteraction: string;
	setSelectedInteraction: (selectedselectedInteractionActions: string) => void;
	openInteractionList: boolean;
	announcementGuideMetaData: AnnouncementGuideMetaData[];
	setOpenInteractionList: (params: boolean) => void;
	pageinteraction: boolean;
	setPageInteraction: (pageinteraction: boolean, skipMutualExclusivity?: boolean) => void;
	btnBgColor: string;
	setBtnBgColor: (btnBgColor: string) => void;
	btnBorderColor: string;
	setBtnBorderColor: (btnBorderColor: string) => void;
	newCurrentStep: number;
	setNewCurrentStep: (newCurrentStep: number) => void;
	btnTextColor: string;
	setBtnTextColor: (btnTextColor: string) => void;
	selectedTab: string;
	setSelectedTab: (selectedTab: string) => void;
	currentButtonName: string;
	cuntainerId: string;
	setCuntainerId: (cuntainerId: string) => void;
	buttonId: string;
	setButtonId: (cuntainerId: string) => void;
	openWarning: boolean;
	setOpenWarning: (params: boolean) => void;
	setCurrentButtonName: (currentButtonName: string) => void;
	setZiindex: (params: boolean) => void;
	currentStep: number;
	setCurrentStep: (currentStep: number) => void;
	isPlaceholderVisible: boolean;
	setIsPlaceholderVisible: (params: boolean) => void;
	buttonProperty: boolean;
	setButtonProperty: (params: boolean) => void;
	setBannerButtonSelected: (params: boolean) => void;
	bannerButtonSelected: boolean;
	buttonsContainer: ButtonContainer[];
	addNewButton: (button: TButton, containerId: string) => void;
	addNewButtonInTooltip: (containerId: string) => void;
	deleteTooltipButtonContainer: (containerId: string) => void;
	cloneTooltipButtonContainer: (containerId: string) => void;
	deleteButton: (containerId: string, buttonId: string) => void;
	deleteButtonInTooltip: (buttonId: string, containerId: string) => void;
	selectedOption: string | number | null;
	setSelectedOption: (selectedOption: string | number | null) => void;
	selectedTemplate: string;
	setSelectedTemplate: (template: string, skipOverlayReset?: boolean) => void;
	selectedStepTypeHotspot: any;
	setSelectedStepTypeHotspot: (selectedStepTypeHotspot: any) => void;
	announcementPreview: boolean;
	setAnnouncementPreview: (announcementPreview: boolean) => void;

	bannerPreview: boolean;
	setBannerPreview: (bannerPreview: boolean) => void;

	tooltipPreview: boolean;
	setTooltipPreview: (tooltipPreview: boolean) => void;

	hotspotPreview: boolean;
	setHotspotPreview: (hotspotPreview: boolean) => void;

	selectedTemplateTour: string;
	setSelectedTemplateTour: (selectedTemplateTour: string, skipOverlayReset?: boolean) => void;
	tooltipCount: number;
	setTooltipCount: (tooltipCount: number) => void;
	tooltipYaxis: string;
	setTooltipYaxis: (tooltipYaxis: string) => void;
	tooltipWidth: string;
	setTooltipWidth: (tooltipWidth: string) => void;
	elementButtonName: string;
	setElementButtonName: (elementButtonName: string) => void;
	tooltippadding: string;
	setTooltipPadding: (tooltippadding: string) => void;
	tooltipborderradius: string;
	setTooltipBorderradius: (tooltipborderradius: string) => void;
	tooltipbordersize: string;
	setTooltipBordersize: (tooltipbordersize: string) => void;
	tooltipXaxis: string;
	setTooltipXaxis: (tooltipXaxis: string) => void;
	tooltipPosition: string;
	setTooltipPosition: (tooltipPosition: string) => void;
	tooltipBordercolor: string;
	setTooltipBordercolor: (tooltipBordercolor: string) => void;
	tooltipBackgroundcolor: string;
	setTooltipBackgroundcolor: (tooltipBackgroundcolor: string) => void;
	hotspotXaxis: string;
	setHotspotXaxis: (template: string) => void;
	currentStepIndex: number;
	setCurrentStepIndex: (currentStepIndex: number) => void;
	position: string;
	setPosition: (position: string) => void;
	padding: string;
	setPadding: (padding: string) => void;
	Annpadding: string;
	setAnnPadding: (Annpadding: string) => void;
	radius: string;
	setRadius: (radius: string) => void;
	borderSize: number;
	setBorderSize: (bordersize: number) => void;
	AnnborderSize: number;
	setAnnBorderSize: (Annbordersize: number) => void;
	borderRadius: number;
	setBorderRadius: (borderRadius: number) => void;
	setBorderColor: (bordercolor: string) => void;
	borderColor: string;
	setBackgroundColor: (backgroundcolor: string) => void;
	backgroundColor: string;
	setWidth: (width: number) => void;
	width: number;
	textvaluess: string;
	setTextvaluess: (textvaluess: string) => void;
	btnname: string;
	setBtnName: (btnname: string) => void;
	fit: string;
	setFit: (fit: string) => void;
	fill: string;
	setFill: (fill: string) => void;
	sectionHeight: string;
	setSectionHeight: (sectionHeight: string) => void;
	guideName: string;
	SetGuideName: (guideName: string) => void;
	tempGuideName: string;
	setTempGuideName: (tempGuideName: string) => void;
	setImageSrc: (imageSrc: string) => void;
	imageSrc: string;
	textBoxRef: string;
	backgroundC: string;
	setBackgroundC: (htmlContent: string) => void;
	sectionColor: string;
	setSectionColor: (sectionColor: string) => void;
	Bposition: string;
	setBposition: (htmlContent: string) => void;
	bpadding: string;
	setbPadding: (htmlContent: string) => void;
	Bbordercolor: string;
	setBBorderColor: (htmlContent: string) => void;
	ProgressColor: string;
	setProgressColor: (htmlContent: string) => void;
	BborderSize: string;
	highlightedButton: string;
	setHighlightedButton: (highlightedButton: string) => void;
	setBBorderSize: (htmlContent: string) => void;
	zindeex: number;
	setZindeex: (htmlContent: number) => void;
	setHtmlContent: (htmlContent: string) => void;
	htmlContent: string;
	buttonColor: string;
	showTooltipCanvasSettings: boolean;
	setShowTooltipCanvasSettings: (showTooltipCanvasSettings: boolean) => void;
	showLauncherSettings: boolean;
	setShowLauncherSettings: (showLauncherSettings: boolean) => void;
	elementClick: string;
	setElementClick: (elementClick: string) => void;
	preview: boolean;
	setPreview: (preview: boolean) => void;
	setButtonColor: (buttonColor: string) => void;
	setImageName: (imageName: string) => void;
	imageName: string;
	alignment: string;
	setAlignment: (alignment: string) => void;
	textvalue: string;
	setTextvalue: (textvalue: string) => void;
	htmlCode: string;
	setHtmlCode: (htmlcode: string) => void;
	guidedatas: string;
	setGuideDataS: (guidedatas: string) => void;
	settingAnchorEl: TSettingAnchorEl;
	tooltipBtnSettingAnchorEl: TSettingAnchorEl;
	setSettingAnchorEl: (params: TSettingAnchorEl) => void;
	settingAnchorElNew: TSettingAnchorElNew;
	setSettingAnchorElNew: (params: TSettingAnchorElNew) => void;
	setTooltipBtnSettingAnchorEl: (params: TSettingAnchorEl) => void;
	imageAnchorEl: TSettingAnchorEl;
	setImageAnchorEl: (params: TSettingAnchorEl) => void;
	rteAnchorEl: TSettingAnchorElNew;
	setRTEAnchorEl: (params: TSettingAnchorElNew) => void;
	// Global state to trigger closing all button popups
	closeAllButtonPopups: number;
	triggerCloseAllButtonPopups: () => void;
	axisData: any;
	setAxisData: (params: axisData) => void;
	updateButtonAction: (containerId: string, buttonId: string, value: TButtonAction[keyof TButtonAction]) => void;
	updateTooltipButtonAction: (containerId: string, buttonId: string, value: TButtonAction[keyof TButtonAction]) => void;
	updateButtonInteraction: (containerId: string, buttonId: string, value: TButtonAction[keyof TButtonAction]) => void;
	updateTooltipButtonInteraction: (
		containerId: string,
		buttonId: string,
		value: TButtonAction[keyof TButtonAction]
	) => void;
	getCurrentButtonInfo: (
		containerId: string,
		buttonId: string
	) => {
		bgColor: string;
		textColor: string;
		borderColor: string;
		title: string;
		selectedActions: TInteractionValue;
		targetURL: string;
		value: string;
		tab: "same-tab" | "new-tab";
	};
	guideListByOrg: any[];
	getGuildeListByOrg: (orgId: string) => Promise<void>;
	imagesContainer: TImageContainer[];
	rtesContainer: TRTEContainer[];
	cloneRTEContainer: (params: string) => void;
	deleteRTEContainer: (params: string) => void;
	cloneImageContainer: (params: string) => void;
	cloneTooltipImage: (params: string) => void;
	deleteImageContainer: (params: string) => void;
	deleteTooltipImageContainer: (params: string) => void;
	storedTransform: string;
	setStoredTransform: (storedTransform: string) => void;
	uploadImage: (containerId: string, value: TImages) => void;
	uploadTooltipImage: (containerId: string, value: TImages) => void;
	replaceImage: (containerId: string, imageId: string, value: TImages[keyof TImages]) => void;
	replaceTooltipImage: (containerId: string, value: TImages[keyof TImages]) => void;
	addNewImageContainer: () => void;
	addNewRTEContainer: () => void;
	updateImageContainerOnReload: (params: any[], mode?: "submit" | "reset") => void;
	updateRTEContainerOnReload: (params: any[], mode?: "submit" | "reset") => void;
	updateButtonContainerOnReload: (params: any[], mode?: "submit" | "reset") => void;
	updateImageContainer: (
		containerId: string,
		keyname: keyof TImageContainer,
		value: TImageContainer[keyof TImageContainer]
	) => void;
	updateTooltipImageContainer: (
		containerId: string,
		keyname: keyof TImageContainer,
		value: TImageContainer[keyof TImageContainer]
	) => void;
	updateRTEContainer: (containerId: string, rteId: string, newText: string) => void;

	toggleFit: (containerId: string, imageId: string, mode: "Fit" | "Fill") => void;
	toggleTooltipImageFit: (containerId: string, mode: "Fit" | "Fill") => void;
	dismissData: DismissData | null;
	setDismissData: (data: DismissData) => void;
	createWithAI: boolean;
	setCreateWithAI: (createWithAI: boolean) => void;
	isAIGuidePersisted: boolean;
	setIsAIGuidePersisted: (persisted: boolean) => void;
	interactionData: any;
	setInteractionData: (data: any) => void;
	overlayEnabled: boolean;
	setOverlayEnabled: (enabled: boolean, skipMutualExclusivity?: boolean) => void;
	isUnSavedChanges: false;
	setIsUnSavedChanges: (isUnSavedChanges: boolean) => void;
	isReturningFromPreview: boolean;
	setIsReturningFromPreview: (isReturning: boolean) => void;
	isNavigatingBetweenSteps: boolean;
	setIsNavigatingBetweenSteps: (isNavigating: boolean) => void;
	clearGuideDetails: () => void;
	clearRteDetails: (containerId: string, rteId: string) => void;
	TooltipGuideDetails: () => void;
	HotspotGuideDetails: () => void;
	TooltipGuideDetailsNew: () => void;
	HotspotGuideDetailsNew: () => void;
	clearBannerButtonDetials: () => void;
	resetBannerCanvasToDefaults: () => void;
	steps: Steps[];
	setSteps: (steps: Steps[]) => void;
	createNewStep: (title: string, type: string, description: string) => void;
	createNewAnnouncementStep: (title: string, type: string, description: string) => void;
	generateSteps: (guideSteps: any[]) => void;
	renameStep: (id: string, title: string, desc: string) => void;
	deleteStep: (id: string) => void;
	deleteCheckpoint: (interaction: string) => void;
	changeCurrentStep: (id: string, type: string) => void;
	tooltip: TooltipState;
	setTooltip: (params: TooltipState) => void;
	checkPointMetaData: TCheckPointMetaData[];
	toolTipGuideMetaData: TToolTipGuideMetaData[];
	checklistGuideMetaData: TChecklistGuideMetaData[];
	hotspotGuideMetaData: THotspotGuideMetaData[];
	createTooltipSections: (sectionType: TSectionType, index: number) => void;
	handleTooltipRTEBlur: (containerId: string) => void;
	handleTooltipRTEValue: (containerId: string, value: string) => void;
	handleAnnouncementRTEBlur: (containerId: string) => void;
	handleAnnouncementRTEValue: (containerId: string, value: string) => void;
	ensureAnnouncementRTEContainer: (stepIndex: number, isTourAnnouncement: boolean) => any;
	ensureAnnouncementButtonContainer: (stepIndex: number, isTourAnnouncement: boolean) => any;
	handleRTEDeleteSection: (containerId: string) => void;
	handleRTECloneSection: (containerId: string) => void;
	setTooltipDataOnEdit: (data: any) => void;
	setChecklistDataOnEdit: (data: any) => void;
	setAnnouncementDataOnEdit: (data: any) => void;
	setTourDataOnEdit: (data: any, type: any) => void;
	setHotspotDataOnEdit: (data: any) => void;
	setXpathToTooltipMetaData: (xpath: {
		value: string;
		PossibleElementPath: string;
		position: { x: number; y: number };
	}) => void;
	setOpenTooltip: (params: boolean) => void;
	setTooltipPositionByXpath: (params: any) => void;
	setTooltipElementOptions: (
		keyname: string,
		selectedTemplate: string,
		selectedTemplateTour: string,
		option: string
	) => void;
	createNewTooltipSteps: (stepName: string, type: string, description: string) => void;
	updateStepNameInTooltip: (stepId: string, stepName: string) => void;
	openTooltip: boolean;
	isALTKeywordEnabled: boolean;
	setIsALTKeywordEnabled: (params: boolean) => void;
	resetTooltipMetaData: () => void;
	syncAITooltipDataForPreview: () => void;
	syncAITooltipContainerData: () => void;
	restoreTooltipElementClickState: () => void;
	restoreManualTooltipElementClickState: () => void;
	syncAIAnnouncementDataForPreview: (preserveGlobalState?: boolean) => void;
	syncAIAnnouncementContainerData: () => void;
	syncAnnouncementJsonWithMetaData: () => void;
	initializeAITourTooltipMetadata: () => void;
	syncAIAnnouncementCanvasSettings: (canvasData: any) => void;
	syncButtonContainerToMetadata: () => void;
	syncMetadataToButtonContainer: () => void;
	syncMetadataToSavedGuideData: () => void;
	forceSaveButtonConfiguration: () => void;
	syncAITourDataForPreview: () => void;
	syncCurrentStepDataForAITour: () => void;
	restoreAITourRTEData: () => void;
	syncGlobalProgressBarStateForAITour: () => void;
	restoreAITourProgressBarState: () => void;
	syncGlobalOverlayStateForAnnouncements: () => void;
	currentHoveredElement: HTMLElement | null;
	setCurrentHoveredElement: (params: HTMLElement) => void;
	activeMenu: string | null;
	searchText: string;
	setActiveMenu: (menuId: string | null) => void;
	setSearchText: (text: string) => void;
	isExtensionClosed: boolean;
	setIsExtensionClosed: (isExtensionClosed: boolean) => void;

	// Undo/Redo functionality
	canUndo: () => boolean;
	canRedo: () => boolean;
	undo: () => void;
	redo: () => void;
}

const useDrawerStore = create<DrawerState>()(
	devtools(
		persist(
			immer((set, get) => ({
				// Global state to trigger closing all button popups
				closeAllButtonPopups: 0,
				triggerCloseAllButtonPopups: () => {
					set((state) => {
						state.closeAllButtonPopups = Date.now();
					});
				},
				resetElementStyles: () => {
					// Get the targetElement from the state if available
					const targetElement = get().currentHoveredElement;

					// Clear the specific targetElement if it exists
					if (targetElement) {
						//targetElement.style.backgroundColor = "";
						targetElement.style.outline = "";
						targetElement.style.boxShadow = "";
						targetElement.style.pointerEvents = "";
						targetElement.removeAttribute("data-target-element");
					}

					// Also clear any elements with the quickAdopt-selection class
					const selectedElements = document.querySelectorAll(".quickAdopt-selection");

					selectedElements.forEach((element) => {
						if (element instanceof HTMLElement) {
							//element.style.backgroundColor = "";
							element.style.outline = "";
							element.style.boxShadow = "";
							element.style.pointerEvents = "";
							element.classList.remove("quickAdopt-selection");
						}
					});
				},

				// Check if a section type has reached its limit
				hasReachedLimit: (type: TSectionType) => {
					const state = get();
					const currentStepIndex = state.currentStep - 1;

					// Get containers for the current step
					const containers = state.toolTipGuideMetaData[currentStepIndex]?.containers || [];

					// Count sections of the specified type
					const count = containers.filter(container => container.type === type).length;

					// Check if count has reached the limit
					return count >= MAX_SECTIONS[type];
				},
				isUnSavedChanges: false,
				isReturningFromPreview: false,
				setIsReturningFromPreview: (isReturning) => {
					set((state) => {
						state.isReturningFromPreview = isReturning;
					});
				},
				isNavigatingBetweenSteps: false,
				setIsNavigatingBetweenSteps: (isNavigating) => {
					set((state) => {
						state.isNavigatingBetweenSteps = isNavigating;
					});
				},
				unsavedChangesBeforePreview: false,
				setUnsavedChangesBeforePreview: (value) => {
					set((state) => {
						state.unsavedChangesBeforePreview = value;
					});
				},
				getUnsavedChangesBeforePreview: () => {
					return get().unsavedChangesBeforePreview;
				},
				isALTKeywordEnabled: true,
				currentStep: 1,
				announcementJson: {
					GuideStep: [],
				},
				checklistJson: {
					GuideStep: [],
				},
				bannerJson: {
					GuideStep: [],
				},
				selectedTemplate: "",

				stepData: [],
				steps: [
					{
						id: crypto.randomUUID(),
						name: "Step 1",
						stepCount: 1,
						stepType: "", //removed step type as it is causing tooltip issue
						stepDescription: "",
					},
				],
				currentHoveredElement: null,
				guideListByOrg: [],
				designPopup: false,

				// Undo/Redo functionality
				canUndo: () => {
					return useHistoryStore.getState().canUndo();
				},
				canRedo: () => {
					return useHistoryStore.getState().canRedo();
				},
				undo: () => {
					const historyStore = useHistoryStore.getState();
					const entry = historyStore.undo();

					if (entry) {
						// Apply the previous state
						applyHistoryEntry(entry, true);
					}
				},
				redo: () => {
					const historyStore = useHistoryStore.getState();
					const entry = historyStore.redo();

					if (entry) {
						// Apply the next state
						applyHistoryEntry(entry, false);
					}
				},
				// Batch update function to record multiple changes as a single history entry
				batchUpdate: (updateFn, actionType, description, targetId, metadata) => {
					const state = get();

					// Store the state before any changes
					const stateBefore = {
						overlayEnabled: state.overlayEnabled,
						pageinteraction: state.pageinteraction,
						dismiss: state.dismiss,
						dismissData: deepClone(state.dismissData || {}),
						progress: state.progress,
						selectedOption: state.selectedOption,
						progressColor: state.ProgressColor
					};

					// Apply the updates
					updateFn();

					// Get the updated state
					const updatedState = get();
					const stateAfter = {
						overlayEnabled: updatedState.overlayEnabled,
						pageinteraction: updatedState.pageinteraction,
						dismiss: updatedState.dismiss,
						dismissData: deepClone(updatedState.dismissData || {}),
						progress: updatedState.progress,
						selectedOption: updatedState.selectedOption,
						progressColor: updatedState.ProgressColor
					};

					// Record the change for undo/redo
					recordChange(
						actionType,
						description,
						stateBefore,
						stateAfter,
						targetId,
						metadata || {
							guideType: state.selectedTemplate || state.selectedTemplateTour,
							currentStep: state.currentStep
						}
					);
				},
				setIsALTKeywordEnabled: () => {
					set((state) => {
						const wasEnabled = state.isALTKeywordEnabled;
						state.isALTKeywordEnabled = !state.isALTKeywordEnabled;

						// If we're disabling element selection mode, clean up all highlights
						if (wasEnabled && !state.isALTKeywordEnabled) {
							// Clean up all highlighted elements immediately
							setTimeout(() => {
								// Remove all elements with quickAdopt-selection class
								const highlightedElements = document.querySelectorAll('.quickAdopt-selection');
								highlightedElements.forEach((element: Element) => {
									const htmlElement = element as HTMLElement;
									htmlElement.style.outline = '';
									//htmlElement.style.backgroundColor = '';
									htmlElement.style.boxShadow = '';
									htmlElement.style.pointerEvents = '';
									htmlElement.removeAttribute('disabled');
									htmlElement.classList.remove('quickAdopt-selection');
								});

								// Remove any elements with data-target-element attribute
								const targetElements = document.querySelectorAll('[data-target-element="true"]');
								targetElements.forEach((element: Element) => {
									const htmlElement = element as HTMLElement;
									htmlElement.style.outline = '';
									//htmlElement.style.backgroundColor = '';
									htmlElement.style.boxShadow = '';
									htmlElement.style.pointerEvents = '';
									htmlElement.removeAttribute('disabled');
									htmlElement.removeAttribute('data-target-element');
								});

								// Remove any elements with quickadapt highlighting attributes
								const quickAdaptElements = document.querySelectorAll('[data-quickadapt-highlighted="true"]');
								quickAdaptElements.forEach((element: Element) => {
									const htmlElement = element as HTMLElement;
									htmlElement.style.outline = '';
									htmlElement.style.outlineOffset = '';
									//htmlElement.style.backgroundColor = '';
									htmlElement.style.boxShadow = '';
									htmlElement.style.pointerEvents = '';
									htmlElement.removeAttribute('disabled');
									htmlElement.removeAttribute('data-quickadapt-highlighted');
								});

								console.log('Cleaned up all element highlights after disabling Alt selection mode');
							}, 0);
						}
					});
				},
				resetALTKeywordForNewTooltip: () => {
					set((state) => {
		
						state.isALTKeywordEnabled = true;
					});
				},
				settingAnchorEl: {
					containerId: "",
					buttonId: "",
					value: null,
				},
				settingAnchorElNew: {
					containerId: "",
					rteId: "",
					value: null,
				},
				tooltipBtnSettingAnchorEl: {
					containerId: "",
					buttonId: "",
					value: null,
				},
				imageAnchorEl: {
					containerId: "",
					buttonId: "",
					value: null,
				},
				rteAnchorEl: {
					containerId: "",
					rteId: "",
					value: null,
				},
				isTooltipPopup: false,
				setIsTooltipPopup: (data) => {
					set((state) => {
						state.isTooltipPopup = data;
					});
				},
				hotspbgcolor: "red",
				setHotspBgColor: (data) => {
					set((state) => {
						state.hotspbgcolor = data;
					});
				},
				hotspotSize: "",
				setHotspotSize: (data) => {
					set((state) => {
						state.hotspotSize = data;
					});
				},
				progress: false,
				setProgress: (data) => {
					set((state) => {
						// Store the previous state for undo
						const stateBefore = {
							dismiss: state.dismiss,
							dismissData: deepClone(state.dismissData || {}),
							progress: state.progress,
							selectedOption: state.selectedOption,
							progressColor: state.ProgressColor
						};

						// Update the state
						state.progress = data;

						// For AI-created tours, immediately apply global progress bar synchronization
						if (state.createWithAI && state.selectedTemplate === "Tour") {
							console.log("🔄 setProgress: Applying global progress bar synchronization for AI tour", {
								newProgressValue: data,
								currentStep: state.currentStep,
								totalSteps: state.interactionData?.GuideStep?.length || 0
							});

							// Apply the change to all steps immediately
							state.syncGlobalProgressBarStateForAITour();
						}

						// Record the change for undo/redo
						recordChange(
							'ELEMENT_UPDATE',
							`Updated progress setting`,
							stateBefore,
							{
								dismiss: state.dismiss,
								dismissData: deepClone(state.dismissData || {}),
								progress: data,
								selectedOption: state.selectedOption,
								progressColor: state.ProgressColor
							},
							`element_settings`,
							{
								guideType: state.selectedTemplate || state.selectedTemplateTour,
								currentStep: state.currentStep,
								progress: data,
								selectedOption: state.selectedOption,
								dismiss: state.dismiss,
								progressColor: state.ProgressColor
							}
						);
					});
				},
				pulseAnimationsH: true,
				setPulseAnimationsH: (data) => {
					set((state) => {
						state.pulseAnimationsH = data;
					});
				},
				dismiss: true,
				setDismiss: (data) => {
					set((state) => {
						// Store the previous state for undo
						const stateBefore = {
							dismiss: state.dismiss,
							dismissData: deepClone(state.dismissData || {}),
							progress: state.progress,
							selectedOption: state.selectedOption,
							progressColor: state.ProgressColor
						};

						// Update the state
						state.dismiss = data;

						// For AI-created tours, immediately apply global progress bar synchronization
						if (state.createWithAI && state.selectedTemplate === "Tour") {
							console.log("🔄 setDismiss: Applying global progress bar synchronization for AI tour", {
								newDismissValue: data,
								currentStep: state.currentStep,
								totalSteps: state.interactionData?.GuideStep?.length || 0
							});

							// Apply the change to all steps immediately
							state.syncGlobalProgressBarStateForAITour();
						}

						// Record the change for undo/redo
						recordChange(
							'ELEMENT_UPDATE',
							`Updated dismiss setting`,
							stateBefore,
							{
								dismiss: data,
								dismissData: deepClone(state.dismissData || {}),
								progress: state.progress,
								selectedOption: state.selectedOption,
								progressColor: state.ProgressColor
							},
							`element_settings`,
							{
								guideType: state.selectedTemplate || state.selectedTemplateTour,
								currentStep: state.currentStep,
								progress: state.progress,
								selectedOption: state.selectedOption,
								dismiss: data,
								progressColor: state.ProgressColor
							}
						);
					});
				},
				elementbuttonClick: false,
				SetElementButtonClick: (data) => {
					set((state) => {
						state.elementbuttonClick = data;
					});
				},
				buttonClick: false,
				setButtonClick: (data) => {
					set((state) => {
						state.buttonClick = data;
					});
				},
				isSaveClicked: false,
				setIsSaveClicked: (data) => {
					set((state) => {
						state.isSaveClicked = data;
					});
				},

				hotspotXaxis: "",
				setHotspotXaxis: (data) => {
					set((state) => {
						state.hotspotXaxis = data;
					});
				},
				hotspotYaxis: "",
				setHotspotYaxis: (data) => {
					set((state) => {
						state.hotspotYaxis = data;
					});
				},
				hotspotBorderColor: "",
				setHotspotBorderColor: (data) => {
					set((state) => {
						state.hotspotBorderColor = data;
					});
				},
				pageinteraction: false, // Default to false to ensure mutual exclusivity with overlayEnabled (which defaults to true)
				setPageInteraction: (data, skipMutualExclusivity = false) => {
					set((state) => {
						// Store the previous state for undo
						const stateBefore = {
							overlayEnabled: state.overlayEnabled,
							pageinteraction: state.pageinteraction
						};

						// Update the state
						state.pageinteraction = data;

						// Only enforce mutual exclusivity if not in loading mode
						if (!skipMutualExclusivity) {
							// Asymmetric behavior: only disable overlay when page interaction is enabled
							if (data === true) {
								// When page interaction is enabled, automatically disable overlay
								state.overlayEnabled = false;
							}
							// When page interaction is disabled, do NOT automatically enable overlay
							// This allows both options to be disabled simultaneously
						}

						// For announcements, automatically sync global page interaction state to all steps
						if (state.selectedTemplate === "Announcement") {
							state.syncGlobalOverlayStateForAnnouncements();
						}

						// Record the change for undo/redo (only if not in loading mode)
						if (!skipMutualExclusivity) {
							recordChange(
								'OVERLAY_UPDATE',
								`Updated page interaction settings`,
								stateBefore,
								{ overlayEnabled: state.overlayEnabled, pageinteraction: data },
								`overlay_settings`,
								{
									guideType: state.selectedTemplate || state.selectedTemplateTour,
									currentStep: state.currentStep
								}
							);
						}
					});
				},
				editClicked: false,
				setEditClicked: (data) => {
					set((state) => {
						state.editClicked = data;
					});
				},
				dropdownValue: "",
				setDropdownValue: (data) => {
					set((state) => {
						state.dropdownValue = data;
					});
				},
				openTooltip: false,
				hotspotWidth: "",
				setHotspotWidth: (data) => {
					set((state) => {
						state.hotspotWidth = data;
					});
				},
				selectedOption: "", // Initial value
				setSelectedOption: (data) => {
					set((state) => {
						// Store the previous state for undo
						const stateBefore = {
							dismiss: state.dismiss,
							dismissData: deepClone(state.dismissData || {}),
							progress: state.progress,
							selectedOption: state.selectedOption,
							progressColor: state.ProgressColor
						};

						// Update the state
						state.selectedOption = data;

						// For AI-created tours, immediately apply global progress bar synchronization
						if (state.createWithAI && state.selectedTemplate === "Tour") {
							console.log("🔄 setSelectedOption: Applying global progress bar synchronization for AI tour", {
								newSelectedOption: data,
								currentStep: state.currentStep,
								totalSteps: state.interactionData?.GuideStep?.length || 0
							});

							// Apply the change to all steps immediately
							state.syncGlobalProgressBarStateForAITour();
						}

						// Record the change for undo/redo
						recordChange(
							'ELEMENT_UPDATE',
							`Updated progress option`,
							stateBefore,
							{
								dismiss: state.dismiss,
								dismissData: deepClone(state.dismissData || {}),
								progress: state.progress,
								selectedOption: data,
								progressColor: state.ProgressColor
							},
							`element_settings`,
							{
								guideType: state.selectedTemplate || state.selectedTemplateTour,
								currentStep: state.currentStep,
								progress: state.progress,
								selectedOption: data,
								dismiss: state.dismiss,
								progressColor: state.ProgressColor
							}
						);
					});
				},
				autoPosition: false,
				setAutoPosition: (data) => {
					set((state) => {
						state.autoPosition = data;
					});
				},
				elementClick: "element",
				setElementClick: (data) => {
					set((state) => {
						state.elementClick = data;
					});
				},
				highlightedButton: null,
				setHighlightedButton: (data) => {
					set((state) => {
						state.highlightedButton = data;
					});
				},
				btnidss: "",
				setbtnidss: (data) => {
					set((state) => {
						state.btnidss = data;
					});
				},
				hotspotHeight: "",
				setHotspotHeight: (data) => {
					set((state) => {
						state.hotspotHeight = data;
					});
				},
				ButtonsDropdown: "close",
				setButtonsDropdown: (data) => {
					set((state) => {
						state.ButtonsDropdown = data;
					});
				},
				hotspotBackgroundColor: "",
				setHotspotBackgroundColor: (data) => {
					set((state) => {
						state.hotspotBackgroundColor = data;
					});
				},
				storedTransform: {},
				setStoredTransform: (stepIndex: number, data: string) => {
					set((state) => {
						state.storedTransform[stepIndex] = data;
					});
				},

				open: true,
				setOpen: (data) => {
					set((state) => {
						state.isPopoverOpen = data;
					});
				},
				elementButtonName: "",
				setElementButtonName: (data) => {
					set((state) => {
						state.elementButtonName = data;
					});
				},
				currentStepIndex: 0,
				setCurrentStepIndex: (data) => {
					set((state) => {
						state.currentStepIndex = data;
					});
				},
				hotspotBorderRadius: "",
				setHotspotBorderRadius: (data) => {
					set((state) => {
						state.hotspotBorderRadius = data;
					});
				},
				hotspotPosition: "",
				setHotspotPosition: (data) => {
					set((state) => {
						state.hotspotOpacity = data;
					});
				},
				axisData: null as DOMRect | null,
				setAxisData: (data: DOMRect | null) => {
					set((state) => {
						state.axisData = data;
					});
				},
				hotspotOpacity: "",
				setHotspotOpacity: (data) => {
					set((state) => {
						state.hotspotOpacity = data;
					});
				},
				elementSelected: false,
				setElementSelected: (data) => {
					set((state) => {
						state.elementSelected = data;
					});
				},
				pulseAnimation: false,
				setPulseAnimation: (data) => {
					set((state) => {
						state.pulseAnimation = data;
					});
				},
				// stopAnimationUponInteraction: true,
				// setStopAnimationUponInteraction: (data) => {
				// 	set((state) => {
				// 		state.stopAnimationUponInteraction = data;
				// 	});
				// },
				imagesContainer: [
					{
						id: crypto.randomUUID(),
						images: [
							{
								id: crypto.randomUUID(),
								url: defaultAnnouncementImages[Math.floor(Math.random() * defaultAnnouncementImages.length)],
								altText: "Default Announcement Image",
								backgroundColor: "#ffffff",
								objectFit: "contain",
							  },
						],
						style: {
							backgroundColor: "transparent",
							height: IMG_CONTAINER_DEFAULT_HEIGHT,
							paddingLeft: 0,
							paddingTop: 0,
							paddingRight: 0,
							paddingBottom: 0,
							maxHeight: 0,
						},
						hyperlink: undefined,
					},
				],
				rtesContainer: [
					{
						id: crypto.randomUUID(),
						rtes: [
							{
								id: crypto.randomUUID(), // Ensure each RTE has a unique ID
								text: "", // Initialize text if needed
							},
						],
						style: {
							backgroundColor: "#f0f0f0",
						},
					},
				],
				buttonsContainer: BUTTON_CONT_DEF_VALUE,
				announcementGuideMetaData: [
					{
						containers: [
							// {
							// 	id: getRandomID(),
							// 	type: "rte",
							// 	placeholder: DEF_PLACEHOLDER_MSG,
							// 	rteBoxValue: "",
							// },
						],
						stepName: "",
						stepDescription: "",
						stepType: "Announcement",
						currentStep: 1,
						xpath: DEF_XPATH,
						id: crypto.randomUUID(),
						hotspots: HOTSPOT_DEFAULT_VALUE,
						canvas: {},
						design: {
							gotoNext: "",
							element: {
								progress: "Template1", // Default enabled for new announcements
								isDismiss: false,
								progressSelectedOption: 1,
								progressColor: "var(--primarycolor)"
							},
						},
					},
				],

				toolTipGuideMetaData: [
					{
						containers: [
							// {
							// 	id: getRandomID(),
							// 	type: "rte",
							// 	placeholder: DEF_PLACEHOLDER_MSG,
							// 	rteBoxValue: "",
							// },
						],
						stepName: "Step 1",
						currentStep: 1,
						stepId: "",
						stepDescription: "",
						stepType: "Tooltip",
						xpath: DEF_XPATH,
						id: crypto.randomUUID(),
						hotspots: HOTSPOT_DEFAULT_VALUE,
						canvas: CANVAS_DEFAULT_VALUE,
						design: {
							gotoNext: "",
							element: {
								progress: "",
								isDismiss: "",
							},
						},
					},
				],

				checklistGuideMetaData: [
					{
						id: crypto.randomUUID(),
						checkpoints: CHECKPOINT_DEFAULT_VALUE_CHECKLIST,
						TitleSubTitle: TITLESUBTITLE_DEFAULT_VALUE_CHECKLIST,
						canvas: CANVAS_DEFAULT_VALUE_CHECKLIST,
						launcher: LAUNCHER_DEFAULT_VALUE_CHECKLIST,
					},
				],

				hotspotGuideMetaData: [
					{
						containers: [
							{
								...BUTTON_CONT_DEF_VALUE_1,
								type: "button",
								id: getRandomID(),
							},
							{
								...IMG_CONT_DEF_VALUE,
								type: "image",
								id: getRandomID(),
							},
						],
						currentStep: 1,
						xpath: DEF_XPATH,
						id: crypto.randomUUID(),
						hotspots: HOTSPOT_DEFAULT_VALUE,
					},
				],
				selectedTemplateTour: "",
				tooltipCount: 0,
				fit: "",
				isPopoverOpen: false,
				tooltip: {
					visible: false,
					text: "",
					position: { x: 0, y: 0 },
				},
				setIsPopoverOpen: (data) => {
					set((state) => {
						state.isPopoverOpen = data;
					});
				},
				setTooltipCount: (data) => {
					set((state) => {
						state.tooltipCount = data;
					});
				},

				setCurrentStep: (data) => {
					set((state) => {
						// Set flag to prevent save button from being enabled during step navigation
						state.isNavigatingBetweenSteps = true;

						// Sync AI data before changing steps for tours
						if (state.createWithAI && state.selectedTemplate === "Tour") {
							// First, ensure global progress bar state is applied to all steps
							// This prevents step-specific settings from overriding global state
							state.syncGlobalProgressBarStateForAITour();

							// Call the unified tour sync function to ensure data is synchronized
							// This will handle all step types (announcement, tooltip, banner, hotspot)
							const aiGuideSteps = state.interactionData?.GuideStep || [];
							if (aiGuideSteps.length > 0) {
								// Sync data for the step we're navigating to
								const targetStepIndex = data - 1;
								const targetStepData = aiGuideSteps[targetStepIndex];

								if (targetStepData && (targetStepData.StepType === "Tooltip" || targetStepData.StepType === "Hotspot")) {
									// For tooltip and hotspot steps, ensure metadata is initialized and XPath data is available
									const existingMetadata = state.toolTipGuideMetaData[targetStepIndex];
									if (!existingMetadata) {
										// Initialize tooltip/hotspot metadata first
										const initFunction = state.initializeAITourTooltipMetadata;
										if (initFunction) {
											initFunction();
										}
									}

									// Then sync any additional data if needed
									if (!existingMetadata || !existingMetadata.xpath?.value) {
										// Temporarily set currentStep to target to sync the right data
										const originalStep = state.currentStep;
										state.currentStep = data;

										// Call sync function
										const syncFunction = state.syncAITourDataForPreview;
										if (syncFunction) {
											syncFunction();
										}

										// Restore original step (will be set to data below)
										state.currentStep = originalStep;
									}
								}
							}

							console.log("🔄 setCurrentStep: Applied global progress bar synchronization before step navigation", {
								fromStep: state.currentStep,
								toStep: data,
								globalProgressState: {
									progress: state.progress,
									selectedOption: state.selectedOption,
									dismiss: state.dismiss,
									progressColor: state.ProgressColor
								}
							});
						}

						// Sync AI announcement data before changing steps
						if (state.createWithAI && state.selectedTemplate === "Announcement") {
							// For pure AI announcements (not in tours), use announcementGuideMetaData
							const currentStepIndex = state.currentStep - 1;
							const currentAnnouncementMetadata = state.announcementGuideMetaData[currentStepIndex];

							if (currentAnnouncementMetadata && state.interactionData?.GuideStep?.[currentStepIndex]) {
								// Update interactionData with current announcement metadata before step change
								const currentGuideStep = state.interactionData.GuideStep[currentStepIndex];

								// Update TextFieldProperties from RTE containers
								const rteContainers = currentAnnouncementMetadata.containers.filter(c => c.type === "rte");
								if (rteContainers.length > 0) {
									currentGuideStep.TextFieldProperties = rteContainers.map((container: any) => ({
										Id: container.id,
										Text: container.rteBoxValue || "",
									}));
								}

								// Update Canvas settings
								currentGuideStep.Canvas = {
									Position: currentAnnouncementMetadata.canvas.position,
									Width: currentAnnouncementMetadata.canvas.width,
									Padding: currentAnnouncementMetadata.canvas.padding,
									Radius: currentAnnouncementMetadata.canvas.borderRadius,
									BorderSize: currentAnnouncementMetadata.canvas.borderSize,
									BorderColor: currentAnnouncementMetadata.canvas.borderColor,
									BackgroundColor: currentAnnouncementMetadata.canvas.backgroundColor,
								};

								console.log("Synced AI pure announcement data before step change from", state.currentStep, "to", data);
							}

							// After syncing the current step data, also sync announcementJson
							const updatedGuideSteps = state.announcementGuideMetaData.map((metadata: any, index: number) => {
								return {
									stepName: index + 1, // Use step number as stepName
									StepId: metadata.id,
									StepTitle: metadata.stepName,
									StepCount: index + 1,
									StepType: metadata.stepType || "Announcement",
									Description: metadata.stepDescription || "",
									Canvas: {
										Position: metadata.canvas?.position || "center-center",
										Width: metadata.canvas?.width || 500,
										Padding: metadata.canvas?.padding || "12",
										Radius: metadata.canvas?.borderRadius || 8,
										BorderSize: metadata.canvas?.borderSize || 0,
										BorderColor: metadata.canvas?.borderColor || "#000000",
										BackgroundColor: metadata.canvas?.backgroundColor || "#ffffff",
									},
								};
							});

							// Update announcementJson with the new GuideStep data
							state.announcementJson = {
								...state.announcementJson,
								GuideStep: updatedGuideSteps,
							};

							console.log("Updated announcementJson during step change:", state.announcementJson);
						}

						// Sync button data for non-AI guides when changing steps
						if (!state.createWithAI && (state.selectedTemplate === "Tooltip" || state.selectedTemplate === "Announcement" ||
							(state.selectedTemplate === "Tour" && (state.selectedTemplateTour === "Tooltip" || state.selectedTemplateTour === "Announcement" || state.selectedTemplateTour === "Banner")))) {
							// Save current step's button data to metadata before changing steps
							console.log("🔄 Saving button data before step navigation from", state.currentStep, "to", data);
							state.syncButtonContainerToMetadata();
							state.syncMetadataToSavedGuideData();
						}

						// CRITICAL FIX: Sync AI tooltip data when changing steps in AI tours
						if (state.createWithAI && state.selectedTemplate === "Tour") {
							// Save current step's data to interactionData before changing steps
							console.log("🔄 Syncing AI tour data before step navigation from", state.currentStep, "to", data);
							state.syncCurrentStepDataForAITour();

							// Also ensure tooltip data is properly synchronized for preview
							if (state.selectedTemplateTour === "Tooltip") {
								state.syncAITooltipDataForPreview();
							}
						}

						state.currentStep = data;

						// For announcements, ensure global overlay state is maintained during step navigation
						if (state.selectedTemplate === "Announcement") {
							// Preserve global overlay state - don't load from individual steps
							// This prevents individual step data from overriding global settings
							setTimeout(() => {
								state.syncGlobalOverlayStateForAnnouncements();
							}, 0);

							console.log("🔄 setCurrentStep: Preserved global overlay state for announcement", {
								step: data,
								overlayEnabled: state.overlayEnabled,
								pageinteraction: state.pageinteraction
							});
						}

						// Reset banner canvas settings when navigating to a banner step in tours
						if (state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Banner") {
							// Check if this is a newly created banner step (no canvas data in bannerJson)
							const currentBannerStep = state.bannerJson?.GuideStep?.[data - 1];
							if (!currentBannerStep?.Canvas || Object.keys(currentBannerStep.Canvas).length === 0) {
								// This is a new banner step, reset canvas to defaults
								state.resetBannerCanvasToDefaults();
							} else if (currentBannerStep?.Canvas) {
								// Check if the canvas data has default values and reset if so
								const canvas = currentBannerStep.Canvas;
								const hasDefaultValues =
									canvas.BackgroundColor === CANVAS_DEFAULT_VALUE_Banner.backgroundColor &&
									canvas.BorderColor === CANVAS_DEFAULT_VALUE_Banner.borderColor &&
									canvas.BorderSize === CANVAS_DEFAULT_VALUE_Banner.borderSize &&
									canvas.Padding === CANVAS_DEFAULT_VALUE_Banner.padding &&
									canvas.Position === CANVAS_DEFAULT_VALUE_Banner.position;

								if (hasDefaultValues) {
									// This step has default values, ensure state variables are also set to defaults
									state.resetBannerCanvasToDefaults();
								}
							}
						}

						// Load button data for the new step for non-AI guides
						if (!state.createWithAI && (state.selectedTemplate === "Tooltip" || state.selectedTemplate === "Announcement" ||
							(state.selectedTemplate === "Tour" && (state.selectedTemplateTour === "Tooltip" || state.selectedTemplateTour === "Announcement" || state.selectedTemplateTour === "Banner")))) {
							// Load button data from metadata for the new step
							console.log("🔄 Loading button data for new step", data);
							state.syncMetadataToButtonContainer();
						}

						// CRITICAL FIX: Load AI tooltip data for the new step in AI tours
						if (state.createWithAI && state.selectedTemplate === "Tour") {
							// Load data for the new step from interactionData
							console.log("🔄 Loading AI tour data for new step", data);

							// Ensure tooltip metadata is properly loaded for the new step
							if (state.selectedTemplateTour === "Tooltip") {
								// Sync tooltip data for the new step
								state.syncAITooltipDataForPreview();

								// Also restore element click state for the new step
								setTimeout(() => {
									console.log("🔄 setCurrentStep: Restoring element click state for new tooltip step", data);
									state.restoreTooltipElementClickState();
								}, 50);
							}
						}

						// Clear the navigation flag after all operations complete
						// Use setTimeout to ensure all sync operations and component updates complete
						setTimeout(() => {
							state.isNavigatingBetweenSteps = false;
						}, 50); // Short timeout to ensure all operations complete
					});
				},

				setSteps: (data) => {
					set((state) => {
						state.steps = data;
					});
				},

				textArray: [],
				setTextArray: (data) => {
					set((state) => {
						state.textArray = data;
					});
				},
				textvaluess: "",
				setTextvaluess: (data) => {
					set((state) => {
						if (data) {
							state.isUnSavedChanges = true;
						}
						state.textvaluess = data;
					});
				},
				btnBgColor: "",
				setBtnBgColor: (data) => {
					set((state) => {
						state.btnBgColor = data;
					});
				},
				showTooltipCanvasSettings: false,
				setShowTooltipCanvasSettings: (data) => {
					set((state) => {
						state.showTooltipCanvasSettings = data;
					});
				},
				showLauncherSettings: false,
				setShowLauncherSettings: (data) => {
					set((state) => {
						state.showLauncherSettings = data;
					});
				},
				preview: false,
				setPreview: (data) => {
					set((state) => {
						state.preview = data;
					});
				},
				cuntainerId: "",
				setCuntainerId: (data) => {
					set((state) => {
						state.cuntainerId = data;
					});
				},
				btnname: "",
				setBtnName: (data) => {
					set((state) => {
						state.btnname = data;
					});
				},
				buttonId: "",
				setButtonId: (data) => {
					set((state) => {
						state.buttonId = data;
					});
				},
				btnBorderColor: "",
				setBtnBorderColor: (data) => {
					set((state) => {
						state.btnBorderColor = data;
					});
				},
				newCurrentStep: 0,
				setNewCurrentStep: (data) => {
					set((state) => {
						state.newCurrentStep = data;
					});
				},
				btnTextColor: "",
				setBtnTextColor: (data) => {
					set((state) => {
						state.btnTextColor = data;
					});
				},

				buttonProperty: false,
				setButtonProperty: (data) => {
					set((state) => {
						state.buttonProperty = data;
					});
				},
				bannerButtonSelected: false,
				setBannerButtonSelected: (data) => {
					set((state) => {
						state.bannerButtonSelected = data;
					});
				},
				loading: false,
				setLoading: (data) => {
					set((state) => {
						state.buttonProperty = data;
					});
				},
				guidedatas: "",
				setGuideDataS: (data) => {
					set((state) => {
						state.guidedatas = data;
					});
				},
				bannerPopup: false,
				setBannerPopup: (data) => {
					set((state) => {
						state.bannerPopup = data;
					});
				},
				hotspotPopup: false,
				setHotspotPopup: (data) => {
					set((state) => {
						state.hotspotPopup = data;
					});
				},
				titlePopup: false,
				setTitlePopup: (data) => {
					set((state) => {
						state.titlePopup = data;
					});
				},
				ziindex: false,
				setZiindex: (data) => {
					set((state) => {
						state.ziindex = data;
					});
				},
				setFit: (data) => {
					set((state) => {
						state.padding = data;
					});
				},
				isPlaceholderVisible: true,
				setIsPlaceholderVisible: (data) => {
					set((state) => {
						state.isPlaceholderVisible = data;
					});
				},
				openWarning: false,
				setOpenWarning: (data) => {
					set((state) => {
						state.openWarning = data;
					});
				},
				isCollapsed: false,
				setIsCollapsed: (data) => {
					set((state) => {
						state.isCollapsed = data;
					});
				},
				isGuideInfoScreen: false,
				setIsGuideInfoScreen: (data) => {
					set((state) => {
						state.isGuideInfoScreen = data;
					});
				},
				sectionColor: "",
				setSectionColor: (data) => {
					set((state) => {
						state.sectionColor = data;
					});
				},
				backgroundC: "",
				setBackgroundC: (data) => {
					set((state) => {
						state.backgroundC = data;
					});
				},
				Bposition: "Cover Top",
				setBposition: (data) => {
					set((state) => {
						state.Bposition = data;
					});
				},
				bpadding: "12",
				setbPadding: (data) => {
					set((state) => {
						state.bpadding = data;
					});
				},
				Bbordercolor: "#00000000",
				setBBorderColor: (data) => {
					set((state) => {
						state.Bbordercolor = data;
					});
				},
				ProgressColor: "var(--primarycolor)",
			setProgressColor: (data) => {
				set((state) => {
					// Store the previous state for undo
					const stateBefore = {
						dismiss: state.dismiss,
						dismissData: deepClone(state.dismissData || {}),
						progress: state.progress,
						selectedOption: state.selectedOption,
						progressColor: state.ProgressColor
					};

					// Update the state
					state.ProgressColor = data;

					// For AI-created tours, immediately apply global progress bar synchronization
					if (state.createWithAI && state.selectedTemplate === "Tour") {
						console.log("🔄 setProgressColor: Applying global progress bar synchronization for AI tour", {
							newProgressColor: data,
							currentStep: state.currentStep,
							totalSteps: state.interactionData?.GuideStep?.length || 0
						});

						// Apply the change to all steps immediately
						state.syncGlobalProgressBarStateForAITour();
					}

					// Record the change for undo/redo
					recordChange(
						'ELEMENT_UPDATE',
						`Updated progress color`,
						stateBefore,
						{
							dismiss: state.dismiss,
							dismissData: deepClone(state.dismissData || {}),
							progress: state.progress,
							selectedOption: state.selectedOption,
							progressColor: data
						},
						`element_settings`,
						{
							guideType: state.selectedTemplate || state.selectedTemplateTour,
							currentStep: state.currentStep,
							progress: state.progress,
							selectedOption: state.selectedOption,
							dismiss: state.dismiss,
							progressColor: data
						}
					);
				});
			},
				BborderSize: "2",
				setBBorderSize: (data) => {
					set((state) => {
						state.BborderSize = data;
					});
				},
				zindeex: 9999,
				setZindeex: (data) => {
					set((state) => {
						state.zindeex = data;
					});
				},
				htmlCode: "",
				setHtmlCode: (data) => {
					set((state) => {
						state.htmlCode = data;
					});
				},
				fill: "",
				setFill: (data) => {
					set((state) => {
						state.padding = data;
					});
				},
				setImageSrc: (data) => {
					set((state) => {
						state.padding = data;
					});
				},
				imageSrc: "",
				textBoxRef: "",
				htmlContent: "",
				setHtmlContent: (data: string) =>
					set((state) => {
						state.htmlContent = data;
					}),
				buttonColor: "",
				setButtonColor: (data) => {
					set((state) => {
						state.padding = data;
					});
				},
				setImageName: (data) => {
					set((state) => {
						state.padding = data;
					});
				},
				imageName: "",
				alignment: "",
				setAlignment: (data) => {
					set((state) => {
						state.padding = data;
					});
				},
				textvalue: "",
				setTextvalue: (data) => {
					set((state) => {
						state.padding = data;
					});
				},
				sectionHeight: "",
				setSectionHeight: (data) => {
					set((state) => {
						state.padding = data;
					});
				},
				guideName: "",
				SetGuideName: (data) => {
					set((state) => {
						state.guideName = data;
					});
				},
				tempGuideName: "",
				setTempGuideName: (data) => {
					set((state) => {
						state.tempGuideName = data;
					});
				},
				tooltipXaxis: "4",
				setTooltipXaxis: (data) => {
					set((state) => {
						state.tooltipXaxis = data;
					});
				},
				tooltipYaxis: "4",
				setTooltipYaxis: (data) => {
					set((state) => {
						state.tooltipYaxis = data;
					});
				},

				tooltipPosition: "",
				setTooltipPosition: (data) => {
					set((state) => {
						state.tooltipPosition = data;
					});
				},
				tooltipWidth: "300",
				setTooltipWidth: (data) => {
					set((state) => {
						state.tooltipWidth = data;
					});
				},
				tooltippadding: "12",
				setTooltipPadding: (data) => {
					set((state) => {
						state.tooltippadding = data;
					});
				},
				tooltipborderradius: "8",
				setTooltipBorderradius: (data) => {
					set((state) => {
						state.tooltipborderradius = data;
					});
				},
				tooltipbordersize: "0",
				setTooltipBordersize: (data) => {
					set((state) => {
						state.tooltipbordersize = data;
					});
				},
				tooltipBordercolor: "transparent",
				setTooltipBordercolor: (data) => {
					set((state) => {
						state.tooltipBordercolor = data;
					});
				},
				tooltipBackgroundcolor: "",
				setTooltipBackgroundcolor: (data) => {
					set((state) => {
						state.tooltipBackgroundcolor = data;
					});
				},
				hotspotXaxis: "",
				setHotspotXaxis: (data) => {
					set((state) => {
						state.hotspotXaxis = data;
					});
				},

				position: "",
				setPosition: (data) => {
					set((state) => {
						state.position = data;
					});
				},
				padding: "12",
				setPadding: (data) => {
					set((state) => {
						state.padding = data;
					});
				},
				Annpadding: "12",
				setAnnPadding: (data) => {
					set((state) => {
						state.Annpadding = data;
					});
				},
				width: 500,
				setWidth: (data) => {
					set((state) => {
						state.width = data;
					});
				},
				radius: "",
				setRadius: (data) => {
					set((state) => {
						state.selectedTemplate = data;
					});
				},
				borderSize: 0,
				setBorderSize: (data) => {
					set((state) => {
						state.borderSize = data;
					});
				},
				AnnborderSize: 2,
				setAnnBorderSize: (data) => {
					set((state) => {
						state.AnnborderSize = data;
					});
				},
				borderColor: "transparent",
				setBorderColor: (data) => {
					set((state) => {
						state.borderColor = data;
					});
				},

				backgroundColor: "#f0f0f0",
				setBackgroundColor: (data) => {
					set((state) => {
						state.backgroundColor = data;
					});
				},
				borderRadius: 4,
				setBorderRadius: (data) => {
					set((state) => {
						state.borderRadius = data;
					});
				},

				setDesignPopup: (data) => {
					set((state) => {
						state.designPopup = data;
					});
				},
				selectedStepTypeHotspot: false,
				setSelectedStepTypeHotspot: (data) => {
					set((state) => {
						state.selectedStepTypeHotspot = data;
					});
				},
				setSelectedTemplate: (data) => {
					set((state) => {
						state.selectedTemplate = data;
					});
				},
				setSelectedTemplateTour: (data, skipOverlayReset = false) => {
					set((state) => {
						state.selectedTemplateTour = data;
						// Only reset overlay and page interaction to default values when creating a new tour template
						// Skip reset during mode transitions to preserve user settings
						if (!skipOverlayReset) {
							state.overlayEnabled = true; // Default enabled
							state.pageinteraction = false; // Default disabled
						}

						// Reset banner canvas settings to defaults when switching to Banner template in tours
						if (data === "Banner") {
							state.Bposition = CANVAS_DEFAULT_VALUE_Banner.position;
							state.bpadding = CANVAS_DEFAULT_VALUE_Banner.padding;
							state.BborderSize = CANVAS_DEFAULT_VALUE_Banner.borderSize;
							state.Bbordercolor = CANVAS_DEFAULT_VALUE_Banner.borderColor;
							state.backgroundC = CANVAS_DEFAULT_VALUE_Banner.backgroundColor;

							// Also reset the global canvas state variables that might be used
							state.backgroundColor = CANVAS_DEFAULT_VALUE_Banner.backgroundColor;
							state.borderColor = CANVAS_DEFAULT_VALUE_Banner.borderColor;
							state.Annpadding = CANVAS_DEFAULT_VALUE_Banner.padding;
							state.AnnborderSize = CANVAS_DEFAULT_VALUE_Banner.borderSize;
						}
					});
				},
				announcementPreview: false,
				setAnnouncementPreview: (data) => {
					set((state) => {
						state.announcementPreview = data;
					});
				},
				bannerPreview: false,
				setBannerPreview: (data) => {
					set((state) => {
						state.bannerPreview = data;
					});
				},

				tooltipPreview: false,
				setTooltipPreview: (data) => {
					set((state) => {
						state.tooltipPreview = data;
					});
				},

				hotspotPreview: false,
				setHotspotPreview: (data) => {
					set((state) => {
						state.hotspotPreview = data;
					});
				},
				/* Checklist */
				titleColor: "transparent",
				setTitleColor: (data) => {
					set((state) => {
						state.titleColor = data;
					});
				},
				launcherColor: "transparent",
				setLauncherColor: (data) => {
					set((state) => {
						state.launcherColor = data;
					});
				},
				checkpointsPopup: false,
				setCheckPointsPopup: (data) => {
					set((state) => {
						state.checkpointsPopup = data;
					});
				},
				checkpointsEditPopup: false,
				setCheckPointsEditPopup: (data) => {
					set((state) => {
						state.checkpointsEditPopup = data;
					});
				},

				checkpointsAddPopup: false,
				setCheckPointsAddPopup: (data) => {
					set((state) => {
						state.checkpointsAddPopup = data;
					});
				},
				checkpointTitleColor: "transparent",
				setCheckpointTitleColor: (data) => {
					set((state) => {
						state.checkpointTitleColor = data;
					});
				},
				checkpointTitleDescription: "",

				setCheckpointTitleDescription: (data) => {
					set((state) => {
						state.checkpointTitleDescription = data;
					});
				},
				checkpointIconColor: "transparent",
				setCheckpointIconColor: (data) => {
					set((state) => {
						state.checkpointIconColor = data;
					});
				},
				unlockCheckPointInOrder: false,
				setUnlockCheckPointInOrder: (data) => {
					set((state) => {
						state.unlockCheckPointInOrder = data;
					});
				},
				checkPointMessage: "",
				setCheckPointMessage: (data) => {
					set((state) => {
						state.checkPointMessage = data;
					});
				},
				checklistTitle: "",
				setChecklistTitle: (data) => {
					set((state) => {
						state.checkPointMessage = data;
					});
				},
				checklistSubTitle: "",
				setChecklistSubTitle: (data) => {
					set((state) => {
						state.checklistSubTitle = data;
					});
				},

				/* Checklist */

				// setSelectedTemplate: (data) => {
				// 	set((state) => {
				// 		state.selectedTemplate = data;
				// 		if (state.selectedTemplate === "Hotspot" || state.selectedTemplateTour === "Hotspot") {
				// 			if (state.toolTipGuideMetaData[0]) {
				// 				state.toolTipGuideMetaData[0].containers = [

				// 					{
				// 						id: getRandomID(),
				// 						type: "rte",
				// 						placeholder: DEF_PLACEHOLDER_MSG,
				// 						rteBoxValue: "",
				// 					},
				// 					{
				// 						...BUTTON_CONT_DEF_VALUE_1,
				// 						type: "button",
				// 						id: getRandomID(),
				// 					},
				// 				];
				// 			} else if (state.selectedTemplate === "Tooltip" || state.selectedTemplateTour === "Tooltip") {
				// 				state.toolTipGuideMetaData[0].containers = [
				// 					{
				// 						id: getRandomID(),
				// 						type: "rte",
				// 						placeholder: DEF_PLACEHOLDER_MSG,
				// 						rteBoxValue: "",
				// 					},
				// 				];
				// 			}
				// 		}
				// 		else {
				// 			if (state.selectedTemplate === "Hotspot" || state.selectedTemplateTour === "Hotspot") {
				// 				state.toolTipGuideMetaData = [
				// 					{
				// 						containers: [
				// 							// {
				// 							// 	id: getRandomID(),
				// 							// 	type: "rte",
				// 							// 	placeholder: DEF_PLACEHOLDER_MSG,
				// 							// 	rteBoxValue: "",
				// 							// },
				// 						],
				// 						stepName: "Step 1",
				// 						currentStep: 1,
				// 						stepDescription: "",
				// 						stepType: "Hotspot",
				// 						xpath: DEF_XPATH,
				// 						id: crypto.randomUUID(),
				// 						hotspots: HOTSPOT_DEFAULT_VALUE,
				// 						canvas: CANVAS_DEFAULT_VALUE,
				// 						design: {
				// 							gotoNext: "",
				// 							element: {
				// 								progress: "",
				// 								isDismiss: "",
				// 							},
				// 						},
				// 					},
				// 				]
				// 				state.toolTipGuideMetaData[0].containers = [

				// 					{
				// 						id: getRandomID(),
				// 						type: "rte",
				// 						placeholder: DEF_PLACEHOLDER_MSG,
				// 						rteBoxValue: "",
				// 					},
				// 					{
				// 						...BUTTON_CONT_DEF_VALUE_1,
				// 						type: "button",
				// 						id: getRandomID(),
				// 					},
				// 				];
				// 			}
				// 			else if (state.selectedTemplate === "Tooltip" || state.selectedTemplateTour === "Tooltip")
				// 			{
				// 				state.toolTipGuideMetaData = [
				// 					{
				// 						containers: [
				// 							// {
				// 							// 	id: getRandomID(),
				// 							// 	type: "rte",
				// 							// 	placeholder: DEF_PLACEHOLDER_MSG,
				// 							// 	rteBoxValue: "",
				// 							// },
				// 						],
				// 						stepName: "Step 1",
				// 						currentStep: 1,
				// 						stepDescription: "",
				// 						stepType: "Tooltip",
				// 						xpath: DEF_XPATH,
				// 						id: crypto.randomUUID(),
				// 						hotspots: HOTSPOT_DEFAULT_VALUE,
				// 						canvas: CANVAS_DEFAULT_VALUE,
				// 						design: {
				// 							gotoNext: "",
				// 							element: {
				// 								progress: "",
				// 								isDismiss: "",
				// 							},
				// 						},
				// 					},
				// 				]
				// 				state.toolTipGuideMetaData[0].containers = [
				// 					{
				// 						id: getRandomID(),
				// 						type: "rte",
				// 						placeholder: DEF_PLACEHOLDER_MSG,
				// 						rteBoxValue: "",
				// 					},
				// 				];

				// 				}
				// 		}
				// 	});
				// },

				setSelectedTemplate: (data, skipOverlayReset = false) => {
					set((state) => {
						state.selectedTemplate = data;
						// Only reset overlay and page interaction to default values when creating a new template
						// Skip reset during mode transitions to preserve user settings
						if (!skipOverlayReset) {
							state.overlayEnabled = true; // Default enabled
							state.pageinteraction = false; // Default disabled
						}

						// Reset banner canvas settings to defaults when switching to Banner template
						if (data === "Banner") {
							state.Bposition = CANVAS_DEFAULT_VALUE_Banner.position;
							state.bpadding = CANVAS_DEFAULT_VALUE_Banner.padding;
							state.BborderSize = CANVAS_DEFAULT_VALUE_Banner.borderSize;
							state.Bbordercolor = CANVAS_DEFAULT_VALUE_Banner.borderColor;
							state.backgroundC = CANVAS_DEFAULT_VALUE_Banner.backgroundColor;

							// Also reset the global canvas state variables that might be used
							state.backgroundColor = CANVAS_DEFAULT_VALUE_Banner.backgroundColor;
							state.borderColor = CANVAS_DEFAULT_VALUE_Banner.borderColor;
							state.Annpadding = CANVAS_DEFAULT_VALUE_Banner.padding;
							state.AnnborderSize = CANVAS_DEFAULT_VALUE_Banner.borderSize;
						}
						if (state.selectedTemplate === "Hotspot" || state.selectedTemplateTour === "Hotspot") {
							state.toolTipGuideMetaData[0].containers = [
								{
									id: getRandomID(),
									type: "rte",
									placeholder: DEF_PLACEHOLDER_MSG,
									rteBoxValue: "",
								},
								{
									...BUTTON_CONT_DEF_VALUE_1,
									type: "button",
									id: getRandomID(),
								},
							];
						} else if (state.selectedTemplate === "Tooltip" || state.selectedTemplateTour === "Tooltip") {
							// For AI-created tooltips, sync data immediately after template selection
							if (state.createWithAI && state.interactionData && state.selectedTemplate === "Tooltip") {
								// Use setTimeout to ensure this runs after the state update
								setTimeout(() => {
									// Call the sync function to populate tooltip metadata from AI data
									const aiGuideSteps = state.interactionData?.GuideStep || [];
									if (aiGuideSteps.length > 0) {
										// Create tooltip metadata from AI data
										const newTooltipMetadata = aiGuideSteps.map((step: any, index: number) => {
											const containers = [];

											// Add RTE containers from TextFieldProperties
											if (step.TextFieldProperties && step.TextFieldProperties.length > 0) {
												step.TextFieldProperties.forEach((textField: any) => {
													containers.push({
														id: textField.Id || getRandomID(),
														type: "rte",
														placeholder: DEF_PLACEHOLDER_MSG,
														rteBoxValue: textField.Text || "",
														style: {
															backgroundColor: "transparent",
														},
													});
												});
											}

											// Add button containers from ButtonSection
											if (step.ButtonSection && step.ButtonSection.length > 0) {
												step.ButtonSection.forEach((buttonSection: any) => {
													if (buttonSection.CustomButtons && buttonSection.CustomButtons.length > 0) {
														const buttons = buttonSection.CustomButtons.map((button: any) => ({
															id: button.ButtonId || getRandomID(),
															name: button.ButtonName || "Button",
															position: "center",
															type: "primary",
															isEditing: false,
															index: 0,
															style: {
																backgroundColor: button.BackgroundColor || "#5F9EA0",
																borderColor: button.BorderColor || "#5F9EA0",
																color: button.TextColor || "#ffffff",
															},
															actions: {
																value: button.Actions?.value || "close",
																targetURL: button.Actions?.targetURL || "",
																tab: button.Actions?.tab || "new-tab",
																interaction: button.Actions?.interaction || null,
															},
															survey: null,
														}));

														containers.push({
															id: buttonSection.Id || getRandomID(),
															type: "button",
															buttons: buttons,
															style: {
																backgroundColor: "transparent",
															},
														});
													}
												});
											}

											// If no containers were created, add a default RTE container
											if (containers.length === 0) {
												containers.push({
													id: getRandomID(),
													type: "rte",
													placeholder: DEF_PLACEHOLDER_MSG,
													rteBoxValue: "",
													style: {
														backgroundColor: "transparent",
													},
												});
											}

											return {
												id: step.StepId || getRandomID(),
												containers: containers,
												currentStep: index + 1,
												stepName: step.StepTitle || `Step ${index + 1}`,
												stepDescription: step.StepDescription || "",
												stepType: step.StepType || "Tooltip",
												stepId: step.StepId || getRandomID(),
												xpath: {
													value: step.ElementPath || "",
													PossibleElementPath: step.PossibleElementPath || "",
													position: { x: 0, y: 0 },
												},
												canvas: {
													position: step.Canvas?.Position || "absolute",
													autoposition: step.AutoPosition || false,
													xaxis: step.Position?.XAxisOffset || "1px",
													yaxis: step.Position?.YAxisOffset || "1px",
													width: step.Canvas?.Width || "300px",
													padding: step.Canvas?.Padding || "2px",
													borderRadius: step.Canvas?.BorderRadius || "8px",
													borderSize: step.Canvas?.BorderSize || "0px",
													borderColor: step.Canvas?.BorderColor || "transparent",
													backgroundColor: step.Canvas?.BackgroundColor || "#ffffff",
												},
												hotspots: step.Hotspot || HOTSPOT_DEFAULT_VALUE,
												design: {
													gotoNext: {
														NextStep: step.Design?.GotoNext?.NextStep || "",
														ButtonId: step.Design?.GotoNext?.ButtonId || "",
														elementPath: step.Design?.GotoNext?.elementPath || "",
														ButtonName: step.Design?.GotoNext?.ButtonName || "",
													},
													element: {
														progress: step.Tooltip?.EnableProgress ? "Template1" : "",
														isDismiss: step.Modal?.DismissOption || false,
														progressSelectedOption: step.Tooltip?.ProgressTemplate || 1,
														progressColor: step.Modal?.ProgressColor || "var(--primarycolor)",
													},
												},
											};
										});

										// Update tooltip metadata
										state.toolTipGuideMetaData = newTooltipMetadata;
									}
								}, 0);
							} else {
								// For manually created tooltips, use default containers
								state.toolTipGuideMetaData[0].containers = [
									{
										id: getRandomID(),
										type: "rte",
										placeholder: DEF_PLACEHOLDER_MSG,
										rteBoxValue: "",
									},
								];
							}
						}
					});
				},

				toolTipGuideMetaData: [
					{
						containers: [
							// {
							// 	id: getRandomID(),
							// 	type: "rte",
							// 	placeholder: DEF_PLACEHOLDER_MSG,
							// 	rteBoxValue: "",
							// },
						],
						stepName: "Step 1",
						currentStep: 1,
						stepId: "",
						stepDescription: "",
						stepType: "Tooltip",
						xpath: DEF_XPATH,
						id: crypto.randomUUID(),
						hotspots: HOTSPOT_DEFAULT_VALUE,
						canvas: CANVAS_DEFAULT_VALUE,
						design: {
							gotoNext: "",
							element: {
								progress: "",
								isDismiss: "",
							},
						},
					},
				],
				setAnnouncementJson: async (data) => {},
				setChecklistJson: async (data) => {},
				setBannerJson: async (data) => {},

				setCanvasSetting: (data) => {
					set((state) => {
						const { announcementJson, currentStep } = state;
						if (announcementJson?.GuideStep) {
							const stepIndex = announcementJson.GuideStep.findIndex((item) => item.stepName === currentStep);

							// Store the previous state for undo
							const stateBefore = stepIndex !== -1
								? deepClone(announcementJson.GuideStep[stepIndex].Canvas)
								: {};

							if (stepIndex === -1) {
								// Add new step if not present
								announcementJson.GuideStep.push({
									stepName: currentStep,
									LayoutPositions: {}, // Initialize empty LayoutPositions
									Canvas: data, // Apply the provided canvas data
								});
							} else {
								// Update existing step
								announcementJson.GuideStep[stepIndex].Canvas = {
									...announcementJson.GuideStep[stepIndex].Canvas,
									...data,
								};
							}

							state.isUnSavedChanges = stepIndex === -1 ? false : true;

							// Record the change for undo/redo
							const stateAfter = stepIndex !== -1
								? deepClone(announcementJson.GuideStep[stepIndex].Canvas)
								: data;

							recordChange(
								'CANVAS_UPDATE',
								`Updated canvas settings for Announcement step ${currentStep}`,
								stateBefore,
								stateAfter,
								`announcement_canvas_${currentStep}`,
								{
									guideType: 'Announcement',
									currentStep: currentStep
								}
							);
						}
					});
				},
				setChecklistCanvasSetting: (data) => {
					set((state) => {
						const { checklistJson, currentStep } = state;
						if (checklistJson?.GuideStep) {
							const stepIndex = checklistJson.GuideStep.findIndex((item) => item.stepName === currentStep);

							// Store the previous state for undo
							const stateBefore = stepIndex !== -1
								? deepClone(checklistJson.GuideStep[stepIndex].Canvas)
								: {};

							if (stepIndex === -1) {
								// Add new step if not present
								checklistJson.GuideStep.push({
									stepName: currentStep,
									LayoutPositions: {}, // Initialize empty LayoutPositions
									Canvas: data, // Apply the provided canvas data
								});
							} else {
								// Update existing step
								checklistJson.GuideStep[stepIndex].Canvas = {
									...checklistJson.GuideStep[stepIndex].Canvas,
									...data,
								};
							}

							state.isUnSavedChanges = stepIndex === -1 ? false : true;

							// Record the change for undo/redo
							const stateAfter = stepIndex !== -1
								? deepClone(checklistJson.GuideStep[stepIndex].Canvas)
								: data;

							recordChange(
								'CANVAS_UPDATE',
								`Updated canvas settings for Checklist step ${currentStep}`,
								stateBefore,
								stateAfter,
								`checklist_canvas_${currentStep}`,
								{
									guideType: 'Checklist',
									currentStep: currentStep
								}
							);
						}
					});
				},
				setBannerCanvasSetting: (data) => {
					set((state) => {
						const { bannerJson, currentStep } = state;
						if (bannerJson?.GuideStep) {
							const stepIndex = bannerJson.GuideStep.findIndex((item) => item.stepName === currentStep);

							// Store the previous state for undo
							const stateBefore = stepIndex !== -1
								? deepClone(bannerJson.GuideStep[stepIndex].Canvas)
								: {};

							if (stepIndex === -1) {
								// Add new step if not present
								bannerJson.GuideStep.push({
									stepName: currentStep,
									LayoutPositions: {}, // Initialize empty LayoutPositions
									Canvas: data, // Apply the provided canvas data
								});
							} else {
								// Update existing step
								bannerJson.GuideStep[stepIndex].Canvas = {
									...bannerJson.GuideStep[stepIndex].Canvas,
									...data,
								};
							}

							state.isUnSavedChanges = stepIndex === -1 ? false : true;

							// Record the change for undo/redo
							const stateAfter = stepIndex !== -1
								? deepClone(bannerJson.GuideStep[stepIndex].Canvas)
								: data;

							recordChange(
								'CANVAS_UPDATE',
								`Updated canvas settings for Banner step ${currentStep}`,
								stateBefore,
								stateAfter,
								`banner_canvas_${currentStep}`,
								{
									guideType: 'Banner',
									currentStep: currentStep
								}
							);
						}
					});
				},

				setHotspotSetting: async (data) => {
					set((state) => {
						if (state.announcementJson?.GuideStep) {
							const index = state.announcementJson.GuideStep.findIndex((item) => item.stepName === state.currentStep);

							// Store the previous state for undo
							const stateBefore = index !== -1
								? deepClone(state.announcementJson.GuideStep[index].LayoutPositions)
								: {};

							if (index === -1) {
								state.announcementJson.GuideStep.push({ stepName: state.currentStep, LayoutPositions: data });
							} else {
								state.announcementJson.GuideStep = state.announcementJson.GuideStep.map((item) => {
									return {
										...item,
										LayoutPositions: state.currentStep === item.stepName ? data : item.LayoutPositions,
									};
								});
							}

							state.isUnSavedChanges = true;

							// Record the change for undo/redo
							recordChange(
								'HOTSPOT_SETTING_UPDATE',
								`Updated hotspot settings for step ${state.currentStep}`,
								stateBefore,
								deepClone(data),
								`hotspot_setting_${state.currentStep}`,
								{
									guideType: 'Announcement',
									currentStep: state.currentStep
								}
							);
						}
					});
				},

				cloneButtonContainer: (containerId) => {
					set((state) => {
						const container = state.buttonsContainer.find((c) => c.id === containerId);
						if (container) {
							const newContainer = {
								id: crypto.randomUUID(),
								style: { ...container.style }, // Preserve the original style including backgroundColor
								buttons: container.buttons.map((button) => {
									// Define default values for actions if button.actions is undefined
									const defaultAction = {
										value: "close" as "close" | "Previous" | "open-url" | "Next",
										targetURL: "",
										tab: "same-tab" as "same-tab" | "new-tab", // Ensure tab is one of the valid types
									};

									// Safely clone button actions with type checks
									const action = button.actions
										? {
												value: button.actions.value as "close" | "Previous" | "open-url" | "Next",
												targetURL: button.actions.targetURL || "",
												tab: button.actions.tab || "same-tab",
												interaction: button.actions.interaction || null,
										  }
										: defaultAction;

									return {
										...button,
										id: crypto.randomUUID(), // Generate a new unique ID for the cloned button
										actions: action,
									};
								}),
							};

							state.buttonsContainer.push(newContainer); // Add the new container
							state.isUnSavedChanges = true;
						}
					});
				},

				updateButton: (containerId, buttonId, keyname, value) => {
					set((state) => {
						const container = state.buttonsContainer.find((c) => c.id === containerId);
						if (container) {
							const button = container.buttons.find((b) => b.id === buttonId) as TButton;
							if (button) {
								// Store the previous state for undo
								const stateBefore = deepClone(button);

								// @ts-ignore
								button[keyname] = value;
								state.isUnSavedChanges = true;

								// Record the change for undo/redo
								recordChange(
									'BUTTON_UPDATE',
									`Updated button ${keyname} for ${button.name}`,
									stateBefore,
									deepClone(button),
									buttonId,
									{
										containerId: containerId,
										buttonId: buttonId,
										keyname: keyname
									}
								);
							}
								// For AI announcements, also update the metadata containers
								if (state.createWithAI) {
									const currentStepIndex = state.currentStep - 1;
									const isAnnouncement = state.selectedTemplate === "Announcement" || state.selectedTemplateTour === "Announcement";
									const isTourAnnouncement = state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Announcement";

									if (isAnnouncement) {
										if (isTourAnnouncement) {
											// For Tour+Announcement, use toolTipGuideMetaData
											if (state.toolTipGuideMetaData[currentStepIndex]) {
												const buttonContainer = state.toolTipGuideMetaData[currentStepIndex].containers.find(
													(c: any) => c.type === "button" && c.id === containerId
												);

												if (buttonContainer) {
													const metaButton = buttonContainer.buttons.find((b: any) => b.id === buttonId);
													if (metaButton) {
														// @ts-ignore
														metaButton[keyname] = value;
													}
												}
											}
										} else {
											// For pure Announcements, use announcementGuideMetaData
											if (state.announcementGuideMetaData[currentStepIndex]) {
												const buttonContainer = state.announcementGuideMetaData[currentStepIndex].containers.find(
													(c: any) => c.type === "button" && c.id === containerId
												);

												if (buttonContainer) {
													const metaButton = buttonContainer.buttons.find((b: any) => b.id === buttonId);
													if (metaButton) {
														// @ts-ignore
														metaButton[keyname] = value;
													}
												}
											}
										}
										state.isUnSavedChanges = true;
									}
								
							}
						}
					});
				},

				updateContainer: (containerId, keyname, value) => {
					set((state) => {
						const container = state.buttonsContainer.find((c) => c.id === containerId);
						if (container) {
							if (keyname === "style") {
								// Handle style updates
								container.style = { ...container.style, ...value };
							} else if (keyname === "BackgroundColor") {
								// Handle BackgroundColor property
								container.BackgroundColor = value;
							} else {
								// Handle other properties
								// @ts-ignore
								container[keyname] = value;
							}
							state.isUnSavedChanges = true;
						}
					});
				},
				updateImageContainer: (
					containerId: string,
					keyname: keyof TImageContainer,
					value: TImageContainer[keyof TImageContainer]
				) => {
					set((state) => {
						const container = state.imagesContainer.find((c) => c.id === containerId);
						if (container) {
							// @ts-ignore
							container[keyname] = { ...container[keyname], ...value };
							state.isUnSavedChanges = true;
						}
					});
				},
				updateRTEContainer: (containerId, rteId, newText) => {
					set((state) => {
						const container = state.rtesContainer.find((item) => item.id === containerId);
						if (container) {
							const rte = container.rtes.find((rte) => rte.id === rteId);
							if (rte) {
								// Store the previous state for undo
								const stateBefore = deepClone(rte);

								rte.text = newText; // Update text of the specific RTE
								state.isUnSavedChanges = true;

								// Record the change for undo/redo
								recordChange(
									'TEXT_UPDATE',
									`Updated text content`,
									stateBefore,
									deepClone(rte),
									rteId,
									{
										containerId: containerId,
										rteId: rteId
									}
								);
							}
						}
					});
				},
				clearRteDetails: (containerId, rteId) => {
					set((state) => {
						const container = state.rtesContainer.find((item) => item.id === containerId);
						if (container) {
							container.rtes = container.rtes.filter((rte) => rte.id !== rteId);
							state.rtesContainer = state.rtesContainer.filter((item) => item.id !== containerId);
						}
					});
				},

				addNewButton: (button: Partial<TButton>, containerId = "") => {
					set((state) => {
						const newButton: TButton = {
							id: crypto.randomUUID(),
							name: button.name || "New Button",
							position: button.position || "center",
							type: button.type || "primary",
							isEditing: button.isEditing || false,
							index: button.index || 0,
							style: {
								backgroundColor: button.style?.backgroundColor || "#ffffff",
								borderColor: button.style?.borderColor || "transparent",
								color: button.style?.color || "#fff",
							},
							actions: {
								value: button.actions?.value || "close",
								targetURL: button.actions?.targetURL || "",
								tab: button.actions?.tab || "same-tab",
								interaction: button.actions?.interaction || null, // Ensure interaction is explicitly defined
							},
							survey: button.survey || null,
						};

						if (containerId) {
							state.buttonsContainer = state.buttonsContainer.map((item) => {
								const lastIndex = item.buttons[item.buttons.length - 1]?.index || 0;
								return {
									...item,
									buttons:
										item.id === containerId
											? [
													...item.buttons,
													{
														...newButton,
														name: `Button ${lastIndex + 2}`,
														index: lastIndex + 1,
													},
											  ]
											: item.buttons,
								};
							});
							// For AI announcements, also update the metadata containers
							if (state.createWithAI) {
								const currentStepIndex = state.currentStep - 1;
								const isAnnouncement = state.selectedTemplate === "Announcement" || state.selectedTemplateTour === "Announcement";
								const isTourAnnouncement = state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Announcement";

								if (isAnnouncement) {
									if (isTourAnnouncement) {
										// For Tour+Announcement, use toolTipGuideMetaData
										if (state.toolTipGuideMetaData[currentStepIndex]) {
											const buttonContainer = state.toolTipGuideMetaData[currentStepIndex].containers.find(
												(c: any) => c.type === "button" && c.id === containerId
											);

											if (buttonContainer) {
												const lastIndex = buttonContainer.buttons[buttonContainer.buttons.length - 1]?.index || 0;
												buttonContainer.buttons.push({
													...newButton,
													name: `Button ${lastIndex + 2}`,
													index: lastIndex + 1,
												});
											}
										}
									} else {
										// For pure Announcements, use announcementGuideMetaData
										if (state.announcementGuideMetaData[currentStepIndex]) {
											const buttonContainer = state.announcementGuideMetaData[currentStepIndex].containers.find(
												(c: any) => c.type === "button" && c.id === containerId
											);

											if (buttonContainer) {
												const lastIndex = buttonContainer.buttons[buttonContainer.buttons.length - 1]?.index || 0;
												buttonContainer.buttons.push({
													...newButton,
													name: `Button ${lastIndex + 2}`,
													index: lastIndex + 1,
												});
											}
										}
									}
								}
							
								const targetStep = state.currentStep - 1;
								const currentGuideStep = state.interactionData.GuideStep[targetStep];
								const buttonContainers = state.announcementGuideMetaData[targetStep].containers.filter(c => c.type === "button");
								if (buttonContainers.length > 0) {
									currentGuideStep.ButtonSection = buttonContainers.map((container: any) => ({
										Id: container.id,
										CustomButtons: container.buttons?.map((button: any) => ({
											ButtonStyle: button.type || "primary",
											ButtonName: button.name,
											Alignment: button.position || "center",
											ButtonId: button.id,
											BackgroundColor: container.style?.backgroundColor || "transparent",
											ButtonAction: {
												Action: button.actions?.value || button.action?.Action || "",
												ActionValue: button.actions?.tab || button.action?.ActionValue || "",
												TargetUrl: button.actions?.targetURL || button.action?.TargetUrl || "",
											},
											Padding: {
												Top: 0,
												Right: 0,
												Bottom: 0,
												Left: 0,
											},
											ButtonProperties: {
												Padding: 0,
												Width: 0,
												Font: 0,
												FontSize: 0,
												ButtonTextColor: button.style?.color || "#ffffff",
												ButtonBackgroundColor: button.style?.backgroundColor || "#5F9EA0",
												ButtonBorderColor: button.style?.borderColor || "#5F9EA0",
											},
										})) || [],
									}));
								}
							}
						} else {
							state.buttonsContainer.push({
								id: crypto.randomUUID(),
								buttons: [newButton],
								style: {
									backgroundColor: "transparent",
								},
							});
						}

						state.isUnSavedChanges = true;
					});
				},

				deleteButton: (containerId, buttonId) => {
					set((state) => {
						const isLastItem = state.buttonsContainer.find((item) => item.id === containerId);
						if (isLastItem?.buttons.length === 1) {
							state.isUnSavedChanges = true;
							state.buttonsContainer = state.buttonsContainer.filter((item) => item.id !== containerId);
						} else {
							state.isUnSavedChanges = true;
							state.buttonsContainer = state.buttonsContainer.map((item) => {
								return {
									...item,
									buttons: item.id === containerId ? item.buttons.filter((i) => i.id !== buttonId) : item.buttons,
								};
							});
						}
						// For AI announcements, also update the metadata containers
						if (state.createWithAI) {
							const currentStepIndex = state.currentStep - 1;
							const isAnnouncement = state.selectedTemplate === "Announcement" || state.selectedTemplateTour === "Announcement";
							const isTourAnnouncement = state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Announcement";

							if (isAnnouncement) {
								if (isTourAnnouncement) {
									// For Tour+Announcement, use toolTipGuideMetaData
									if (state.toolTipGuideMetaData[currentStepIndex]) {
										const buttonContainer = state.toolTipGuideMetaData[currentStepIndex].containers.find(
											(c: any) => c.type === "button" && c.id === containerId
										);

										if (buttonContainer) {
											if (buttonContainer.buttons.length === 1) {
												// Remove the entire container if it's the last button
												state.toolTipGuideMetaData[currentStepIndex].containers =
													state.toolTipGuideMetaData[currentStepIndex].containers.filter((c: any) => c.id !== containerId);
											} else {
												// Remove just the button
												buttonContainer.buttons = buttonContainer.buttons.filter((b: any) => b.id !== buttonId);
											}
										}
									}
								} else {
									// For pure Announcements, use announcementGuideMetaData
									if (state.announcementGuideMetaData[currentStepIndex]) {
										const buttonContainer = state.announcementGuideMetaData[currentStepIndex].containers.find(
											(c: any) => c.type === "button" && c.id === containerId
										);

										if (buttonContainer) {
											if (buttonContainer.buttons.length === 1) {
												// Remove the entire container if it's the last button
												state.announcementGuideMetaData[currentStepIndex].containers =
													state.announcementGuideMetaData[currentStepIndex].containers.filter((c: any) => c.id !== containerId);
											} else {
												// Remove just the button
												buttonContainer.buttons = buttonContainer.buttons.filter((b: any) => b.id !== buttonId);
											}
										}
									}
								}
							}
						}
					});
				},
				deleteButtonContainer: (containerId) => {
					set((state) => {
						// Only set unsaved changes if not returning from preview mode or navigating between steps
						if (!state.isReturningFromPreview && !state.isNavigatingBetweenSteps) {
							state.isUnSavedChanges = true;
						}
						state.buttonsContainer = state.buttonsContainer.filter((item) => item.id !== containerId);
					});
					// For AI announcements, also update the metadata containers
					if (state.createWithAI) {
						const currentStepIndex = state.currentStep - 1;
						const isAnnouncement = state.selectedTemplate === "Announcement" || state.selectedTemplateTour === "Announcement";
						const isTourAnnouncement = state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Announcement";

						if (isAnnouncement) {
							if (isTourAnnouncement) {
								// For Tour+Announcement, use toolTipGuideMetaData
								if (state.toolTipGuideMetaData[currentStepIndex]) {
									state.toolTipGuideMetaData[currentStepIndex].containers =
										state.toolTipGuideMetaData[currentStepIndex].containers.filter((c: any) => c.id !== containerId);
								}
							} else {
								// For pure Announcements, use announcementGuideMetaData
								if (state.announcementGuideMetaData[currentStepIndex]) {
									state.announcementGuideMetaData[currentStepIndex].containers =
										state.announcementGuideMetaData[currentStepIndex].containers.filter((c: any) => c.id !== containerId);
								}
							}
						}
					}
				},

				setSettingAnchorEl: (data) => {
					set((state) => {
						state.settingAnchorEl = data;
					});
				},
				setTooltipBtnSettingAnchorEl: (data) => {
					set((state) => {
						state.tooltipBtnSettingAnchorEl = data;
					});
				},
				setImageAnchorEl: (data) => {
					set((state) => {
						state.imageAnchorEl = data;
					});
				},
				setSettingAnchorElNew: (data) => {
					set((state) => {
						state.settingAnchorElNew = data;
					});
				},
				setRTEAnchorEl: (data) => {
					set((state) => {
						state.rteAnchorEl = data;
					});
				},

				selectedActions: "close",
				setSelectActions: (data) => {
					set((state) => {
						state.selectedActions = data;
					});
				},
				targetURL: "",
				setTargetURL: (data) => {
					set((state) => {
						state.targetURL = data;
					});
				},
				selectedInteraction: "",
				setSelectedInteraction: (data) => {
					set((state) => {
						state.selectedInteraction = data;
					});
				},
				openInteractionList: false,
				setOpenInteractionList: (data) => {
					set((state) => {
						state.openInteractionList = data;
					});
				},
				selectedTab: "new-tab",
				setSelectedTab: (data) => {
					set((state) => {
						state.selectedTab = data;
					});
				},
				currentButtonName: "",
				setCurrentButtonName: (data) => {
					set((state) => {
						state.currentButtonName = data;
					});
				},
				updateButtonAction: (containerId, buttonId, value) => {
					set((state) => {
						const container = state.buttonsContainer.find((c) => c.id === containerId);
						if (container) {
							const button = container.buttons.find((b) => b.id === buttonId) as TButton;
							if (button) {
								// Only set unsaved changes if not returning from preview mode or navigating between steps
								if (!state.isReturningFromPreview && !state.isNavigatingBetweenSteps) {
									state.isUnSavedChanges = true;
								}
								button["actions"] = value;
							}
								// For AI announcements and banners, also update the metadata containers
								if (state.createWithAI) {
									const currentStepIndex = state.currentStep - 1;
									const isAnnouncement = state.selectedTemplate === "Announcement" || state.selectedTemplateTour === "Announcement";
									const isBanner = state.selectedTemplate === "Banner" || state.selectedTemplateTour === "Banner";
									const isTourAnnouncement = state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Announcement";
									const isTourBanner = state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Banner";

									if (isAnnouncement) {
										if (isTourAnnouncement) {
											// For Tour+Announcement, use toolTipGuideMetaData
											if (state.toolTipGuideMetaData[currentStepIndex]) {
												const buttonContainer = state.toolTipGuideMetaData[currentStepIndex].containers.find(
													(c: any) => c.type === "button" && c.id === containerId
												);

												if (buttonContainer) {
													const metaButton = buttonContainer.buttons.find((b: any) => b.id === buttonId);
													if (metaButton) {
														metaButton["actions"] = value;
													}
												}
											}
										} else {
											// For pure Announcements, use announcementGuideMetaData
											if (state.announcementGuideMetaData[currentStepIndex]) {
												const buttonContainer = state.announcementGuideMetaData[currentStepIndex].containers.find(
													(c: any) => c.type === "button" && c.id === containerId
												);

												if (buttonContainer) {
													const metaButton = buttonContainer.buttons.find((b: any) => b.id === buttonId);
													if (metaButton) {
														metaButton["actions"] = value;
													}
												}
											}
										}
									} else if (isBanner) {
										// For Banner steps (both standalone and tour), use toolTipGuideMetaData
										if (state.toolTipGuideMetaData[currentStepIndex]) {
											const buttonContainer = state.toolTipGuideMetaData[currentStepIndex].containers.find(
												(c: any) => c.type === "button" && c.id === containerId
											);

											if (buttonContainer) {
												const metaButton = buttonContainer.buttons.find((b: any) => b.id === buttonId);
												if (metaButton) {
													metaButton["actions"] = value;
												}
											}
										}
									}
								}
							}
						
					});
				},
				updateTooltipButtonAction: (containerId, buttonId, value) => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						const containerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
							(c) => c.id === containerId
						);

						// Also update the buttonsContainer to ensure consistency
						const buttonContainer = state.buttonsContainer.find((c) => c.id === containerId);
						if (buttonContainer) {
							const button = buttonContainer.buttons.find((b) => b.id === buttonId);
							if (button) {
								button.actions = {
									value: value.value || "close",
									targetURL: value.targetURL || "",
									tab: value.tab || "same-tab",
									interaction: value.interaction || null,
								};
							}
						}
						if (containerIndex !== -1) {
							//@ts-ignore
							const buttonIndex = state.toolTipGuideMetaData[targetStep].containers[containerIndex].buttons.findIndex(
								(b: { id: string }) => b.id === buttonId
							);
							if (buttonIndex !== -1) {
								state.isUnSavedChanges = containerIndex === -1 ? false : true;
								//@ts-ignore

								state.toolTipGuideMetaData[targetStep].containers[containerIndex].buttons[buttonIndex]["actions"] =
									value;

								// Synchronize AI tooltip data when button action is updated
								if (state.createWithAI && state.selectedTemplate === "Tooltip") {
									if (state.interactionData?.GuideStep?.[targetStep]) {
										const currentGuideStep = state.interactionData.GuideStep[targetStep];

										// Update ButtonSection from button containers
										const buttonContainers = state.toolTipGuideMetaData[targetStep].containers.filter(c => c.type === "button");
										if (buttonContainers.length > 0) {
											currentGuideStep.ButtonSection = buttonContainers.map((container: any) => ({
												Id: container.id,
												CustomButtons: container.buttons?.map((button: any) => ({
													ButtonStyle: button.type || "primary",
													ButtonName: button.name,
													Alignment: button.position || "center",
													ButtonId: button.id,
													BackgroundColor: container.style?.backgroundColor || "transparent",
													ButtonAction: {
														Action: button.actions?.value || "close",
														TargetUrl: button.actions?.targetURL || "",
														ActionValue: button.actions?.tab || "same-tab"
													},
													Padding: {
														Top: 0,
														Right: 0,
														Bottom: 0,
														Left: 0,
													},
													ButtonProperties: {
														Padding: 0,
														Width: 0,
														Font: 0,
														FontSize: 0,
														ButtonTextColor: button.style?.color || "#ffffff",
														ButtonBackgroundColor: button.style?.backgroundColor || "#5F9EA0",
														ButtonBorderColor: button.style?.borderColor || "#5F9EA0",
													}
												})) || [],
											}));
										}
									}
								}

								// Synchronize AI tour data when button action is updated for tooltip steps in tours
								if (state.createWithAI && state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Tooltip") {
									if (state.interactionData?.GuideStep?.[targetStep]) {
										const currentGuideStep = state.interactionData.GuideStep[targetStep];

										// Update ButtonSection from button containers
										const buttonContainers = state.toolTipGuideMetaData[targetStep].containers.filter(c => c.type === "button");
										if (buttonContainers.length > 0) {
											currentGuideStep.ButtonSection = buttonContainers.map((container: any) => ({
												Id: container.id,
												CustomButtons: container.buttons?.map((button: any) => ({
													ButtonStyle: button.type || "primary",
													ButtonName: button.name,
													Alignment: button.position || "center",
													ButtonId: button.id,
													BackgroundColor: container.style?.backgroundColor || "transparent",
													ButtonAction: {
														Action: button.actions?.value || "close",
														TargetUrl: button.actions?.targetURL || "",
														ActionValue: button.actions?.tab || "same-tab"
													},
													Padding: {
														Top: 0,
														Right: 0,
														Bottom: 0,
														Left: 0,
													},
													ButtonProperties: {
														Padding: 0,
														Width: 0,
														Font: 0,
														FontSize: 0,
														ButtonTextColor: button.style?.color || "#ffffff",
														ButtonBackgroundColor: button.style?.backgroundColor || "#5F9EA0",
														ButtonBorderColor: button.style?.borderColor || "#5F9EA0",
													}
												})) || [],
											}));
										}

										console.log("Updated button actions for AI tour tooltip step", targetStep, {
											ButtonSection: currentGuideStep.ButtonSection
										});
									}
								}
							}
						}

						// For non-AI guides, ensure button data is immediately synchronized
						if (!state.createWithAI) {
							console.log("🔄 Syncing button data after button action update");
							state.syncButtonContainerToMetadata();
						}
					});
				},
				updateButtonInteraction: (containerId, buttonId, value) => {
					set((state) => {
						const container = state.buttonsContainer.find((c) => c.id === containerId);
						if (container) {
							const button = container.buttons.find((b) => b.id === buttonId) as TButton;
							if (button) {
								// Only set unsaved changes if not returning from preview mode or navigating between steps
								if (!state.isReturningFromPreview && !state.isNavigatingBetweenSteps) {
									state.isUnSavedChanges = true;
								}
								button["survey"] = value;
							}
								// For AI announcements and banners, also update the metadata containers
								if (state.createWithAI) {
									const currentStepIndex = state.currentStep - 1;
									const isAnnouncement = state.selectedTemplate === "Announcement" || state.selectedTemplateTour === "Announcement";
									const isBanner = state.selectedTemplate === "Banner" || state.selectedTemplateTour === "Banner";
									const isTourAnnouncement = state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Announcement";
									const isTourBanner = state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Banner";

									if (isAnnouncement) {
										if (isTourAnnouncement) {
											// For Tour+Announcement, use toolTipGuideMetaData
											if (state.toolTipGuideMetaData[currentStepIndex]) {
												const buttonContainer = state.toolTipGuideMetaData[currentStepIndex].containers.find(
													(c: any) => c.type === "button" && c.id === containerId
												);

												if (buttonContainer) {
													const metaButton = buttonContainer.buttons.find((b: any) => b.id === buttonId);
													if (metaButton) {
														metaButton["survey"] = value;
													}
												}
											}
										} else {
											// For pure Announcements, use announcementGuideMetaData
											if (state.announcementGuideMetaData[currentStepIndex]) {
												const buttonContainer = state.announcementGuideMetaData[currentStepIndex].containers.find(
													(c: any) => c.type === "button" && c.id === containerId
												);

												if (buttonContainer) {
													const metaButton = buttonContainer.buttons.find((b: any) => b.id === buttonId);
													if (metaButton) {
														metaButton["survey"] = value;
													}
												}
											}
										}
									} else if (isBanner) {
										// For Banner steps (both standalone and tour), use toolTipGuideMetaData
										if (state.toolTipGuideMetaData[currentStepIndex]) {
											const buttonContainer = state.toolTipGuideMetaData[currentStepIndex].containers.find(
												(c: any) => c.type === "button" && c.id === containerId
											);

											if (buttonContainer) {
												const metaButton = buttonContainer.buttons.find((b: any) => b.id === buttonId);
												if (metaButton) {
													metaButton["survey"] = value;
												}
											}
										}
									}
								}
							}
						
					});
				},
				updateTooltipButtonInteraction: (containerId, buttonId, value) => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						const containerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
							(c) => c.id === containerId
						);
						if (containerIndex !== -1) {
							//@ts-ignore
							const buttonIndex = state.toolTipGuideMetaData[targetStep].containers[containerIndex].buttons.findIndex(
								(b: { id: string }) => b.id === buttonId
							);
							if (buttonIndex !== -1) {
								state.isUnSavedChanges = containerIndex === -1 ? false : true;
								//@ts-ignore

								state.toolTipGuideMetaData[targetStep].containers[containerIndex].buttons[buttonIndex]["survey"] =
									value;
							}
						}
					});
				},
				getCurrentButtonInfo: (containerId: string, buttonId: string) => {
					let buttonInfo = get()
						.buttonsContainer.find((item) => item.id === containerId)
						?.buttons.find((btn) => btn.id === buttonId);

					// If not found, try to find it in the toolTipGuideMetaData
					if (!buttonInfo) {
						const targetStep = get().currentStep - 1;
						if (get().toolTipGuideMetaData && get().toolTipGuideMetaData[targetStep]) {
							const container = get().toolTipGuideMetaData[targetStep].containers.find((c) => c.id === containerId);
							if (container && container.buttons) {
								// @ts-ignore
								buttonInfo = container.buttons.find((b) => b.id === buttonId);
							}
						}
					}

					if (!buttonInfo) {
						const targetStep = get().currentStep - 1;
						if (get().announcementGuideMetaData && get().announcementGuideMetaData[targetStep]) {
							const container = get().announcementGuideMetaData[targetStep].containers.find((c) => c.id === containerId);
							if (container && container.buttons) {
								// @ts-ignore
								buttonInfo = container.buttons.find((b) => b.id === buttonId);
							}
						}
					}

					// Extract color from border string if it contains CSS border syntax
					const extractColorFromBorder = (borderValue: string) => {
						if (!borderValue) return "#70afaf"; // Default border color
						// If it's already just a color (starts with #), return it
						if (borderValue.startsWith("#")) return borderValue;
						// If it contains CSS border syntax like "1px solid #5F9EA0", extract the color
						const colorMatch = borderValue.match(/#[0-9A-Fa-f]{6}/);
						return colorMatch ? colorMatch[0] : "#70afaf"; // Default to #70afaf if no color found
					};

					return {
						bgColor: buttonInfo?.style?.backgroundColor || "#5F9EA0",
						borderColor: extractColorFromBorder(buttonInfo?.style?.borderColor || "#70afaf"),
						textColor: buttonInfo?.style?.color || "#ffffff",
						title: buttonInfo?.name || "Button 1",
						selectedActions: buttonInfo?.actions?.value || "close", // Default to "close" if no action exists
						targetURL: buttonInfo?.actions?.targetURL || "",
						value: buttonInfo?.actions?.value || "close",
						tab: buttonInfo?.actions?.tab || "same-tab", // Include tab information
					};
				},

				getGuildeListByOrg: async (orgId) => {
					const res = await getAllGuideByOrgId("");
					set((state) => {
						if (!res) {
							state.guideListByOrg = [];
						} else {
							const data = res?.map((item: any) => {
								return {
									title: item.Name,
									type: item.GuideType,
									orgId: item.OrganizationId,
									targetUrl: item.TargetUrl,
									guideId: item.GuideId,
								};
							});
							state.guideListByOrg = data;
						}
					});
				},
				uploadImage: (containerId: string, imageData: TImages) => {
					set((state) => {
						const container = state.imagesContainer.find((item) => item.id === containerId);
						if (container) {
							state.isUnSavedChanges = true;
							state.imagesContainer = state.imagesContainer.map((item: TImageContainer) => {
								if (item.id === containerId) {
									return {
										...item,
										images: [{ ...imageData, url: imageData.url }],
									};
								}
								return item;
							});
							// For AI guides, also update the metadata containers
							if (state.createWithAI) {
								const currentStepIndex = state.currentStep - 1;
								const isAnnouncement = state.selectedTemplate === "Announcement" || state.selectedTemplateTour === "Announcement";
								const isTourAnnouncement = state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Announcement";

								if (isAnnouncement) {
									// Update announcement metadata containers
									if (isTourAnnouncement) {
										// For Tour+Announcement, use toolTipGuideMetaData
										if (state.toolTipGuideMetaData[currentStepIndex]) {
											const existingImageContainer = state.toolTipGuideMetaData[currentStepIndex].containers.find(
												(c: any) => c.type === "image" && c.id === containerId
											);

											if (existingImageContainer) {
												// Update existing image container
												existingImageContainer.images = [{
													id: imageData.id,
													url: imageData.url,
													altText: imageData.altText,
													backgroundColor: imageData.backgroundColor,
													objectFit: imageData.objectFit
												}];
											} else {
												// Add new image container to tooltip metadata
												state.toolTipGuideMetaData[currentStepIndex].containers.push({
													id: containerId,
													type: "image",
													images: [{
														id: imageData.id,
														url: imageData.url,
														altText: imageData.altText,
														backgroundColor: imageData.backgroundColor,
														objectFit: imageData.objectFit
													}],
													style: {
														backgroundColor: "transparent",
														height: IMG_CONTAINER_DEFAULT_HEIGHT,
													},
												});
											}
										}
									} else {
										// For pure Announcements, use announcementGuideMetaData
										if (state.announcementGuideMetaData[currentStepIndex]) {
											const existingImageContainer = state.announcementGuideMetaData[currentStepIndex].containers.find(
												(c: any) => c.type === "image" && c.id === containerId
											);

											if (existingImageContainer) {
												// Update existing image container
												existingImageContainer.images = [{
													id: imageData.id,
													url: imageData.url,
													altText: imageData.altText,
													backgroundColor: imageData.backgroundColor,
													objectFit: imageData.objectFit
												}];
											} else {
												// Add new image container to announcement metadata
												state.announcementGuideMetaData[currentStepIndex].containers.push({
													id: containerId,
													type: "image",
													images: [{
														id: imageData.id,
														url: imageData.url,
														altText: imageData.altText,
														backgroundColor: imageData.backgroundColor,
														objectFit: imageData.objectFit
													}],
													style: {
														backgroundColor: "transparent",
														height: IMG_CONTAINER_DEFAULT_HEIGHT,
													},
												});
											}
										}
									}

									// Also update interactionData immediately to ensure consistency
									if (state.interactionData?.GuideStep?.[currentStepIndex]) {
										const currentGuideStep = state.interactionData.GuideStep[currentStepIndex];

										// Find existing ImageProperties or create new one
										if (!currentGuideStep.ImageProperties) {
											currentGuideStep.ImageProperties = [];
										}

										const existingImageProperty = currentGuideStep.ImageProperties.find((prop: any) => prop.Id === containerId);
										if (existingImageProperty) {
											// Update existing ImageProperty
											existingImageProperty.CustomImage = [{
												Url: imageData.url,
												AltText: imageData.altText,
											}];
										} else {
											// Add new ImageProperty
											currentGuideStep.ImageProperties.push({
												Id: containerId,
												CustomImage: [{
													Url: imageData.url,
													AltText: imageData.altText,
												}],
											});
										}

										console.log("Updated interactionData ImageProperties after image upload:", currentGuideStep.ImageProperties);
									}
								}
							}
						}
					});
				},
				replaceImage: (containerId: string, imageId: string, imageData: TImages[keyof TImages]) => {
					set((state) => {
						const container = state.imagesContainer.find((item) => item.id === containerId);
						if (container) {
							const image = container.images.find((item) => item.id === imageId);
							if (image) {
								state.isUnSavedChanges = true;
								state.imagesContainer = state.imagesContainer.map((item: TImageContainer) => {
									return {
										...item,
										images:
											item.id === containerId
												? item.images.map((imgItem) => {
														return imgItem.id === image.id ? imageData : imgItem;
												  })
												: item.images,
									};
								});
							}
						}
					});
				},
				cloneImageContainer: async (containerId: string) => {
					set((state) => {
						const container = state.imagesContainer.find((c) => c.id === containerId);
						if (container) {
							state.isUnSavedChanges = true;
							const newContainer = {
								id: crypto.randomUUID(),
								style: {
									backgroundColor: container.style.backgroundColor,
									height: IMG_CONTAINER_DEFAULT_HEIGHT,
									paddingLeft: container.style.paddingLeft,
									paddingTop: container.style.paddingTop,
									paddingRight: container.style.paddingRight,
									paddingBottom: container.style.paddingBottom,
									maxHeight: container.style.maxHeight,
								},
								images: container.images.map((imgItem) => ({
									...imgItem,
									id: crypto.randomUUID(),
								})),
							};
							state.imagesContainer.push(newContainer);
						}
					});
				},
				cloneRTEContainer: async (containerId: string) => {
					set((state) => {
						const container = state.rtesContainer.find((c) => c.id === containerId);
						if (container) {
							state.isUnSavedChanges = true;
							const newContainer = {
								id: crypto.randomUUID(),
								style: {
									backgroundColor: container.style.backgroundColor,
								},
								rtes: container.rtes.map((rteItem) => ({
									...rteItem,
									id: crypto.randomUUID(),
								})),
							};
							state.rtesContainer.push(newContainer);
						}
					});
				},
				deleteImageContainer: async (containerId: string) => {
					set((state) => {
						state.isUnSavedChanges = true;
						state.imagesContainer = state.imagesContainer.filter((item) => item.id !== containerId);
					});
				},
				deleteRTEContainer: async (containerId: string) => {
					set((state) => {
						state.isUnSavedChanges = true;
						state.rtesContainer = state.rtesContainer.filter((item) => item.id !== containerId);
					});
				},

				addNewImageContainer: () => {
					set((state) => {
						state.isUnSavedChanges = true;
						state.imagesContainer.push({
							id: crypto.randomUUID(),
							images: [],
							style: {
								backgroundColor: "transparent",
								height: IMG_CONTAINER_DEFAULT_HEIGHT,
								paddingLeft: 0,
								paddingTop: 0,
								paddingRight: 0,
								paddingBottom: 0,
								maxHeight: 0,
							},
						});
					});
				},
				// When adding or updating an RTE section
				addNewRTEContainer: () => {
					set((state) => {
						const newContainer = {
							id: crypto.randomUUID(), // Unique container ID
							rtes: [
								{
									id: crypto.randomUUID(), // Ensure each RTE has a unique ID
									text: "", // Initialize text if needed
								},
							],
							style: { backgroundColor: "#f0f0f0" },
						};
						state.rtesContainer.push(newContainer);
					});
				},
				updateImageContainerOnReload: (data: any[], mode: "submit" | "reset" = "submit") => {
					set((state) => {
						if (mode === "reset") {
							state.imagesContainer = [IMG_CONT_DEF_VALUE];
						} else {
							state.imagesContainer = data.map((item) => {
								const newImage = {
									id: crypto.randomUUID(), // Add unique ID for each image
									url: item.Url,
									altText: item.AltText,
									backgroundColor: item.BackgroundColor,
									objectFit: item.Fit,
								};
								return {
									id: crypto.randomUUID(), // Unique ID for the image container
									images: [newImage],
									style: {
										backgroundColor: item.BackgroundColor,
										height: IMG_CONTAINER_DEFAULT_HEIGHT,
										paddingLeft: 0,
										paddingTop: 0,
										paddingRight: 0,
										paddingBottom: 0,
										maxHeight: 0,
									},
								};
							});
						}
					});
				},
				updateRTEContainerOnReload: (data: any[], mode: "submit" | "reset" = "submit") => {
					set((state) => {
						if (mode === "reset") {
							state.rtesContainer = [RTE_CONT_DEF_VALUE]; // Reset to default
						} else if (Array.isArray(data)) {
							state.rtesContainer = data.map((item) => ({
								id: crypto.randomUUID(), // Unique container ID
								rtes: [
									{
										id: crypto.randomUUID(), // Unique RTE ID
										text: item.Text, // Safely handle text
									},
								],
								style: item.style || {
									backgroundColor: "#f0f0f0",
								},
							}));
						} else {
							console.error("Invalid data format for updateRTEContainerOnReload:", data);
						}
					});
				},

				updateButtonContainerOnReload: (params: any[], mode: "submit" | "reset" = "submit") => {
					if (!Array.isArray(params)) {
						console.error("Expected an array, received:", params);
						return; // Exit early if params is not an array
					}

					set((state) => {
						if (mode === "reset") {
							state.buttonsContainer = BUTTON_CONT_DEF_VALUE;
						} else {
							const groupedButtons = params.reduce((acc: Record<string, any[]>, button: any) => {
								const containerId = button.ContainerId || "default";
								if (!acc[containerId]) {
									acc[containerId] = [];
								}
								acc[containerId].push(button);
								return acc;
							}, {});

							state.buttonsContainer = Object.entries(groupedButtons).map(([containerId, buttons]) => ({
								id: containerId,
								buttons: buttons.map((button: any) => ({
									id: crypto.randomUUID(),
									name: button.ButtonName || "Default Name",
									position: button.Alignment || "center",
									type: button.ButtonStyle || "primary",
									backgroundColor: button.BackgroundColor || "transparent",
									isEditing: false,
									index: 0,
									style: {
										backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || "#5f9ea0",
										borderColor: button.ButtonProperties?.ButtonBorderColor || "#5f9ea0",
										color: button.ButtonProperties?.ButtonTextColor || "#ffffff",
									},
									actions: {
										tab: button.ButtonAction?.ActionValue || "same-tab",
										value: button.ButtonAction?.Action || "close",
										targetURL: button.ButtonAction?.TargetUrl || "",
									},
									survey: null,
								})),
								style: {
									backgroundColor: groupedButtons[containerId][0]?.BackgroundColor,
								},
							}));
						}
					});
				},
				toggleFit: (containerId, imageId, mode) => {
					set((state) => {
						const container = state.imagesContainer.find((c) => c.id === containerId);
						if (container) {
							const image = container.images.find((b) => b.id === imageId) as TImages;
							if (image) {
								state.isUnSavedChanges = true;
								image["objectFit"] = mode === "Fill" ? "cover" : "contain";
							}
						}
					});
				},
				cleanupDuplicateSteps: () => {
					set((state) => {
						// Find and remove duplicate steps in the steps array
						const uniqueSteps: any[] = [];
						const stepIds = new Set<string>();
						const stepNames = new Map<string, number>(); // Track step names and their counts

						// First pass: identify unique steps by ID and track step names
						state.steps.forEach(step => {
							if (!stepIds.has(step.id)) {
								stepIds.add(step.id);

								// Track step names for potential duplicates
								const count = stepNames.get(step.name) || 0;
								stepNames.set(step.name, count + 1);

								uniqueSteps.push(step);
							}
						});

						// Second pass: handle duplicate step names by renaming them
						uniqueSteps.forEach((step, index) => {
							const nameCount = stepNames.get(step.name) || 0;
							if (nameCount > 1) {
								// If there are multiple steps with the same name, ensure they have unique stepCount values
								step.stepCount = index + 1;
							}
						});

						// Update the steps array with unique steps
						state.steps = uniqueSteps;

						// Clean up toolTipGuideMetaData to match the steps
						const uniqueMetadata: any[] = [];
						const metadataIds = new Set<string>();
						const metadataByStepId = new Map();

						// Group metadata by stepId for easier matching
						state.toolTipGuideMetaData.forEach(metadata => {
							if (metadata.stepId) {
								metadataByStepId.set(metadata.stepId, metadata);
							}
						});

						// Match metadata to steps
						uniqueSteps.forEach((step, index) => {
							// Try to find metadata for this step
							let metadata = metadataByStepId.get(step.id);

							// If no metadata found by ID, try to find by name
							if (!metadata) {
								metadata = state.toolTipGuideMetaData.find(m =>
									m.stepName === step.name && !metadataIds.has(m.id)
								);
							}

							// If metadata found, add it to the unique list
							if (metadata && !metadataIds.has(metadata.id)) {
								metadataIds.add(metadata.id);

								// Update metadata to match step
								metadata.stepId = step.id;
								metadata.stepName = step.name;
								metadata.currentStep = index + 1;

								uniqueMetadata.push(metadata);
							} else {
								// If no metadata found, create a new one
								const newMetadata = {
									containers: [
										{
											id: getRandomID(),
											type: "rte",
											placeholder: DEF_PLACEHOLDER_MSG,
											rteBoxValue: "",
										}
									],
									stepName: step.name,
									currentStep: index + 1,
									stepId: step.id,
									stepDescription: step.stepDescription || "",
									stepType: step.stepType || "Announcement",
									xpath: DEF_XPATH,
									id: crypto.randomUUID(),
									hotspots: HOTSPOT_DEFAULT_VALUE,
									canvas: CANVAS_DEFAULT_VALUE,
									design: {
										gotoNext: {
											NextStep: "",
											ButtonId: "",
											elementPath: "",
											ButtonName: "",
										},
										element: {
											isDismiss: false,
											progress: "",
										},
									},
								};
								uniqueMetadata.push(newMetadata);
							}
						});

						// Replace toolTipGuideMetaData with unique entries
						state.toolTipGuideMetaData = uniqueMetadata;
					});
				},

				setIsUnSavedChanges: (data) => {
					set((state) => {
						// Prevent setting isUnSavedChanges to true when returning from preview mode
						// or when navigating between steps
						// This ensures the save button doesn't flicker during sync operations
						if (data === true && (state.isReturningFromPreview || state.isNavigatingBetweenSteps)) {
							return; // Skip setting to true when returning from preview or navigating between steps
						}
						state.isUnSavedChanges = data as boolean;
					});
				},
				dismissData: true,
				setDismissData: (data) =>
					set((state) => {
						// Store the previous state for undo
						const stateBefore = {
							dismiss: state.dismiss,
							dismissData: deepClone(state.dismissData || {}),
							progress: state.progress,
							selectedOption: state.selectedOption
						};

						// Update the state
						state.dismissData = data;

						// Record the change for undo/redo
						recordChange(
							'ELEMENT_UPDATE',
							`Updated dismiss data`,
							stateBefore,
							{
								dismiss: state.dismiss,
								dismissData: deepClone(data),
								progress: state.progress,
								selectedOption: state.selectedOption
							},
							`element_settings`,
							{
								guideType: state.selectedTemplate || state.selectedTemplateTour,
								currentStep: state.currentStep,
								progress: state.progress,
								selectedOption: state.selectedOption,
								dismiss: state.dismiss
							}
						);
					}),
				createWithAI: false,
				setCreateWithAI: (createWithAI) =>
					set((state) => {
						state.createWithAI = createWithAI;
					}),
				isAIGuidePersisted: false,
				setIsAIGuidePersisted: (persisted) =>
					set((state) => {
						state.isAIGuidePersisted = persisted;
					}),
				interactionData: null,
				setInteractionData: (data) =>
					set((state) => {
						state.interactionData = data;
					}),
				overlayEnabled: true,
				setOverlayEnabled: (enabled, skipMutualExclusivity = false) =>
					set((state) => {
						// Store the previous state for undo
						const stateBefore = {
							overlayEnabled: state.overlayEnabled,
							pageinteraction: state.pageinteraction
						};

						// Update the state
						state.overlayEnabled = enabled;

						// Only enforce mutual exclusivity if not in loading mode
						if (!skipMutualExclusivity) {
							// Enforce mutual exclusivity: when overlay is enabled, page interaction must be disabled and vice versa
							state.pageinteraction = !enabled;
						}

						// For announcements, automatically sync global overlay state to all steps
						if (state.selectedTemplate === "Announcement") {
							state.syncGlobalOverlayStateForAnnouncements();
						}

						// Record the change for undo/redo (only if not in loading mode)
						if (!skipMutualExclusivity) {
							recordChange(
								'OVERLAY_UPDATE',
								`Updated overlay settings`,
								stateBefore,
								{ overlayEnabled: enabled, pageinteraction: state.pageinteraction },
								`overlay_settings`,
								{
									guideType: state.selectedTemplate || state.selectedTemplateTour,
									currentStep: state.currentStep
								}
							);
						}
					}),
				clearGuideDetails: () => {
					set((state) => {
						// Don't clear these states if extension is just being closed without logout
						// if (!state.isExtensionClosed) {
						// 	state.activeMenu = null;
						// 	state.searchText = "";
						// 	state.isPopupOpen = false;
						// 	state.drawerActiveMenu = null;
						// 	state.drawerSearchText = "";
						// }
						// ... rest of the clear states ...

						return {
							...state, // Preserve any other state values if needed
							isUnSavedChanges: false,
							openWarning: false, // Reset openWarning when clearing guide details
							ProgressColor:"var(--primarycolor)",
							pulseAnimationsH: true,
							currentStep: 1,
							announcementJson: {
								GuideStep: [],
							},
							bannerJson: {
								GuideStep: [],
							},
							tooltipWidth: "300px",
							tooltipXaxis: "4",
							tooltipYaxis: "4",
							tooltipCount: 0,
							guideListByOrg: [],
							designPopup: false,
							settingAnchorEl: {
								containerId: "",
								buttonId: "",
								value: null,
							},
							tooltipBtnSettingAnchorEl: {
								containerId: "",
								buttonId: "",
								value: null,
							},
							imageAnchorEl: {
								containerId: "",
								buttonId: "",
								value: null,
							},
							imagesContainer: [
								{
									id: crypto.randomUUID(),
									images: [
										    {
												id: crypto.randomUUID(),
												url: defaultAnnouncementImages[Math.floor(Math.random() * defaultAnnouncementImages.length)],
												altText: "Default Announcement Image",
												backgroundColor: "#ffffff",
												objectFit: "contain",
											},
									],
									style: {
										backgroundColor: "transparent",
										height: IMG_CONTAINER_DEFAULT_HEIGHT,
										paddingLeft: 0,
										paddingTop: 0,
										paddingRight: 0,
										paddingBottom: 0,
										maxHeight: 0,
									},
									hyperlink: undefined,
								},
							],
							rtesContainer: [
								{
									id: crypto.randomUUID(),
									rtes: [
										{
											id: crypto.randomUUID(), // Ensure each RTE has a unique ID
											text: "", // Initialize text if needed
										},
									],
									style: {
										backgroundColor: "#f0f0f0",
									},
								},
							],
							buttonsContainer: BUTTON_CONT_DEF_VALUE,
							steps: [
								{
									id: crypto.randomUUID(),
									name: "Step 1",
									stepCount: 1,
									stepType: "",
									stepDescription: "",
								},
							],
							TToolTipGuideMetaData: {
								id: "",
								containers: [],
								currentStep: 0,
								stepName: "",
								stepDescription: "",
								stepType: "",
								xpath: {
									value: "",
									PossibleElementPath: "",
									position: { x: 0, y: 0 },
								},
								canvas: {
									position: "",
									backgroundColor: "",
									width: "",
									borderRadius: "",
									padding: "",
									borderColor: "",
									borderSize: "",
								},
								hotspots: {
									XPosition: "",
									YPosition: "",
									Type: "",
									Color: "string",
									Size: "",
									PulseAnimation: "",
									stopAnimationUponInteraction: "",
									ShowUpon: "",
									ShowByDefault: "",
								},
								design: {
									gotoNext: {
										NextStep: "",
										ButtonId: "",
										elementPath: "",
										ButtonName: "",
									},
									element: {
										progress: "",
										isDismiss: false,
									},
								},
							},
							toolTipGuideMetaData: [
								{
									containers: [],
									stepName: "Step 1",
									stepDescription: "",
									stepType: "",
									stepId: "",
									currentStep: 1,
									xpath: DEF_XPATH,
									id: crypto.randomUUID(),
									hotspots: HOTSPOT_DEFAULT_VALUE,
									canvas: CANVAS_DEFAULT_VALUE,
									design: {},
								},
							],
							checklistGuideMetaData: [
								{
									id: crypto.randomUUID(),
									checkpoints: CHECKPOINT_DEFAULT_VALUE_CHECKLIST,
									TitleSubTitle: TITLESUBTITLE_DEFAULT_VALUE_CHECKLIST,
									canvas: CANVAS_DEFAULT_VALUE_CHECKLIST,
									launcher: LAUNCHER_DEFAULT_VALUE_CHECKLIST,
								},
							],
							announcementGuideMetaData: [
								{
									containers: [
										{
											id: getRandomID(),
											type: "rte",
											placeholder: DEF_PLACEHOLDER_MSG,
											rteBoxValue: "",
										  },
										  {
											type: "button",
											...BUTTON_CONT_DEF_VALUE_1,
											id: getRandomID(),
										  },
										  {
											...IMG_CONT_DEF_VALUE,
											type: "image",
											id: getRandomID(),
											images: [
											  {
												id: getRandomID(),
												url: defaultAnnouncementImages[Math.floor(Math.random() * defaultAnnouncementImages.length)],
												altText: "Default Announcement Image",
												backgroundColor: "#ffffff",
												objectFit: "contain",
											  },
											],
										  },
									],
									stepName: "Step 1",
									currentStep: 1,
									stepDescription: "",
									stepType: "Announcement",
									xpath: DEF_XPATH,
									id: crypto.randomUUID(),
									hotspots: HOTSPOT_DEFAULT_VALUE,
									canvas: {},
									design: {
										gotoNext: "",
										element: {
											progress: "Template1", // Default enabled for new announcements
											isDismiss: false,
											progressSelectedOption: 1,
											progressColor: "var(--primarycolor)"
										},
									},
								},
							],
							selectedTemplate: state.selectedTemplate || "",
							selectedTemplateTour: state.selectedTemplateTour || "",
							fit: "",
							guidedatas: "",
							ziindex: false,
							htmlCode: "",
							fill: "",
							imageSrc: "",
							textBoxRef: "",
							htmlContent: "",
							buttonColor: "",
							imageName: "",
							alignment: "",
							textvalue: "",
							sectionHeight: "",
							guideName: "",
							tempGuideName: "",
							position: "",
							padding: "",
							radius: "",
							borderSize: "",
							borderColor: "",
							backgroundColor: "",
							dismissData: {
						Actions: "",
						DisplayType: "Cross Icon",
						Color: "#000000",
						DontShowAgain: true,
						dismisssel: true // Set to false by default - will only show after user enables and applies
					},
							Annpadding: 12,
							AnnborderSize: 0,
							bpadding: "12",
							Bbordercolor: "#00000000",
							BborderSize: "2",
							width: 500,
							borderRadius: 4,
							backgroundC: "",
							// Reset overlay and page interaction to default values for new guides
							overlayEnabled: true, // Default enabled
							pageinteraction: false, // Default disabled
							// Reset launcher settings panel state
							showLauncherSettings: false,
						};
					});
				},
				HotspotGuideDetails: () => {
					set((state) => {
						// Find the step ID for "Step 1"
						const stepId = state.steps.find((s) => s.name === "Step 1")?.id;

						return {
							elementSelected: false,
							// Reset overlay and page interaction to default values for new guides
							overlayEnabled: true, // Default enabled
							pageinteraction: false, // Default disabled
							toolTipGuideMetaData: [
								{
									containers: [], // Hotspot does not require containers

									stepName: "Step 1",
									stepDescription: "",
									stepType: "Hotspot",
									currentStep: 1,
									stepId: stepId, // Assign the retrieved step ID
									xpath: DEF_XPATH,
									id: crypto.randomUUID(),
									hotspots: HOTSPOT_DEFAULT_VALUE,
									canvas: CANVAS_DEFAULT_VALUE,
									design: {
										gotoNext: "",
										element: {
											progress: "",
											isDismiss: false, // Fixed: Changed from "" to false
										},
									},
								},
							],
						};
					});
				},

				TooltipGuideDetails: () => {
					set((state) => {
						// Find step ID for "Step 1"
						const stepId = state.steps.find((s) => s.name === "Step 1")?.id;

						return {
							elementSelected: false,
							// Reset overlay and page interaction to default values for new guides
							overlayEnabled: true, // Default enabled
							pageinteraction: false, // Default disabled
							toolTipGuideMetaData: [
								{
									containers: [
										{
											id: getRandomID(),
											type: "rte",
											placeholder: DEF_PLACEHOLDER_MSG,
											rteBoxValue: "",
										},
									], // Only RTE since stepType is Tooltip

									stepName: "Step 1",
									stepDescription: "",
									stepId: stepId,
									stepType: "Tooltip",
									currentStep: 1,
									xpath: DEF_XPATH,
									id: crypto.randomUUID(),
									hotspots: HOTSPOT_DEFAULT_VALUE,
									canvas: CANVAS_DEFAULT_VALUE,
									design: {
										gotoNext: "",
										element: {
											progress: "",
											isDismiss: false, // Changed from "" to false for proper boolean handling
										},
									},
								},
							],
						};
					});
				},

				HotspotGuideDetailsNew: () => {
					set((state) => ({
						elementSelected: false,
						// Reset overlay and page interaction to default values for new guides
						overlayEnabled: true, // Default enabled
						pageinteraction: false, // Default disabled
						toolTipGuideMetaData: [
							{
								containers: [
									// {
									// 	id: getRandomID(),
									// 	type: "rte",
									// 	placeholder: DEF_PLACEHOLDER_MSG,
									// 	rteBoxValue: "",
									// },
								],
								stepName: "Step 1",
								stepDescription: "",
								stepType: "Hotspot",
								currentStep: 1,
								xpath: DEF_XPATH,
								id: crypto.randomUUID(),
								hotspots: HOTSPOT_DEFAULT_VALUE,
								canvas: CANVAS_DEFAULT_VALUE,
								design: {
									gotoNext: "",
									element: {
										progress: "",
										isDismiss: "",
									},
								},
							},
							{
								containers: [
									// {
									// 	id: getRandomID(),
									// 	type: "rte",
									// 	placeholder: DEF_PLACEHOLDER_MSG,
									// 	rteBoxValue: "",
									// },
								],
								stepName: "Step 2",
								stepDescription: "",
								stepType: "Hotspot",
								currentStep: 1,
								xpath: DEF_XPATH,
								id: crypto.randomUUID(),
								hotspots: HOTSPOT_DEFAULT_VALUE,
								canvas: CANVAS_DEFAULT_VALUE,
								design: {
									gotoNext: "",
									element: {
										progress: "",
										isDismiss: "",
									},
								},
							},
						],
					}));
				},
				TooltipGuideDetailsNew: () => {
					set((state) => ({
						elementSelected: false,
						// Reset overlay and page interaction to default values for new guides
						overlayEnabled: true, // Default enabled
						pageinteraction: false, // Default disabled
						toolTipGuideMetaData: [
							{
								containers: [
									// {
									// 	id: getRandomID(),
									// 	type: "rte",
									// 	placeholder: DEF_PLACEHOLDER_MSG,
									// 	rteBoxValue: "",
									// },
								],
								stepName: "Step 1",
								stepDescription: "",
								stepType: "Tooltip",
								currentStep: 1,
								xpath: DEF_XPATH,
								id: crypto.randomUUID(),
								hotspots: HOTSPOT_DEFAULT_VALUE,
								canvas: CANVAS_DEFAULT_VALUE,
								design: {
									gotoNext: "",
									element: {
										progress: "",
										isDismiss: "",
									},
								},
							},
							{
								containers: [
									// {
									// 	id: getRandomID(),
									// 	type: "rte",
									// 	placeholder: DEF_PLACEHOLDER_MSG,
									// 	rteBoxValue: "",
									// },
								],
								stepName: "Step 2",
								stepDescription: "Tooltip Descp2",
								stepType: "Tooltip",
								currentStep: 1,
								xpath: DEF_XPATH,
								id: crypto.randomUUID(),
								hotspots: HOTSPOT_DEFAULT_VALUE,
								canvas: CANVAS_DEFAULT_VALUE,
								design: {
									gotoNext: "",
									element: {
										progress: "",
										isDismiss: "",
									},
								},
							},
						],
					}));
				},
				clearBannerButtonDetials: () => {
					set((state) => ({
						...state, // Preserve any other state values if needed

						buttonsContainer: BUTTON_CONT_DEF_VALUE,
					}));
				},

				// Reset banner canvas settings to default values for new banner steps
				resetBannerCanvasToDefaults: () => {
					set((state) => {
						// Reset all banner canvas-related state variables to default values
						state.Bposition = CANVAS_DEFAULT_VALUE_Banner.position;
						state.bpadding = CANVAS_DEFAULT_VALUE_Banner.padding;
						state.BborderSize = CANVAS_DEFAULT_VALUE_Banner.borderSize;
						state.Bbordercolor = CANVAS_DEFAULT_VALUE_Banner.borderColor;
						state.backgroundC = CANVAS_DEFAULT_VALUE_Banner.backgroundColor;

						// Also reset the global canvas state variables that might be used
						state.backgroundColor = CANVAS_DEFAULT_VALUE_Banner.backgroundColor;
						state.borderColor = CANVAS_DEFAULT_VALUE_Banner.borderColor;
						state.Annpadding = CANVAS_DEFAULT_VALUE_Banner.padding;
						state.AnnborderSize = CANVAS_DEFAULT_VALUE_Banner.borderSize;

						// Clear any additional cached canvas data
						state.borderRadius = 4;
						state.borderSize = 0;
						state.width = 500;
						state.padding = "10";
					});
				},

				createNewStep: (title: string, type: string, description: string) => {
					set((state) => {
						// Generate a unique ID for the new step
						const newStepId = crypto.randomUUID();
						const stepCount = state.steps.length + 1;

						// Create the new step object
						const newStep = {

							id: newStepId,
							name: title,

							stepCount: stepCount,
							stepType: type,
							stepDescription: description,
						};

						// Add the new step to `steps` array
						state.steps = [...state.steps, newStep];

						// Initialize default containers for the new step
						const newStepData: StepData = {

							id: newStepId,
							imagesContainer: [
								{
									id: crypto.randomUUID(),
									images: [
										    {
												id: crypto.randomUUID(),
												url: defaultAnnouncementImages[Math.floor(Math.random() * defaultAnnouncementImages.length)],
												altText: "Default Announcement Image",
												backgroundColor: "#ffffff",
												objectFit: "contain",
											},
									],
									style: {
										backgroundColor: "transparent",
										height: IMG_CONTAINER_DEFAULT_HEIGHT,
										paddingLeft: 0,
										paddingTop: 0,
										paddingRight: 0,
										paddingBottom: 0,
										maxHeight: 0,
									},
									hyperlink: undefined,
								},
							],
							rtesContainer: [
								{
									id: crypto.randomUUID(),
									rtes: [
										{
											id: crypto.randomUUID(),
											text: "",
										},
									],
									style: {
										backgroundColor: "#f0f0f0",
									},
								},
							],
							buttonsContainer: BUTTON_CONT_DEF_VALUE, // Set this as the default value
						};

						// Add this to the `state.stepData` array
						state.stepData = [...state.stepData, newStepData];

						// Add to toolTipGuideMetaData if applicable
						if (type === "Tooltip" || type === "Hotspot" || state.selectedTemplate === "Tour") {
							// Create a new tooltip metadata entry
							const newTooltipStep = {
								containers: [
									{
										id: crypto.randomUUID(),
										type: "rte",
										placeholder: DEF_PLACEHOLDER_MSG,
										rteBoxValue: "",
									}
								],
								stepName: title,
								currentStep: stepCount,
								stepId: newStepId,
								stepDescription: description,
								stepType: type,
								xpath: DEF_XPATH,
								id: crypto.randomUUID(),
								hotspots: HOTSPOT_DEFAULT_VALUE,
								canvas: CANVAS_DEFAULT_VALUE,
								design: {
									gotoNext: "",
									element: {
										progress: "",
										isDismiss: false,
									},
								},
							};

							// Add to toolTipGuideMetaData array
							state.toolTipGuideMetaData = [...state.toolTipGuideMetaData, newTooltipStep];
						}

						// Add to appropriate guide data structure based on type
						const newGuideStep = {
							StepId: newStepId,
							StepTitle: title,
							Description: description,
							StepType: type,
							StepCount: stepCount,
							stepName: title,
							Overlay: state.overlayEnabled,
							Tooltip: {
								EnableProgress: state.progress,
								ProgressTemplate: state.selectedOption?.toString() || "",
							}
						};

						// Add to the appropriate guide data structure
						if (type === "Announcement" || state.selectedTemplate === "Announcement") {
							// For announcements, inherit global overlay settings
							const announcementGuideStep = {
								...newGuideStep,
								Overlay: state.overlayEnabled,
								Tooltip: {
									...newGuideStep.Tooltip,
									InteractWithPage: state.pageinteraction,
								}
							};

							if (state.announcementJson?.GuideStep) {
								state.announcementJson.GuideStep = [...state.announcementJson.GuideStep, announcementGuideStep];
							}

							// Apply global overlay synchronization after creating the new announcement step
							console.log("🔄 createNewStep: Applying global overlay sync for new announcement step", {
								stepTitle: title,
								globalOverlay: state.overlayEnabled,
								globalPageInteraction: state.pageinteraction
							});
							state.syncGlobalOverlayStateForAnnouncements();
						} else if (type === "Banner" || state.selectedTemplate === "Banner") {
							// Create banner step with default canvas values
							const bannerGuideStep = {
								...newGuideStep,
								Canvas: {
									Position: CANVAS_DEFAULT_VALUE_Banner.position,
									Padding: CANVAS_DEFAULT_VALUE_Banner.padding,
									BorderSize: CANVAS_DEFAULT_VALUE_Banner.borderSize,
									BorderColor: CANVAS_DEFAULT_VALUE_Banner.borderColor,
									BackgroundColor: CANVAS_DEFAULT_VALUE_Banner.backgroundColor,
									Radius: "4",
									Width: "100%",
									Zindex: "999999"
								}
							};

							if (state.bannerJson?.GuideStep) {
								state.bannerJson.GuideStep = [...state.bannerJson.GuideStep, bannerGuideStep];
							}

							// Reset banner canvas settings to defaults for new banner steps
							state.Bposition = CANVAS_DEFAULT_VALUE_Banner.position;
							state.bpadding = CANVAS_DEFAULT_VALUE_Banner.padding;
							state.BborderSize = CANVAS_DEFAULT_VALUE_Banner.borderSize;
							state.Bbordercolor = CANVAS_DEFAULT_VALUE_Banner.borderColor;
							state.backgroundC = CANVAS_DEFAULT_VALUE_Banner.backgroundColor;

							// Also reset the global canvas state variables that might be used
							state.backgroundColor = CANVAS_DEFAULT_VALUE_Banner.backgroundColor;
							state.borderColor = CANVAS_DEFAULT_VALUE_Banner.borderColor;
							state.Annpadding = CANVAS_DEFAULT_VALUE_Banner.padding;
							state.AnnborderSize = CANVAS_DEFAULT_VALUE_Banner.borderSize;
						}
					});
				},

				createNewAnnouncementStep: (title, type, description) => {
					set((state) => {
						const randomImage = defaultAnnouncementImages[Math.floor(Math.random() * defaultAnnouncementImages.length)];
						const containers = [
							{
								id: getRandomID(),
								type: "rte",
								placeholder: DEF_PLACEHOLDER_MSG,
								rteBoxValue: "",
							},
							{
								type: "button",
								...BUTTON_CONT_DEF_VALUE_1,
								id: getRandomID(),
							},
							{
								...IMG_CONT_DEF_VALUE,
								type: "image",
								id: getRandomID(),
								images: [
									{
									  id: getRandomID(),
									  url: randomImage,
									  altText: "Default Announcement Image",
									  backgroundColor: "#ffffff",
									  objectFit: "contain",
									},
								  ],
							},
						];

						const newStep = {
							id: crypto.randomUUID(),
							name: title,
							stepCount: state.steps.length + 1,
							stepType: type,
							stepDescription: description,
						};

						const newStepData = {
							StepId: newStep.id,
							StepTitle: title,
							Description: description,
							StepType: type,
							// Inherit current global overlay state for announcements
							Overlay: state.selectedTemplate === "Announcement" ? state.overlayEnabled : false,
							Arrow: false,
							IsClickable: false,
							HtmlSnippet: "",
							Modal: {
								InteractionWithPopup: true,
								IncludeRequisiteButtons: true,
								DismissOption: false,
								ModalPlacedOn: "",
							},
							Position: {
								XAxisOffset: "",
								YAxisOffset: "",
							},
							Canvas: {},
							Design: {
								ViewPortWidth: "",
								BackdropShadow: false,
								QuietIcon: true,
								IconColor: "",
								GotoNext: {
									NextStep: "element",
									ButtonId: "Button",
									ElementPath: "",
									ButtonName: "",
								},
							},
							Tooltip: {
								EnableProgress: false,
								Color: "",
								ProgressTemplate: "",
								GotoNextStep: "",
								ButtonName: "",
								// Inherit current global page interaction state for announcements
								InteractWithPage: state.selectedTemplate === "Announcement" ? state.pageinteraction : false,
							},
							Advanced: {
								ShowAfter: "",
								OnScrollDelay: "",
							},
							tooltipplacement: "",
							LayoutPositions: [],
							containers: containers,
						};

						const updatedState = {
							steps: [...state.steps, newStep],

							...(type === "Announcement" || type === ""
								? {
										announcementGuideMetaData: Array.isArray(state.announcementGuideMetaData)
											? [...state.announcementGuideMetaData, newStepData]
											: [newStepData],
								  }
								: {}),
						};

						// Apply the updated state first
						Object.assign(state, updatedState);

						// For announcements, immediately apply global overlay synchronization after creating the new step
						if (state.selectedTemplate === "Announcement") {
							console.log("🔄 createNewAnnouncementStep: Applying global overlay sync after creating new step", {
								newStepTitle: title,
								globalOverlay: state.overlayEnabled,
								globalPageInteraction: state.pageinteraction
							});

							// Apply global overlay state to all steps including the new one
							state.syncGlobalOverlayStateForAnnouncements();
						}

						return state;
					});
				},
				announcementGuideMetaData: [
					{
						containers: [
							{
								id: getRandomID(),
								type: "rte",
								placeholder: DEF_PLACEHOLDER_MSG,
								rteBoxValue: "",
							},
							{
								type: "button",
								...BUTTON_CONT_DEF_VALUE_1,
								id: getRandomID(),
							},
							{
								...IMG_CONT_DEF_VALUE,
								type: "image",
								id: getRandomID(),
								images: [
									{
									  id: getRandomID(),
									  url: defaultAnnouncementImages[Math.floor(Math.random() * defaultAnnouncementImages.length)],
									  altText: "Default Announcement Image",
									  backgroundColor: "#ffffff",
									  objectFit: "contain",
									},
								  ],
							},
						],
						stepName: "Step 1",
						currentStep: 1,
						stepDescription: "",
						stepType: "Announcement",
						xpath: DEF_XPATH,
						id: crypto.randomUUID(),
						hotspots: HOTSPOT_DEFAULT_VALUE,
						canvas: {},
						design: {
							gotoNext: "",
							element: {
								progress: "Template1", // Default enabled for new announcements
								isDismiss: false,
								progressSelectedOption: 1,
								progressColor: "var(--primarycolor)"
							},
						},
					},
				],
				changeCurrentStep: (id: string, type: string) => {
					set((state) => {
						if (id) {
							// Find the current step's data using the ID
							const currentStepData = state.stepData.find((step) => step.id === id);

							// Set the currentStep to the found step's stepCount
							const currStep = state.steps.find((item) => item.id === id)?.stepCount || state.currentStep;
							state.currentStep = currStep;

							// Load the step-specific data (images, RTEs, buttons, etc.)
							if (currentStepData) {
								state.imagesContainer = currentStepData.imagesContainer;
								state.rtesContainer = currentStepData.rtesContainer;

								// Load the existing buttonsContainer for the step, or use default
								state.buttonsContainer = currentStepData.buttonsContainer || BUTTON_CONT_DEF_VALUE;
							} else {
								// If no data is found for the step, set default containers
								state.imagesContainer = [];
								state.rtesContainer = [];
								state.buttonsContainer = BUTTON_CONT_DEF_VALUE;
							}
							// if (state.selectedTemplate === "Tooltip"|| state.selectedTemplateTour=== "Tooltip" || type==="Tooltip") {
							const opt = { Template1: "1", Template2: "2", Template3: "3" };
							// state.selectedOption =
							// 	+opt[state.toolTipGuideMetaData[state.currentStep - 1]?.design?.element?.progress] || "dismiss";

							state.tooltipPosition =
								state.toolTipGuideMetaData[state.currentStep - 1]?.canvas?.position || "middle-center";
							state.tooltippadding = state.toolTipGuideMetaData[state.currentStep - 1]?.canvas?.padding || "12px";
							state.tooltipborderradius = state.toolTipGuideMetaData[state.currentStep - 1]?.canvas?.borderRadius || "8px";
							state.tooltipbordersize = state.toolTipGuideMetaData[state.currentStep - 1]?.canvas?.borderSize || "0px";
							state.tooltipBordercolor = state.toolTipGuideMetaData[state.currentStep - 1]?.canvas?.borderColor || "transparent";
							state.tooltipBackgroundcolor = state.toolTipGuideMetaData[state.currentStep - 1]?.canvas?.backgroundColor || "#FFFFFF";
							state.tooltipWidth = `${state.toolTipGuideMetaData[state.currentStep - 1]?.canvas?.width}` || "300px";
							state.tooltipXaxis = state.toolTipGuideMetaData[state.currentStep - 1]?.canvas?.xaxis || "4px";
							state.tooltipYaxis = state.toolTipGuideMetaData[state.currentStep - 1]?.canvas?.yaxis || "4px";
							// state.toolTipGuideMetaData[state.currentStep - 1].canvas = null;

							//}
						}
						state.elementSelected = false;
					});
				},

				generateSteps: (steps: any[]) => {
					set((state) => ({
						steps: steps.map((item, index) => ({
							id: item.StepId || crypto.randomUUID(),
							name: item?.StepTitle || `Step ${index + 1}`,
							stepCount: index + 1,
							stepType: item?.StepType,
							stepDescription: item?.Description,
						})),
					}));
				},

				renameStep: (id: string, newTitle: string, desc: string) => {
					set((state) => {
						// Find the current step name before updating
						const currentStep = state.steps.find((s) => s.id === id);
						if (!currentStep) return state; // Exit if step not found

						const oldStepName = currentStep.name;
						const stepCount = currentStep.stepCount;

						// Update all references to this step across different data structures

						// Update in toolTipGuideMetaData
						state.toolTipGuideMetaData = state.toolTipGuideMetaData.map(metadata => {
							if (metadata.stepId === id ||
								metadata.stepName === oldStepName ||
								metadata.stepName === stepCount.toString() ||
								metadata.currentStep === stepCount) {
								return {
										...metadata,

									stepName: newTitle,
									stepDescription: desc
								};
							  }
							return metadata;
						});

						// Update in announcementGuideMetaData
						if (state.announcementGuideMetaData) {
							state.announcementGuideMetaData = state.announcementGuideMetaData.map(metadata => {
								if (metadata.stepName === oldStepName ||
									metadata.stepName === stepCount.toString() ||
									metadata.StepCount === stepCount ||
									metadata.StepTitle === oldStepName) {
									return {
										...metadata,
										stepName: newTitle,
										StepTitle: newTitle,
										stepDescription: desc,
										Description: desc
									};
								}
								return metadata;
							});
						}

						// Update in bannerJson
						if (state.bannerJson?.GuideStep) {
							state.bannerJson.GuideStep = state.bannerJson.GuideStep.map(step => {
								if (step.stepName === oldStepName ||
									step.stepName === stepCount.toString() ||
									step.StepCount === stepCount ||
									step.StepTitle === oldStepName) {
									return {
										...step,
										stepName: newTitle,
										StepTitle: newTitle,
										Description: desc,
										StepId: id
									};
								}
								return step;
							});
						}

						// Update in announcementJson
						if (state.announcementJson?.GuideStep) {
							state.announcementJson.GuideStep = state.announcementJson.GuideStep.map(step => {
								if (step.stepName === oldStepName ||
									step.stepName === stepCount.toString() ||
									step.StepCount === stepCount ||
									step.StepTitle === oldStepName) {
									return {
										...step,
										stepName: newTitle,
										StepTitle: newTitle,
										Description: desc,
										StepId: id
									};
								}
								return step;
							});
						}

						// Update in stepData array
						if (state.stepData) {
							state.stepData = state.stepData.map(data => {
								if (data.id === id) {
									return {
										...data,
										name: newTitle,
										stepDescription: desc
									};
								}
								return data;
							});
						}

						// Update in main steps array
						state.steps = state.steps.map(item => ({
							...item,
							name: item.id === id ? newTitle : item.name,
							stepDescription: item.id === id ? desc : item.stepDescription,

					}));

						// Set flag to indicate changes
						state.isUnSavedChanges = true;

						return state;
					});
				},

				// deleteStep: (id: string) => {
				// 	set((state) => {
				// 		// Filter out the step to delete
				// 		state.steps = state.steps.filter((step) => step.id !== id);

				// 		// Remove associated step data
				// 		state.stepData = state.stepData.filter((data) => data.id !== id);

				// 		// Adjust currentStep if the deleted step was active
				// 		if (state.currentStep === state.steps.find((step) => step.id === id)?.stepCount) {
				// 			state.currentStep = state.steps[0]?.stepCount || 0; // Default to the first step
				// 		}

				// 		// Renumber the stepCounts after deletion
				// 		state.steps = state.steps.map((step, index) => ({
				// 			...step,
				// 			stepCount: index + 1,
				// 		}));
				// 	});
				// },
				deleteStep: (id: string) => {
					set((state) => {

						// Find the step to be deleted to get both its name and count
						const stepToDelete = state.steps.find((step: any) => step.id === id);
						if (!stepToDelete) return; // Exit if step not found

						const stepName = stepToDelete.name;
						const stepCount = stepToDelete.stepCount;
						const stepType = stepToDelete.stepType;

						// Remove step from main steps array
						state.steps = state.steps.filter((step) => step.id !== id);

						// Renumber remaining steps
						state.steps = state.steps.map((step, index) => ({
							...step,
							stepCount: index + 1,
						}));

						// Mark as unsaved changes to enable save button
						state.isUnSavedChanges = true;

						// Clear current containers to prevent stale data
						state.rtesContainer = [];
						state.buttonsContainer = [];
						state.imagesContainer = [];

						// Reset banner canvas settings to defaults if a banner step was deleted
						if (stepType === "Banner" || state.selectedTemplate === "Banner" || state.selectedTemplateTour === "Banner") {
							// Reset all banner canvas-related state variables to default values
							state.Bposition = CANVAS_DEFAULT_VALUE_Banner.position;
							state.bpadding = CANVAS_DEFAULT_VALUE_Banner.padding;
							state.BborderSize = CANVAS_DEFAULT_VALUE_Banner.borderSize;
							state.Bbordercolor = CANVAS_DEFAULT_VALUE_Banner.borderColor;
							state.backgroundC = CANVAS_DEFAULT_VALUE_Banner.backgroundColor;

							// Also reset the global canvas state variables that might be used
							state.backgroundColor = CANVAS_DEFAULT_VALUE_Banner.backgroundColor;
							state.borderColor = CANVAS_DEFAULT_VALUE_Banner.borderColor;
							state.Annpadding = CANVAS_DEFAULT_VALUE_Banner.padding;
							state.AnnborderSize = CANVAS_DEFAULT_VALUE_Banner.borderSize;

							// Clear any cached canvas data that might be loaded from other sources
							state.borderRadius = 4;
							state.borderSize = 0;
							state.width = 500;
							state.padding = "10";
						}

						// Remove from toolTipGuideMetaData by both ID and name to ensure complete removal
						if (state.toolTipGuideMetaData) {
							state.toolTipGuideMetaData = state.toolTipGuideMetaData.filter(
							  (tooltipStep) => tooltipStep.stepId !== id &&
							  tooltipStep.stepName !== stepName &&
							  tooltipStep.stepName !== stepCount.toString() &&
							  tooltipStep.currentStep !== stepCount
							);

							// Reindex remaining tooltip metadata
							state.toolTipGuideMetaData = state.toolTipGuideMetaData.map((metadata, index) => ({
								...metadata,
								currentStep: index + 1,
							}));
						}

						// Remove from announcementGuideMetaData by both ID and name to ensure complete removal
						if (state.announcementGuideMetaData) {
							state.announcementGuideMetaData = state.announcementGuideMetaData.filter(
							  (announcementStep) => announcementStep.stepId !== id &&
							  announcementStep.stepName !== stepName &&
							  announcementStep.stepName !== stepCount.toString() &&
							  announcementStep.currentStep !== stepCount
							);
						}

						// Remove from stepData array
						if (state.stepData) {
							state.stepData = state.stepData.filter((data) => data.id !== id);
						}

						// Remove from bannerJson by both name and count
						if (state.bannerJson?.GuideStep) {

							state.bannerJson.GuideStep = state.bannerJson.GuideStep.filter(
								(step) => step.stepName !== stepName &&
								step.stepName !== stepCount.toString() &&
								step.StepTitle !== stepName &&
								step.StepCount !== stepCount &&
								step.StepId !== id
							);

							// If this was a banner step deletion in a tour, clear all banner canvas data to prevent inheritance
							if (stepType === "Banner" && state.selectedTemplate === "Tour") {
								// For tours, completely clear bannerJson.GuideStep to prevent any canvas inheritance
								state.bannerJson.GuideStep = [];
							}
						}

						// Remove from announcementJson by both name and count
						if (state.announcementJson?.GuideStep) {
							state.announcementJson.GuideStep = state.announcementJson.GuideStep.filter(

								(step) => step.stepName !== stepName &&
								step.stepName !== stepCount.toString() &&
								step.StepTitle !== stepName &&
								step.StepCount !== stepCount
							);
						}

						// Update currentStep if needed
						if (state.currentStep > state.steps.length) {
							state.currentStep = Math.max(1, state.steps.length);
						}
					});
				},

				deleteCheckpoint: (interaction: string) => {
					set((state) => ({
						checklistGuideMetaData: state.checklistGuideMetaData.map((meta: any, index: number) => {
							if (index === 0) {
								return {
									...meta,
									checkpoints: {
										...meta.checkpoints,
										checkpointsList: meta.checkpoints.checkpointsList.filter(
											(checkpoint: any) => checkpoint.interaction !== interaction
										),
									},
								};
							}
							return meta;
						}),
					}));
				},

				setTooltip: (params) => {
					set((state) => {
						state.tooltip = params;
					});
				},
				createTooltipSections: (sectionType, index) => {
					set((state) => {
						const targetIndex = state.currentStep - 1;

						// Check if we've reached the section limit for this type
						const containers = state.toolTipGuideMetaData[targetIndex]?.containers || [];
						const count = containers.filter(container => container.type === sectionType).length;

						// If we've reached the limit, don't add a new section
						if (count >= MAX_SECTIONS[sectionType]) {
							return;
						}

						if (!state.toolTipGuideMetaData[targetIndex]?.containers?.length) {
							state.toolTipGuideMetaData[targetIndex] = {
								id: getRandomID(),
								currentStep: state.currentStep,
								xpath: state.toolTipGuideMetaData[targetIndex]?.xpath || DEF_XPATH,
								PossibleElementPath: state.toolTipGuideMetaData[targetIndex]?.PossibleElementPath || "",
								containers: [],
								stepName: state.steps[targetIndex].name,
								stepDescription: state.steps[targetIndex].stepDescription,
								stepType: state.steps[targetIndex].stepType,
								design: {
									gotoNext: {
										NextStep: "",
										ButtonId: "",
										elementPath: "",
										ButtonName: "",
									},
									element: {
										isDismiss: false,
										progress: "",
									},
								},
							};
						}
						switch (sectionType) {
							case "image":
								state.toolTipGuideMetaData[targetIndex].containers.splice(index + 1, 0, {
									...IMG_CONT_DEF_VALUE,
									type: "image",
									id: getRandomID(),
								});
								break;

							case "button":
								state.toolTipGuideMetaData[targetIndex].containers.splice(index + 1, 0, {
									...BUTTON_CONT_DEF_VALUE_1,
									type: "button",
									id: getRandomID(),
									buttons: BUTTON_CONT_DEF_VALUE_1.buttons.map((button) => ({
										...button,
										id: getRandomID(),
										name: selectedtemp === "Tooltip" || selectedtemp === "Tour" ? "Click Here" : "Got It",
									})),
								});

								break;
							case "rte":
								state.toolTipGuideMetaData[targetIndex].containers.splice(index + 1, 0, {
									...RTE_DEF_CONT_VALUE,
									type: "rte",
									id: getRandomID(),
									placeholder: DEF_PLACEHOLDER_MSG,
									// rteBoxValue: "Hello World",
								});
								break;

							default:
								break;
						}
					});
				},
				addNewButtonInTooltip: async (containerId = "") => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						if (containerId) {
							const targetContainerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
								(item) => item.id === containerId
							);
							const type = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex]?.type;

							if (targetContainerIndex !== -1 && type === "button") {
								const buttons = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].buttons;

								// Get the max index among existing buttons
								const maxIndex = Math.max(0, ...buttons.map((b) => b.index ?? 0));

								// Push a new button with a new unique index and name
								state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].buttons.push({
									...BUTTON_DEFAULT_VALUE,
									id: crypto.randomUUID(),
									name: `Button ${maxIndex + 2}`, // Ensure name is unique and increasing
									index: maxIndex + 1, // Ensure index is unique and increasing
									actions: {
										...BUTTON_DEFAULT_VALUE.actions,
										interaction: null, // Avoid unwanted default interaction
									},
								});

								// Synchronize AI tooltip data when button is added
								if (state.createWithAI && state.selectedTemplate === "Tooltip") {
									if (state.interactionData?.GuideStep?.[targetStep]) {
										const currentGuideStep = state.interactionData.GuideStep[targetStep];

										// Update ButtonSection from button containers
										const buttonContainers = state.toolTipGuideMetaData[targetStep].containers.filter(c => c.type === "button");
										if (buttonContainers.length > 0) {
											currentGuideStep.ButtonSection = buttonContainers.map((container: any) => ({
												Id: container.id,
												CustomButtons: container.buttons?.map((button: any) => ({
													ButtonId: button.id,
													ButtonName: button.name,
													BackgroundColor: button.style?.backgroundColor || "#5F9EA0",
													BorderColor: button.style?.borderColor || "#5F9EA0",
													TextColor: button.style?.color || "#ffffff",
													Actions: button.actions,
												})) || [],
											}));
										}
									}
								}
							}
						}
					});
				},

				deleteTooltipButtonContainer: async (containerId = "") => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						if (containerId) {
							const targetContainerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
								(item) => item.id === containerId
							);
							const type = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].type;
							if (targetContainerIndex !== -1 && type === "button") {
								state.toolTipGuideMetaData[targetStep].containers.splice(targetContainerIndex, 1);
								if (
									!state.toolTipGuideMetaData[targetStep].design.gotoNext ||
									typeof state.toolTipGuideMetaData[targetStep].design.gotoNext !== "object"
								) {
									state.toolTipGuideMetaData[targetStep].design.gotoNext = {};
								}

								// Clear values or remove the object completely
								state.toolTipGuideMetaData[targetStep].design.gotoNext = {};
							}
						}
					});
				},
				cloneTooltipButtonContainer: async (containerId = "") => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						if (containerId) {
							const targetContainerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
								(item) => item.id === containerId
							);

							if (targetContainerIndex === -1) return;

							const container = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex];
							const type = container.type;

							// Check if we've reached the section limit for this type
							const containers = state.toolTipGuideMetaData[targetStep].containers || [];
							const count = containers.filter(container => container.type === type).length;

							// If we've reached the limit, don't clone the section
							if (count >= MAX_SECTIONS[type]) {
								return;
							}

							if (type === "button") {
								const btnContainer = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex];
								// @ts-ignore
								state.toolTipGuideMetaData[targetStep].containers.splice(targetContainerIndex, 0, {
									...btnContainer,
									id: crypto.randomUUID(),
								});
								state.isUnSavedChanges = true;
							}
						}
					});
				},
				deleteButtonInTooltip: async (buttonId = "", containerId = "", ButtonClickID: any) => {
					set((state) => {
						const targetStep = state.currentStep - 1;

						if (buttonId && containerId) {
							const targetContainerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
								(item) => item.id === containerId
							);

							if (targetContainerIndex !== -1) {
								//@ts-ignore
								const buttons = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].buttons;

								if (buttons.length > 1) {
									state.isUnSavedChanges = true;

									//@ts-ignore
									const buttonIndex = buttons.findIndex((item: { id: string }) => item.id === buttonId);

									if (buttonIndex !== -1) {
										//@ts-ignore
										state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].buttons.splice(
											buttonIndex,
											1
										);

										if (ButtonClickID === buttonId) {
											state.toolTipGuideMetaData[targetStep].design.gotoNext = {};
										}
									}
								}
							}
						}
					});
				},

				updateButtonInTooltip: (containerId, buttonId, keyname, value) => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						const containerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
							(c) => c.id === containerId
						);
						if (containerIndex !== -1) {
							// @ts-ignore
							const buttonIndex = state.toolTipGuideMetaData[targetStep].containers[containerIndex].buttons.findIndex(
								(b: { id: string }) => b.id === buttonId
							);
							if (buttonIndex !== -1) {
								// @ts-ignore
								state.toolTipGuideMetaData[targetStep].containers[containerIndex].buttons[buttonIndex][keyname] = value;

								// Synchronize AI tooltip data when button is updated
								if (state.createWithAI && state.selectedTemplate === "Tooltip") {
									if (state.interactionData?.GuideStep?.[targetStep]) {
										const currentGuideStep = state.interactionData.GuideStep[targetStep];

										// Update ButtonSection from button containers
										const buttonContainers = state.toolTipGuideMetaData[targetStep].containers.filter(c => c.type === "button");
										if (buttonContainers.length > 0) {
											currentGuideStep.ButtonSection = buttonContainers.map((container: any) => ({
												Id: container.id,
												CustomButtons: container.buttons?.map((button: any) => ({
													ButtonId: button.id,
													ButtonName: button.name,
													BackgroundColor: button.style?.backgroundColor || "#5F9EA0",
													BorderColor: button.style?.borderColor || "#5F9EA0",
													TextColor: button.style?.color || "#ffffff",
													Actions: button.actions,
												})) || [],
											}));
										}
									}
								}
							}
							state.isUnSavedChanges = containerIndex === -1 ? false : true;
						}
					});
				},
				updateCanvasInTooltip: (value) => {
					set((state) => {
						const targetStep = state.currentStep - 1;

						// Store the previous state for undo
						const stateBefore = state.toolTipGuideMetaData[targetStep]?.canvas
							? deepClone(state.toolTipGuideMetaData[targetStep].canvas)
							: {};

						// Directly modify the draft state instead of creating new objects
						if (!state.toolTipGuideMetaData[targetStep]) {
							state.toolTipGuideMetaData[targetStep] = {};
						}

						// Update canvas settings directly on the draft
						state.toolTipGuideMetaData[targetStep].canvas = { ...value };
						state.tooltipBordercolor = value.borderColor;
						state.isUnSavedChanges = true;

						// Record the change for undo/redo
						recordChange(
							'CANVAS_UPDATE',
							`Updated canvas settings for Tooltip step ${state.currentStep}`,
							stateBefore,
							{ ...value },
							`tooltip_canvas_${state.currentStep}`,
							{
								guideType: 'Tooltip',
								currentStep: state.currentStep
							}
						);

						// Synchronize AI tooltip data when canvas settings are updated
						if (state.createWithAI && state.selectedTemplate === "Tooltip") {
							// Update interactionData with current canvas settings
							if (state.interactionData?.GuideStep?.[targetStep]) {
								const currentGuideStep = state.interactionData.GuideStep[targetStep];

								// Update Canvas settings
								currentGuideStep.Canvas = {
									Position: value.position || currentGuideStep.Canvas?.Position || "absolute",
									Width: value.width || currentGuideStep.Canvas?.Width || "300px",
									Padding: value.padding || currentGuideStep.Canvas?.Padding || "2px",
									BorderRadius: value.borderRadius || currentGuideStep.Canvas?.BorderRadius || "8px",
									BorderSize: value.borderSize || currentGuideStep.Canvas?.BorderSize || "0px",
									BorderColor: value.borderColor || currentGuideStep.Canvas?.BorderColor || "transparent",
									BackgroundColor: value.backgroundColor || currentGuideStep.Canvas?.BackgroundColor || "#ffffff",
								};

								// Update Position settings
								currentGuideStep.Position = {
									XAxisOffset: value.xaxis || currentGuideStep.Position?.XAxisOffset || "1px",
									YAxisOffset: value.yaxis || currentGuideStep.Position?.YAxisOffset || "1px",
								};

								// Update AutoPosition
								if (value.autoposition !== undefined) {
									currentGuideStep.AutoPosition = value.autoposition;
								}
							}
						}
					});
				},
				updateDesignelementInTooltip: (value) => {
					set((state) => {
						const targetStep = state.currentStep - 1;

						try {

							// Make sure toolTipGuideMetaData and design objects exist
							if (!state.toolTipGuideMetaData[targetStep]) {
								console.error('toolTipGuideMetaData not found for step', targetStep);
								return; // Early return without modifying state
							}

							if (!state.toolTipGuideMetaData[targetStep].design) {
								// Initialize with required properties
								state.toolTipGuideMetaData[targetStep].design = {
									gotoNext: {
										ButtonId: '',
										ElementPath: '',
										NextStep: '',
										ButtonName: '',
										Id: ''
									},
									element: {
										progress: '',
										isDismiss: false
									}
								};
							}

							// Store the previous state for undo
							const stateBefore = {
								dismiss: state.dismiss,
								dismissData: deepClone(state.dismissData || {}),
								progress: state.progress,
								selectedOption: state.selectedOption,
								progressColor: state.ProgressColor,
								gotoNext: state.toolTipGuideMetaData[targetStep]?.design?.gotoNext
									? deepClone(state.toolTipGuideMetaData[targetStep].design.gotoNext)
									: {}
							};

							// Check if this is a button selection update (NextStep is provided)
							if (value.NextStep) {
								// Update the gotoNext object with the button data
								state.toolTipGuideMetaData[targetStep].design.gotoNext = {
									NextStep: value.NextStep,
									ButtonId: value.ButtonId || '',
									ElementPath: value.ElementPath || '',
									ButtonName: value.ButtonName || '',
									Id: value.Id || value.ButtonId || '' // Ensure Id is set to ButtonId if not provided
								};
								// CRITICAL FIX: For AI tooltips, immediately sync the gotoNext data to savedGuideData
								if (state.createWithAI && (state.selectedTemplate === "Tooltip" ||
									(state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Tooltip"))) {
									console.log("🔧 FIXING AI tooltip button click data binding - syncing gotoNext to savedGuideData", {
										targetStep,
										gotoNext: state.toolTipGuideMetaData[targetStep].design.gotoNext,
										selectedTemplate: state.selectedTemplate,
										selectedTemplateTour: state.selectedTemplateTour
									});

									// Call the sync function to update both interactionData and savedGuideData
									state.syncAITooltipContainerData();
								}
							}
							// Otherwise, handle element settings update
							else {
								// Update the state
								if (!state.toolTipGuideMetaData[targetStep].design.element) {
									state.toolTipGuideMetaData[targetStep].design.element = {
										progress: '',
										isDismiss: false
									};
								}

								// Update tooltip metadata with all properties
								state.toolTipGuideMetaData[targetStep].design.element = {
									progress: value.progress ,
									isDismiss: value.dismiss,
									progressSelectedOption: value.progressSelectedOption,
									progressColor: value.progressColor
								};

								// Update state properties
								state.progress = value.progress;
								state.selectedOption = value.progressSelectedOption;
								state.dismiss = value.dismiss;

								// Update progress color if provided
								if (value.progressColor !== undefined) {
									state.ProgressColor = value.progressColor;
								}

								// Update dismissData if needed
								if (value.dismiss) {
									if (!state.dismissData) {
										state.dismissData = {
											Actions: "",
											DisplayType: "Cross Icon",
											Color: "#000000",
											DontShowAgain: false,
											dismisssel: true
										};
									} else {
										state.dismissData.dismisssel = true;
									}
								}
							}

							state.isUnSavedChanges = true;

							// Create a safe copy of the state for recording
							const safeStateAfter = {
								dismiss: state.dismiss,
								dismissData: deepClone(state.dismissData || {}),
								progress: state.progress,
								selectedOption: state.selectedOption,
								progressColor: state.ProgressColor !== undefined ? state.ProgressColor : state.ProgressColor,
								gotoNext: state.toolTipGuideMetaData[targetStep]?.design?.gotoNext
									? deepClone(state.toolTipGuideMetaData[targetStep].design.gotoNext)
									: {}
							};

							// Record the change for undo/redo
							recordChange(
								'ELEMENT_BATCH_UPDATE',
								`Updated element settings for Tooltip step ${state.currentStep}`,
								stateBefore,
								safeStateAfter,
								`element_settings_${state.currentStep}`,
								{
									guideType: 'Tooltip',
									currentStep: state.currentStep,
									progress: state.progress,
									selectedOption: state.selectedOption,
									dismiss: state.dismiss,
									progressColor: state.ProgressColor
								}
							);
						} catch (error) {
							console.error('Error updating design element in tooltip:', error);
						}

						// Synchronize AI tooltip data when design element is updated
						if (state.createWithAI && state.selectedTemplate === "Tooltip") {
							if (state.interactionData?.GuideStep?.[targetStep]) {
								const currentGuideStep = state.interactionData.GuideStep[targetStep];
								const currentTooltipMetadata = state.toolTipGuideMetaData[targetStep];

								// Update Design settings - ensure ElementPath is set for button click
								const gotoNextConfig = { ...currentTooltipMetadata.design?.gotoNext || {} };

								// For button click functionality, ElementPath should point to tooltip's target element
								if (gotoNextConfig.NextStep === "button" && gotoNextConfig.ButtonId && currentTooltipMetadata.xpath?.value) {
									gotoNextConfig.ElementPath = currentTooltipMetadata.xpath.value;
								}

								currentGuideStep.Design = {
									GotoNext: gotoNextConfig,
								};

								// Update Tooltip settings
								currentGuideStep.Tooltip = {
									EnableProgress: currentTooltipMetadata.design?.element?.progress || false,
									ProgressTemplate: currentTooltipMetadata.design?.element?.progressSelectedOption || 1,
									InteractWithPage: state.pageinteraction,
								};
								if(state.interactionData?.GuideStep?.[0]){
								state.interactionData.GuideStep[0].Tooltip = {
									EnableProgress: currentTooltipMetadata.design?.element?.progress || false,
									ProgressTemplate: currentTooltipMetadata.design?.element?.progressSelectedOption || 1,
									InteractWithPage: state.pageinteraction,
								};
								}

								// Update Modal settings
								currentGuideStep.Modal = {
									DismissOption: currentTooltipMetadata.design?.element?.isDismiss || false,
									ProgressColor: currentTooltipMetadata.design?.element?.progressColor || "var(--primarycolor)",
								};
							}
						}

						// Synchronize AI tour data when design element is updated for tooltip steps in tours
						if (state.createWithAI && state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Tooltip") {
							if (state.interactionData?.GuideStep?.[targetStep]) {
								const currentGuideStep = state.interactionData.GuideStep[targetStep];
								const currentTooltipMetadata = state.toolTipGuideMetaData[targetStep];

								// Update Design settings for tour tooltip steps
								currentGuideStep.Design = {
									GotoNext: currentTooltipMetadata.design?.gotoNext || {},
								};

								// Update Tooltip settings
								currentGuideStep.Tooltip = {
									EnableProgress: currentTooltipMetadata.design?.element?.progress !== "",
									ProgressTemplate: currentTooltipMetadata.design?.element?.progressSelectedOption || 1,
									InteractWithPage: state.pageinteraction,
								};

								// Update Modal settings
								currentGuideStep.Modal = {
									...currentGuideStep.Modal,
									DismissOption: currentTooltipMetadata.design?.element?.isDismiss || false,
									ProgressColor: currentTooltipMetadata.design?.element?.progressColor || "var(--primarycolor)",
								};

								console.log("Updated Design.GotoNext for AI tour tooltip step", targetStep, {
									GotoNext: currentTooltipMetadata.design?.gotoNext
								});
							}
						}

						// Synchronize AI announcement data when design element is updated
						if (state.createWithAI && state.selectedTemplate === "Announcement") {
							// Progress bar state should be global across all steps in announcements
							// Update ALL steps when progress setting is changed
							state.announcementGuideMetaData.forEach((metadata, stepIndex) => {
								if (metadata && state.interactionData?.GuideStep?.[stepIndex]) {
									const guideStep = state.interactionData.GuideStep[stepIndex];

									// Update the announcement metadata with the new design element values
									if (!metadata.design) {
										metadata.design = {
											gotoNext: {},
											element: {
												progress: "",
												isDismiss: false,
												progressSelectedOption: 1,
												progressColor: "var(--primarycolor)"
											}
										};
									}

									// Update the design element in announcement metadata for ALL steps
									metadata.design.element = {
										...metadata.design.element,
										progress: value.progress ? "Template1" : "",
										isDismiss: value.dismiss || false,
										progressSelectedOption: value.progressSelectedOption || 1,
										progressColor: value.progressColor || "var(--primarycolor)"
									};

									// Update Tooltip settings in interactionData for ALL steps
									guideStep.Tooltip = {
										...guideStep.Tooltip,
										EnableProgress: value.progress || false,
										ProgressTemplate: value.progressSelectedOption?.toString() || "1",
										InteractWithPage: state.pageinteraction,
									};

									// Update Modal settings in interactionData for ALL steps
									guideStep.Modal = {
										...guideStep.Modal,
										DismissOption: value.dismiss || false,
										ProgressColor: value.progressColor || "var(--primarycolor)",
									};

									// Also update savedGuideData for ALL steps to ensure preview shows correct progress bar state immediately
									if (state.savedGuideData?.GuideStep?.[stepIndex]) {
										state.savedGuideData.GuideStep[stepIndex] = {
											...state.savedGuideData.GuideStep[stepIndex],
											Tooltip: {
												...state.savedGuideData.GuideStep[stepIndex].Tooltip,
												EnableProgress: value.progress || false,
												ProgressTemplate: value.progressSelectedOption || 1,
											},
											Modal: {
												...state.savedGuideData.GuideStep[stepIndex].Modal,
												ProgressColor: value.progressColor || "var(--primarycolor)",
												DismissOption: value.dismiss || false,
											},
										};
									}
								}
							});

							console.log("✅ setDesignElement: Updated ALL steps for AI announcement", {
								totalSteps: state.announcementGuideMetaData.length,
								EnableProgress: value.progress || false,
								allStepsProgress: state.savedGuideData?.GuideStep?.map(step => step?.Tooltip?.EnableProgress),
								allInteractionDataProgress: state.interactionData?.GuideStep?.map(step => step?.Tooltip?.EnableProgress),
								globalState: {
									progress: state.progress,
									selectedOption: state.selectedOption,
									dismiss: state.dismiss,
									progressColor: state.ProgressColor
								}
							});

							// Also update global state for AI announcements to ensure element settings UI reflects changes
							state.progress = value.progress || false;
							state.selectedOption = value.progressSelectedOption || 1;
							state.dismiss = value.dismiss || false;
							if (value.progressColor !== undefined) {
								state.ProgressColor = value.progressColor;
							}

							// For announcements, also sync global overlay state to all steps
							state.syncGlobalOverlayStateForAnnouncements();

							console.log("✅ setDesignElement: Updated ALL steps for AI announcement", {
								totalSteps: state.announcementGuideMetaData.length,
								EnableProgress: value.progress || false,
								allStepsProgress: state.savedGuideData?.GuideStep?.map(step => step?.Tooltip?.EnableProgress),
								globalState: {
									progress: state.progress,
									selectedOption: state.selectedOption,
									dismiss: state.dismiss,
									progressColor: state.ProgressColor
								}
							});
						}

						// Synchronize AI tour announcement data when design element is updated
						if (state.createWithAI && state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Announcement") {
							// Progress bar state should be global across all announcement steps in tours
							// Update ALL announcement steps when progress setting is changed
							state.toolTipGuideMetaData.forEach((metadata, stepIndex) => {
								if (metadata && state.interactionData?.GuideStep?.[stepIndex]) {
									const guideStep = state.interactionData.GuideStep[stepIndex];

									// Only update announcement steps in tours
									if (guideStep.StepType === "Announcement") {
										// Update the tooltip metadata with the new design element values (for tour announcements)
										if (!metadata.design) {
											metadata.design = {
												gotoNext: {},
												element: {
													progress: "",
													isDismiss: false,
													progressSelectedOption: 1,
													progressColor: "var(--primarycolor)"
												}
											};
										}

										// Update the design element in tooltip metadata for ALL announcement steps
										metadata.design.element = {
											...metadata.design.element,
											progress: value.progress ? "Template1" : "",
											isDismiss: value.dismiss || false,
											progressSelectedOption: value.progressSelectedOption || 1,
											progressColor: value.progressColor || "var(--primarycolor)"
										};

										// Update Tooltip settings in interactionData for ALL announcement steps
										guideStep.Tooltip = {
											...guideStep.Tooltip,
											EnableProgress: value.progress || false,
											ProgressTemplate: value.progressSelectedOption?.toString() || "1",
											InteractWithPage: state.pageinteraction,
										};

										// Update Modal settings in interactionData for ALL announcement steps
										guideStep.Modal = {
											...guideStep.Modal,
											DismissOption: value.dismiss || false,
											ProgressColor: value.progressColor || "var(--primarycolor)",
										};
									}
								}
							});

							// Also update global state for AI tour announcements to ensure element settings UI reflects changes
							state.progress = value.progress || false;
							state.selectedOption = value.progressSelectedOption || 1;
							state.dismiss = value.dismiss || false;
							if (value.progressColor !== undefined) {
								state.ProgressColor = value.progressColor;
							}

							console.log("✅ setDesignElement: Updated ALL announcement steps in AI tour", {
								totalSteps: state.toolTipGuideMetaData.length,
								announcementSteps: state.interactionData?.GuideStep?.filter(step => step.StepType === "Announcement").length,
								EnableProgress: value.progress || false,
								allAnnouncementStepsProgress: state.interactionData?.GuideStep?.filter(step => step.StepType === "Announcement").map(step => step?.Tooltip?.EnableProgress),
								globalState: {
									progress: state.progress,
									selectedOption: state.selectedOption,
									dismiss: state.dismiss,
									progressColor: state.ProgressColor
								}
							});
						}

						// Synchronize AI tour mixed step types data when design element is updated
						if (state.createWithAI && state.selectedTemplate === "Tour") {
							// For mixed-type AI tours, apply progress bar settings globally to ALL step types
							// This ensures consistent progress bar behavior across announcement, tooltip, banner, and hotspot steps
							state.syncGlobalProgressBarStateForAITour();
						}
					});
				},
				updateprogressclick: (value) => {
					set((state) => {
						const targetStep = state.currentStep - 1;

						// Store the previous state for undo
						const stateBefore = state.toolTipGuideMetaData[targetStep]?.design?.element
							? deepClone(state.toolTipGuideMetaData[targetStep].design.element)
							: {};

						state.toolTipGuideMetaData[targetStep].design.element = value;
						state.isUnSavedChanges = true;

						// Record the change for undo/redo
						recordChange(
							'ELEMENT_UPDATE',
							`Updated progress settings for Tooltip step ${state.currentStep}`,
							stateBefore,
							deepClone(value),
							`tooltip_progress_${state.currentStep}`,
							{
								guideType: 'Tooltip',
								currentStep: state.currentStep,
								progress: state.progress,
								selectedOption: state.selectedOption,
								dismiss: state.dismiss
							}
						);
					});
				},
				updatehotspots: (value) => {
					set((state) => {
						const targetStep = state.currentStep - 1;

						// Store the previous state for undo
						const stateBefore = state.toolTipGuideMetaData[targetStep]?.hotspots
							? deepClone(state.toolTipGuideMetaData[targetStep].hotspots)
							: {};

						state.toolTipGuideMetaData[targetStep].hotspots = value;

						// Only set unsaved changes if not returning from preview mode
						if (!state.isReturningFromPreview) {
							state.isUnSavedChanges = true;
						}

						// Record the change for undo/redo
						recordChange(
							'HOTSPOT_UPDATE',
							`Updated hotspot settings for step ${state.currentStep}`,
							stateBefore,
							deepClone(value),
							`hotspot_${state.currentStep}`,
							{
								guideType: state.selectedTemplate === 'Hotspot' ? 'Hotspot' : 'Tooltip',
								currentStep: state.currentStep
							}
						);
					});
				},

				updateChecklistCanvas: (value) => {
					set((state) => {
						const targetStep = state.currentStep - 1;

						// Store the previous state for undo
						const stateBefore = state.checklistGuideMetaData[0]?.canvas
							? deepClone(state.checklistGuideMetaData[0].canvas)
							: {};

						state.checklistGuideMetaData[0].canvas = value;
						state.isUnSavedChanges = true;

						// Record the change for undo/redo
						recordChange(
							'CANVAS_UPDATE',
							`Updated canvas settings for Checklist`,
							stateBefore,
							deepClone(value),
							`checklist_canvas`,
							{
								guideType: 'Checklist',
								currentStep: state.currentStep
							}
						);
					});
				},
				updateChecklistTitleSubTitle: (value) => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						state.checklistGuideMetaData[0].TitleSubTitle = value;
					});
				},
				updateChecklistLauncher: (value) => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						state.checklistGuideMetaData[0].launcher = value;

						// Also update canvas with launcher position offset values
						if (value?.launcherposition?.xaxisOffset || value?.launcherposition?.yaxisOffset) {
							state.checklistGuideMetaData[0].canvas = {
								...state.checklistGuideMetaData[0].canvas,
								xaxis: value.launcherposition.xaxisOffset || "10",
								yaxis: value.launcherposition.yaxisOffset || "10",
							};
						}
					});
				},
				updateChecklistCheckPoints: (value) => {
					set((state) => {
						const targetStep = state.currentStep - 1;

						state.checklistGuideMetaData[0].checkpoints = value;
					});
				},
				// updateChecklistCheckPointItem: (value) => {
				// 	set((state) => {
				// 		const targetStep = state.currentStep - 1;
				// 		const length = state.checklistGuideMetaData[0].checkpoints?.checkpointsList.length;
				// 		state.checklistGuideMetaData[0].checkpoints.checkpointsList[length] = value;
				// 	});
				// },

				updateChecklistCheckPointItem: (updatedCheckpoint) => {
					set((state) => {
						const checklist = state.checklistGuideMetaData[0].checkpoints.checkpointsList;

						// First try to find by id (for editing existing checkpoints)
						let index = checklist.findIndex((c) => c.id === updatedCheckpoint.id);

						// If not found by id, try to find by interaction (as fallback)
						if (index === -1) {
							index = checklist.findIndex((c) => c.interaction === updatedCheckpoint.interaction);
						}

						if (index !== -1) {
							// Update the existing item
							checklist[index] = { ...checklist[index], ...updatedCheckpoint };
						} else {
							// Add as a new item if not found
							checklist.push(updatedCheckpoint);
						}
					});
				},

				updateTooltipBtnContainer: (containerId = "", keyname, value) => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						if (containerId) {
							const targetContainerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
								(item) => item.id === containerId
							);

							const container = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex];
							const type = container?.type;

							if (targetContainerIndex !== -1 && type === "button") {
								if (keyname === "style") {
									// Handle style updates
									container.style = { ...container.style, ...value };
								} else if (keyname === "BackgroundColor") {
									// Handle BackgroundColor property
									container.BackgroundColor = value;
								} else {
									// Handle other properties
									// @ts-ignore
									container[keyname] = value;
								}
								// Only set unsaved changes if not returning from preview mode
								if (!state.isReturningFromPreview) {
									state.isUnSavedChanges = true;
								}
							}
						}
					});
				},
				uploadTooltipImage: (containerId, images) => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						if (containerId) {
							const targetContainerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
								(item) => item.id === containerId
							);
							const type = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].type;
							if (targetContainerIndex !== -1 && type === "image") {
								// @ts-ignore
								state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].images.push({
									...images,
								});
								state.isUnSavedChanges = targetContainerIndex === -1 ? false : true;
							}
						}
					});
				},
				cloneTooltipImage: (containerId) => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						if (containerId) {
							const targetContainerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
								(item) => item.id === containerId
							);

							if (targetContainerIndex === -1) return;

							const container = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex];
							const type = container.type;

							// Check if we've reached the section limit for this type
							const containers = state.toolTipGuideMetaData[targetStep].containers || [];
							const count = containers.filter(container => container.type === type).length;

							// If we've reached the limit, don't clone the section
							if (count >= MAX_SECTIONS[type]) {
								return;
							}

							if (type === "image") {
								// @ts-ignore

								const targetImageContainer = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex];
								state.toolTipGuideMetaData[targetStep].containers.splice(targetContainerIndex + 1, 0, {
									...targetImageContainer,
									id: crypto.randomUUID(),
								});
								state.isUnSavedChanges = true;
							}
						}
					});
				},
				deleteTooltipImageContainer: (containerId) => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						if (containerId) {
							const targetContainerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
								(item) => item.id === containerId
							);
							const type = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].type;
							if (targetContainerIndex !== -1 && type === "image") {
								// @ts-ignore

								state.toolTipGuideMetaData[targetStep].containers.splice(targetContainerIndex, 1);
								state.isUnSavedChanges = targetContainerIndex === -1 ? false : true;
							}
						}
					});
				},
				replaceTooltipImage: (containerId, images) => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						if (containerId) {
							const targetContainerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
								(item) => item.id === containerId
							);
							const type = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].type;
							if (targetContainerIndex !== -1 && type === "image") {
								// @ts-ignore

								state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].images = [
									{ ...images, id: getRandomID() },
								];
								state.isUnSavedChanges = targetContainerIndex === -1 ? false : true;
							}
						}
					});
				},
				updateTooltipImageContainer: (containerId, keyname, value) => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						if (containerId) {
							const targetContainerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
								(item) => item.id === containerId
							);
							const type = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].type;
							if (targetContainerIndex !== -1 && type === "image") {
								// @ts-ignore

								state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].style = {
									...state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].style,
									...value,
								};
								state.isUnSavedChanges = targetContainerIndex === -1 ? false : true;
							}
						}
					});
				},
				toggleTooltipImageFit: (containerId, mode) => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						if (containerId) {
							const targetContainerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
								(item) => item.id === containerId
							);
							const type = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].type;
							if (targetContainerIndex !== -1 && type === "image") {
								// @ts-ignore

								state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].images[0].objectFit =
									mode === "Fill" ? "cover" : "contain";
								state.isUnSavedChanges = targetContainerIndex === -1 ? false : true;
							}
						}
					});
				},
				handleTooltipRTEBlur: (containerId = "") => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						if (containerId) {
							const targetContainerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
								(item) => item.id === containerId
							);
							const type = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].type;
							if (targetContainerIndex !== -1 && type === "rte") {
								// @ts-ignore

								if (!state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].rteBoxValue) {
									state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].placeholder =
										DEF_PLACEHOLDER_MSG;
								}
								state.isUnSavedChanges = targetContainerIndex === -1 ? false : true;
							}
						}
					});
				},
				handleAnnouncementRTEBlur: (containerId = "") => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						if (containerId) {
							const targetContainerIndex = state.announcementGuideMetaData[targetStep].containers.findIndex(
								(item) => item.id === containerId
							);
							const type = state.announcementGuideMetaData[targetStep].containers[targetContainerIndex].type;
							if (targetContainerIndex !== -1 && type === "rte") {
								// @ts-ignore

								if (!state.announcementGuideMetaData[targetStep].containers[targetContainerIndex].rteBoxValue) {
									state.announcementGuideMetaData[targetStep].containers[targetContainerIndex].placeholder =
										DEF_PLACEHOLDER_MSG;
								}
								// Only set unsaved changes if not returning from preview mode or navigating between steps
								if (!state.isReturningFromPreview && !state.isNavigatingBetweenSteps) {
									state.isUnSavedChanges = targetContainerIndex === -1 ? false : true;
								}
							}
						}
					});
				},
				ensureAnnouncementRTEContainer: (stepIndex: number, isTourAnnouncement: boolean) => {
					let containers: any[] = [];
					const state = get();

					console.log("ensureAnnouncementRTEContainer called", {
						stepIndex,
						isTourAnnouncement,
						createWithAI: state.createWithAI,
						selectedTemplate: state.selectedTemplate,
						selectedTemplateTour: state.selectedTemplateTour
					});

					if (isTourAnnouncement) {
						// For Tour+Announcement, check toolTipGuideMetaData
						if (state.toolTipGuideMetaData[stepIndex]?.containers) {
							containers = state.toolTipGuideMetaData[stepIndex].containers.filter(
								(container: any) => container.type === "rte"
							);
							console.log("Found existing Tour+Announcement RTE containers:", containers);
						}

						// If no RTE containers found, create one
						if (containers.length === 0) {
							console.log("No Tour+Announcement RTE containers found, creating default");
							const defaultContainer = {
								id: getRandomID(),
								type: "rte",
								placeholder: "Start typing here...",
								rteBoxValue: "",
								style: {
									backgroundColor: "transparent",
								},
							};

							set((state) => {
								// Ensure the step exists and has containers array
								if (!state.toolTipGuideMetaData[stepIndex]) {
									state.toolTipGuideMetaData[stepIndex] = {
										containers: [],
										stepName: `Step ${stepIndex + 1}`,
										currentStep: stepIndex + 1,
										stepId: "",
										stepDescription: "",
										stepType: "Announcement",
										xpath: DEF_XPATH,
										id: getRandomID(),
										hotspots: HOTSPOT_DEFAULT_VALUE,
										canvas: CANVAS_DEFAULT_VALUE,
										design: {
											gotoNext: "",
											element: {
												progress: "",
												isDismiss: "",
											},
										},
									};
								}

								if (!state.toolTipGuideMetaData[stepIndex].containers) {
									state.toolTipGuideMetaData[stepIndex].containers = [];
								}

								state.toolTipGuideMetaData[stepIndex].containers.push(defaultContainer);
							});

							containers = [defaultContainer];
						}
					} else {
						// For pure Announcements, check announcementGuideMetaData
						if (state.announcementGuideMetaData[stepIndex]?.containers) {
							containers = state.announcementGuideMetaData[stepIndex].containers.filter(
								(container: any) => container.type === "rte"
							);
							console.log("Found existing pure Announcement RTE containers:", containers);
						}

						// If no RTE containers found, create one
						if (containers.length === 0) {
							console.log("No pure Announcement RTE containers found, creating default");
							const defaultContainer = {
								id: getRandomID(),
								type: "rte",
								placeholder: "Start typing here...",
								rteBoxValue: "",
								style: {
									backgroundColor: "transparent",
								},
							};

							set((state) => {
								// Ensure the step exists and has containers array
								if (!state.announcementGuideMetaData[stepIndex]) {
									state.announcementGuideMetaData[stepIndex] = {
										containers: [],
										stepName: `Step ${stepIndex + 1}`,
										stepDescription: "",
										stepType: "Announcement",
										currentStep: stepIndex + 1,
										xpath: DEF_XPATH,
										id: getRandomID(),
										hotspots: HOTSPOT_DEFAULT_VALUE,
										canvas: {},
										design: {},
									};
								}

								if (!state.announcementGuideMetaData[stepIndex].containers) {
									state.announcementGuideMetaData[stepIndex].containers = [];
								}

								state.announcementGuideMetaData[stepIndex].containers.push(defaultContainer);
							});

							containers = [defaultContainer];
						}
					}

					console.log("ensureAnnouncementRTEContainer returning containers:", containers);
					return containers;
				},
				ensureAnnouncementButtonContainer: (stepIndex: number, isTourAnnouncement: boolean) => {
					let containers: any[] = [];
					const state = get();



					if (isTourAnnouncement) {
						// For Tour+Announcement, check toolTipGuideMetaData
						if (state.toolTipGuideMetaData[stepIndex]?.containers) {
							containers = state.toolTipGuideMetaData[stepIndex].containers.filter(
								(container: any) => container.type === "button"
							);
							console.log("Found existing Tour+Announcement Button containers:", containers);
						}
					} else {
						// For pure Announcements, check announcementGuideMetaData
						if (state.announcementGuideMetaData[stepIndex]?.containers) {
							containers = state.announcementGuideMetaData[stepIndex].containers.filter(
								(container: any) => container.type === "button"
							);
							console.log("Found existing pure Announcement Button containers:", containers);
						}
					}

					console.log("ensureAnnouncementButtonContainer returning containers:", containers);
					return containers;
				},
				handleTooltipRTEValue: (containerId = "", value = "") => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						if (containerId) {
							const targetContainerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
								(item) => item.id === containerId
							);
							const type = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].type;
							if (targetContainerIndex !== -1 && type === "rte") {
								// @ts-ignore

								state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].rteBoxValue = value;

								// Synchronize AI tooltip data when RTE content is updated
								if (state.createWithAI && (state.selectedTemplate === "Tooltip" || state.selectedTemplate === "Tour")) {
									if (state.interactionData?.GuideStep?.[targetStep]) {
										const currentGuideStep = state.interactionData.GuideStep[targetStep];

										// Update TextFieldProperties from RTE containers
										const rteContainers = state.toolTipGuideMetaData[targetStep].containers.filter(c => c.type === "rte");
										if (rteContainers.length > 0) {
											currentGuideStep.TextFieldProperties = rteContainers.map((container: any) => ({
												Id: container.id,
												Text: container.rteBoxValue || "",
											}));
											// BUGFIX: Also update ButtonSection from button containers to preserve manually added buttons (for Tour+Announcement)
											if (state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Announcement") {
												const buttonContainers = state.toolTipGuideMetaData[targetStep].containers.filter(c => c.type === "button");
												if (buttonContainers.length > 0) {
													currentGuideStep.ButtonSection = buttonContainers.map((container: any) => ({
														Id: container.id,
														CustomButtons: container.buttons?.map((button: any) => ({
															ButtonStyle: button.type || "primary",
															ButtonName: button.name,
															Alignment: button.position || "center",
															ButtonId: button.id,
															BackgroundColor: container.style?.backgroundColor || "transparent",
															ButtonAction: {
																Action: button.actions?.value || button.action?.Action || "",
																ActionValue: button.actions?.tab || button.action?.ActionValue || "",
																TargetUrl: button.actions?.targetURL || button.action?.TargetUrl || "",
															},
															Padding: {
																Top: 0,
																Right: 0,
																Bottom: 0,
																Left: 0,
															},
															ButtonProperties: {
																Padding: 0,
																Width: 0,
																Font: 0,
																FontSize: 0,
																ButtonTextColor: button.style?.color || "#ffffff",
																ButtonBackgroundColor: button.style?.backgroundColor || "#5F9EA0",
																ButtonBorderColor: button.style?.borderColor || "#5F9EA0",
															},
														})) || [],
													}));
												}
											}

											console.log(`Synchronized RTE data for ${currentGuideStep.StepType} step ${targetStep}:`, {
												containerId: containerId,
												rteValue: value,
												TextFieldProperties: currentGuideStep.TextFieldProperties
											});
										}
									}
								}

								state.isUnSavedChanges = targetContainerIndex === -1 ? false : true;
							}
						}
					});
				},
				handleAnnouncementRTEValue: (containerId = "", value = "") => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						if (containerId) {
							const targetContainerIndex = state.announcementGuideMetaData[targetStep].containers.findIndex(
								(item) => item.id === containerId
							);
							const type = state.announcementGuideMetaData[targetStep].containers[targetContainerIndex].type;

							if (targetContainerIndex !== -1 && type === "rte") {
								// @ts-ignore

								state.announcementGuideMetaData[targetStep].containers[targetContainerIndex].rteBoxValue = value;

								// Synchronize AI announcement data when RTE content is updated
								if (state.createWithAI && (state.selectedTemplate === "Announcement" || state.selectedTemplateTour === "Announcement")) {
									if (state.interactionData?.GuideStep?.[targetStep]) {
										const currentGuideStep = state.interactionData.GuideStep[targetStep];

										// Update TextFieldProperties from RTE containers
										const rteContainers = state.announcementGuideMetaData[targetStep].containers.filter(c => c.type === "rte");
										if (rteContainers.length > 0) {
											currentGuideStep.TextFieldProperties = rteContainers.map((container: any) => ({
												Id: container.id,
												Text: container.rteBoxValue || "",
											}));
										}
										// BUGFIX: Also update ButtonSection from button containers to preserve manually added buttons
										const buttonContainers = state.announcementGuideMetaData[targetStep].containers.filter(c => c.type === "button");
										if (buttonContainers.length > 0) {
											currentGuideStep.ButtonSection = buttonContainers.map((container: any) => ({
												Id: container.id,
												CustomButtons: container.buttons?.map((button: any) => ({
													ButtonStyle: button.type || "primary",
													ButtonName: button.name,
													Alignment: button.position || "center",
													ButtonId: button.id,
													BackgroundColor: container.style?.backgroundColor || "transparent",
													ButtonAction: {
														Action: button.actions?.value || button.action?.Action || "",
														ActionValue: button.actions?.tab || button.action?.ActionValue || "",
														TargetUrl: button.actions?.targetURL || button.action?.TargetUrl || "",
													},
													Padding: {
														Top: 0,
														Right: 0,
														Bottom: 0,
														Left: 0,
													},
													ButtonProperties: {
														Padding: 0,
														Width: 0,
														Font: 0,
														FontSize: 0,
														ButtonTextColor: button.style?.color || "#ffffff",
														ButtonBackgroundColor: button.style?.backgroundColor || "#5F9EA0",
														ButtonBorderColor: button.style?.borderColor || "#5F9EA0",
													},
												})) || [],
											}));
										}
									}
								}

								state.isUnSavedChanges = targetContainerIndex === -1 ? false : true;
							}
						}
					});
				},
				handleRTEDeleteSection: (containerId) => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						if (containerId) {
							const targetContainerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
								(item) => item.id === containerId
							);
							const type = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex].type;
							if (targetContainerIndex !== -1 && type === "rte") {
								// @ts-ignore

								state.toolTipGuideMetaData[targetStep].containers.splice(targetContainerIndex, 1);
								state.isUnSavedChanges = targetContainerIndex === -1 ? false : true;
							}
						}
					});
				},
				// updateDesignelementInTooltip: (value) => {
				// 	set((state) => {
				// 		try {
				// 			const targetStep = state.currentStep - 1;

				// 			// Make sure toolTipGuideMetaData and design objects exist
				// 			if (!state.toolTipGuideMetaData[targetStep]) {
				// 				console.error('toolTipGuideMetaData not found for step', targetStep);
				// 				return state; // Return unchanged state
				// 			}

				// 			// Initialize design object if it doesn't exist
				// 			if (!state.toolTipGuideMetaData[targetStep].design) {
				// 				state.toolTipGuideMetaData[targetStep].design = {
				// 					gotoNext: {
				// 						NextStep: '',
				// 						ButtonId: '',
				// 						ElementPath: '',
				// 						ButtonName: ''
				// 					},
				// 					element: {
				// 						progress: '',
				// 						isDismiss: false
				// 					}
				// 				};
				// 			}

				// 			// Initialize gotoNext object if it doesn't exist
				// 			if (!state.toolTipGuideMetaData[targetStep].design.gotoNext) {
				// 				state.toolTipGuideMetaData[targetStep].design.gotoNext = {
				// 					NextStep: '',
				// 					ButtonId: '',
				// 					ElementPath: '',
				// 					ButtonName: ''
				// 				};
				// 			}

				// 			// Store the previous state for undo
				// 			const stateBefore = state.toolTipGuideMetaData[targetStep]?.design?.gotoNext
				// 				? deepClone(state.toolTipGuideMetaData[targetStep].design.gotoNext)
				// 				: {};

				// 			// Update the gotoNext object with the new values
				// 			state.toolTipGuideMetaData[targetStep].design.gotoNext = {
				// 				NextStep: value.NextStep || 'element',
				// 				ButtonId: value.ButtonId || '',
				// 				ElementPath: value.ElementPath || '',
				// 				ButtonName: value.ButtonName || ''
				// 			};

				// 			// Mark changes as unsaved
				// 			state.isUnSavedChanges = true;

				// 			// Record the change for undo/redo
				// 			recordChange(
				// 				'GOTO_NEXT_UPDATE',
				// 				`Updated button action for step ${state.currentStep}`,
				// 				stateBefore,
				// 				deepClone(state.toolTipGuideMetaData[targetStep].design.gotoNext),
				// 				`goto_next_${state.currentStep}`,
				// 				{
				// 					guideType: state.selectedTemplate || state.selectedTemplateTour,
				// 					currentStep: state.currentStep
				// 				}
				// 			);
				// 		} catch (error) {
				// 			console.error('Error updating design element in tooltip:', error);
				// 		}
				// 	});
				// },
				handleRTECloneSection: (containerId) => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						if (containerId) {
							const targetContainerIndex = state.toolTipGuideMetaData[targetStep].containers.findIndex(
								(item) => item.id === containerId
							);
							if (targetContainerIndex === -1) return;

							const container = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex];
							const type = container.type;

							// Check if we've reached the section limit for this type
							const containers = state.toolTipGuideMetaData[targetStep].containers || [];
							const count = containers.filter(container => container.type === type).length;

							// If we've reached the limit, don't clone the section
							if (count >= MAX_SECTIONS[type]) {
								return;
							}

							if (type === "rte") {
								// @ts-ignore

								const targetRTEContainer = state.toolTipGuideMetaData[targetStep].containers[targetContainerIndex];
								state.toolTipGuideMetaData[targetStep].containers.splice(targetContainerIndex + 1, 0, {
									...targetRTEContainer,
									id: crypto.randomUUID(),
								});
								// Only set unsaved changes if not returning from preview mode
								if (!state.isReturningFromPreview) {
									state.isUnSavedChanges = true;
								}
							}
						}
					});
				},
				setTooltipDataOnEdit: (data) => {
					set((state) => {
						state.toolTipGuideMetaData = data.map((step, index) => {
							const containers = [];

							containers.push(
								...step.TextFieldProperties.map((textField) => ({
									id: textField.Id,
									textBoxRef: null,
									style: {
										backgroundColor: "transparent",
									},
									type: "rte",
									placeholder: "Start typing here...",
									rteBoxValue: textField.Text || "",
								}))
							);

							containers.push(
								...step.ButtonSection.map((buttonSection) => ({
									id: buttonSection.Id || "",
									buttons: buttonSection.CustomButtons.map((button) => ({
										id: button.Id || getRandomID(),
										name: button.ButtonName || "",
										position: button.Alignment || "",
										type: button.ButtonStyle || "primary",
										isEditing: false,
										index: 0,
										style: {
											backgroundColor: button.ButtonProperties.ButtonBackgroundColor || "",
											borderColor: button.ButtonProperties.ButtonBorderColor || "",
											color: button.ButtonProperties.ButtonTextColor || "",
										},
										actions: {
											value: button.ButtonAction.Action,
											tab: button.ButtonAction.ActionValue,
											targetUrl: button.ButtonAction.TargetUrl,
											interactions: null,
										},
										survey: null,
									})),
									style: {
										backgroundColor: buttonSection.CustomButtons[0]?.BackgroundColor || "",
									},
									type: "button",
								}))
							);

							containers.push(
								...step.ImageProperties.map((image) => ({
									id: image.Id || "",
									images: image.CustomImage.map((img) => ({
										altText: img.AltText || "",
										id: image.Id || getRandomID(),
										url: img.Url || "",
										backgroundColor: img.BackgroundColor || "",
										objectFit: img.Fit || "contain",
									})),
									style: {
										backgroundColor: image.CustomImage[0]?.BackgroundColor || "transparent",
										height: image.CustomImage[0]?.SectionHeight || 200,
									},
									type: "image",
								}))
							);

							return {
								id: step.TextFieldProperties[0]?.Id || getRandomID(),
								currentStep: parseInt(step?.StepTitle?.replace("Step ", "")) || 1,
								xpath: {
									value: step.ElementPath || "",
									PossibleElementPath: step.PossibleElementPath || "",
									position: {
										x: step.Position?.XAxisOffset || 0,
										y: step.Position?.YAxisOffset || 0,
									},
								},
								containers,
								design: {
									gotoNext: {
										NextStep: step.Design?.GotoNext?.NextStep || "element",
										ButtonId: step.Design?.GotoNext?.ButtonId || "",
										elementPath: step.Design?.GotoNext?.ElementPath || "",
										ButtonName: step.Design?.GotoNext?.ButtonName || "",

									},
									element: {
										isDismiss: false,
										progress: step?.Tooltip?.ProgressTemplate || "",
									},
								},
								canvas: {
									position: step.Canvas?.Position || "",
									padding: step.Canvas?.Padding || "0",
									zIndex: step.Canvas?.Zindex || "0",
									borderRadius: step.Canvas?.Radius || step.Canvas?.BorderRadius || "0",
									borderSize: step.Canvas?.BorderSize || "0",
									borderColor: step.Canvas?.BorderColor || "transparent",
									backgroundColor: step.Canvas?.BackgroundColor || "#FFFFFF",
									width: step.Canvas?.Width || "300px",
									xaxis: step.Canvas?.XAxisOffset || "4px",
									yaxis: step.Canvas?.YAxisOffset || "4px",
								},
								stepName: step?.StepTitle || `Step ${index + 1}`,
								stepDescription: step?.Description || step?.stepDescription,
								stepType: step?.StepType,
								stepId: step?.StepId,
								hotspots: {
									XPosition: step.Hotspot?.HotspotPosition?.XOffset || "4",
									YPosition: step.Hotspot?.HotspotPosition?.YOffset || "4",
									Type: step.Hotspot?.Type || "Question",
									Color: step.Hotspot?.Color || "#bb0c0c",
									Size: step.Hotspot?.Size || "16",
									PulseAnimation: step.Hotspot?.PulseAnimation || true,

									stopAnimationUponInteraction:
										step.Hotspot?.StopAnimation !== undefined ? step.Hotspot.StopAnimation : false,
									ShowUpon: step.Hotspot?.ShowUpon || "Hovering Hotspot",
									ShowByDefault: step.Hotspot?.ShowByDefault || false,
								},
							};
						});

						// Set the state for the current step and tooltip settings
						//state.elementSelected = true;
						const opt = { Template1: "1", Template2: "2", Template3: "3" };

						// Set progress settings from API response
						const currentStepData = data[state.currentStep - 1];



						// Set overlay settings from API response
						if (currentStepData?.Overlay !== undefined) {
							state.overlayEnabled = currentStepData.Overlay;
						} else if (currentStepData?.Design?.BackdropShadow !== undefined) {
							state.overlayEnabled = currentStepData.Design.BackdropShadow;
						}

						// Set page interaction setting from API response
						if (currentStepData?.Tooltip?.InteractWithPage !== undefined) {
							state.pageinteraction = currentStepData.Tooltip.InteractWithPage;
						}

						// Set progress color from API response
						if (currentStepData?.Modal?.ProgressColor) {
							state.ProgressColor = currentStepData.Modal.ProgressColor;
						}

						// Set dismiss setting from API response
						if (currentStepData?.Modal?.DismissOption !== undefined) {
							state.dismiss = currentStepData.Modal.DismissOption;
							// Also update dismissData for the UI
							state.dismissData = {
								Actions: "",
								DisplayType: "Cross Icon",
								Color: "#000000",
								DontShowAgain: true,
								dismisssel: currentStepData.Modal.DismissOption,
							};
						}


						state.tooltipPosition = data[state.currentStep - 1]?.Canvas?.Position || "middle-center";
						state.tooltippadding = data[state.currentStep - 1]?.Canvas?.Padding || "12px";
						state.tooltipborderradius = data[state.currentStep - 1]?.Canvas?.Radius || "8px";
						state.tooltipbordersize = data[state.currentStep - 1]?.Canvas?.borderSize || "0px";
						state.tooltipBordercolor = data[state.currentStep - 1]?.Canvas?.BorderColor || "transparent";
						state.tooltipBackgroundcolor = data[state.currentStep - 1]?.Canvas?.BackgroundColor || "#FFFFFF";
						state.tooltipWidth = `${data[state.currentStep - 1]?.Canvas?.Width}` || "200px";
						state.tooltipXaxis = `${data[state.currentStep - 1]?.Canvas?.XAxisOffset}` || "4px";
						state.tooltipYaxis = `${data[state.currentStep - 1]?.Canvas?.YAxisOffset}` || "4px";
					});
				},
				setChecklistDataOnEdit: (data) => {
					set((state) => {
						state.checklistGuideMetaData = data.map((gStep, index) => {
							return {
								id: getRandomID(),

								canvas: {
									borderColor: gStep.Canvas?.BorderColor || "transparent",
									backgroundColor: gStep.Canvas?.BackgroundColor || "#FFFFFF",
									primaryColor: gStep.Canvas?.PrimaryColor || "#5F9EA0",
									height: gStep?.Canvas?.Height || "500px",
									width: gStep.Canvas?.Width || "930px",
									xaxis: gStep.Canvas?.XAxisOffset || gStep?.Launcher?.LauncherPosition?.XAxisOffset || "10px",
									yaxis: gStep.Canvas?.YAxisOffset || gStep?.Launcher?.LauncherPosition?.YAxisOffset || "10px",
									openByDefault: gStep?.Canvas?.OpenByDefault || false,
									hideAfterCompletion: gStep?.Canvas?.HideAfterCompletion,
									cornerRadius: gStep?.Canvas?.CornerRadius || "12px",
									borderWidth: gStep?.Canvas?.BorderWidth || "0",
								},

								launcher: {
									type: gStep?.Launcher?.Type,
									icon: gStep?.Launcher?.Icon,
									iconColor: gStep?.Launcher?.IconColor,
									launcherColor: gStep?.Launcher?.LauncherColor,
									text: gStep?.Launcher?.Text,
									textColor: gStep?.Launcher?.TextColor,
									launcherposition: {
										left: gStep?.Launcher?.LauncherPosition?.Left,
										right: gStep?.Launcher?.LauncherPosition?.Right,
										xaxisOffset: gStep?.Launcher?.LauncherPosition?.XAxisOffset,
										yaxisOffset: gStep?.Launcher?.LauncherPosition?.YAxisOffset,
									},
									notificationBadge: gStep?.Launcher?.NotificationBadge,
									notificationBadgeColor: gStep?.Launcher?.NotificationBadgeColor,
									notificationTextColor: gStep?.Launcher?.NotificationBadgeText || gStep?.Launcher?.NotificationTextColor,
								},

								TitleSubTitle: {
									title: gStep?.TitleSubTitle?.Title,
									titleColor: gStep?.TitleSubTitle?.TitleColor,
									titleBold: gStep?.TitleSubTitle?.TitleBold,
									titleItalic: gStep?.TitleSubTitle?.TitleItalic,
									subTitle: gStep?.TitleSubTitle?.SubTitle,
									subTitleColor: gStep?.TitleSubTitle?.SubTitleColor,
									subTitleBold: gStep?.TitleSubTitle?.SubTitleBold,
									subTitleItalic: gStep?.TitleSubTitle?.SubTitleItalic,
								},

								checkpoints: {
									checkpointsList: Array.isArray(gStep?.Checkpoint?.CheckpointItem)
										? gStep?.Checkpoint?.CheckpointItem.map((item: any) => ({
												id: item?.Id || item?.id,
												interaction: item?.Interaction || item?.interaction,
												title: item?.Title || item?.title,
												description: item?.Description || item?.description,
												redirectURL: item?.RedirectURL || item?.redirectURL,
												icon: item?.Icon || item?.icon,
												supportingMedia: item?.SupportingMedia || item?.supportingMedia,
												mediaTitle: item?.MediaTitle || item?.mediaTitle,
												mediaDescription: item?.MediaDescription || item?.mediaDescription,
										  }))
										: [], // Ensure it remains an array
									checkpointTitles: gStep?.Checkpoint?.CheckpointTitleColor ?? "",
									checkpointsDescription: gStep?.Checkpoint?.CheckpointDescriptionColor ?? "",
									checkpointsIcons: gStep?.Checkpoint?.CheckpointIconsColor ?? "",
									unlockCHeckpointInOrder: gStep?.Checkpoint?.UnlockCheckpointsInOrder ?? false,
									message: gStep?.Checkpoint?.Message ?? "complete items in order",
								},
							};
						});
					});
				},
				setAnnouncementDataOnEdit: (data) => {
					set((state) => {
						state.announcementGuideMetaData = data.map((step, index) => {
							const containers = [];

							containers.push(
								...step.TextFieldProperties.map((textField) => ({
									id: textField.Id,
									textBoxRef: null,
									style: {
										backgroundColor: "transparent",
									},
									type: "rte",
									placeholder: "Start typing here...",
									rteBoxValue: textField.Text || "",
								}))
							);

							containers.push(
								...step.ButtonSection.map((buttonSection) => ({
									id: buttonSection.Id || "",
									buttons: buttonSection.CustomButtons.map((button) => ({
										id: button.ButtonId || button.Id || getRandomID(),
										name: button.ButtonName || "",
										position: button.Alignment || "",
										type: button.ButtonStyle || "primary",
										isEditing: false,
										index: 0,
										style: {
											backgroundColor: button.ButtonProperties.ButtonBackgroundColor || "",
											borderColor: button.ButtonProperties.ButtonBorderColor || "",
											color: button.ButtonProperties.ButtonTextColor || "",
										},
										actions: {
											value: button.ButtonAction.Action,
											tab: button.ButtonAction.ActionValue,
											targetUrl: button.ButtonAction.TargetUrl,
											interactions: null,
										},
										survey: null,
									})),
									style: {
										backgroundColor: buttonSection.CustomButtons[0]?.BackgroundColor || "",
									},
									type: "button",
								}))
							);

							containers.push(
								...step.ImageProperties.map((image) => ({
									id: image.Id || "",
									images: image.CustomImage.map((img) => ({
										altText: img.AltText || "",
										id: image.Id || getRandomID(),
										url: img.Url || "",
										backgroundColor: img.BackgroundColor || "",
										objectFit: img.Fit || "contain",
									})),
									style: {
										backgroundColor: image.CustomImage[0]?.BackgroundColor || "transparent",
										height: image.CustomImage[0]?.SectionHeight || 200,
									},
									type: "image",
								}))
							);

							return {
								id: step.TextFieldProperties[0]?.Id || getRandomID(),
								currentStep: parseInt(step?.StepTitle?.replace("Step ", "")) || 1,
								xpath: {
									value: step.ElementPath || "",
									PossibleElementPath: step.PossibleElementPath || "",
									position: {
										x: step.Position?.XAxisOffset || 0,
										y: step.Position?.YAxisOffset || 0,
									},
								},
								containers,
								design: {
									gotoNext: {
										NextStep: step.Design?.GotoNext?.NextStep || "element",
										ButtonId: step.Design?.GotoNext?.ButtonId || "",
										elementPath: step.Design?.GotoNext?.ElementPath || "",
										buttonName: step.Design?.GotoNext?.ButtonName || "",
									},
									element: {
										isDismiss: false,
										progress: step?.Tooltip?.ProgressTemplate || "",
									},
								},
								canvas: {
									position: step.Canvas?.Position || "middle-center",
									padding: step.Canvas?.Padding || "4",
									zIndex: step.Canvas?.Zindex || "0",
									borderRadius: step.Canvas?.Radius || "4",
									borderSize: step.Canvas?.BorderSize || "0",
									borderColor: step.Canvas?.BorderColor || "transparent",
									backgroundColor: step.Canvas?.BackgroundColor || "#FFFFFF",
									width: step.Canvas?.Width || "500px",
								},
								stepName: step?.StepTitle || `Step ${index + 1}`,
								stepDescription: step?.stepDescription,
								stepType: step?.StepType,
								stepId: step?.StepId,
								hotspots: {
									XPosition: step.Hotspot?.HotspotPosition?.XOffset || "4",
									YPosition: step.Hotspot?.HotspotPosition?.YOffset || "4",
									Type: step.Hotspot?.Type || "Question",
									Color: step.Hotspot?.Color || "#bb0c0c",
									Size: step.Hotspot?.Size || "16",
									PulseAnimation: step.Hotspot?.PulseAnimation || true,

									stopAnimationUponInteraction:
										step.Hotspot?.StopAnimation !== undefined ? step.Hotspot.StopAnimation : false,
									ShowUpon: step.Hotspot?.ShowUpon || "Hovering Hotspot",
									ShowByDefault: step.Hotspot?.ShowByDefault || false,
								},
							};
						});

						// Set the state for the current step and tooltip settings
						//state.elementSelected = true;
						const opt = { Template1: "1", Template2: "2", Template3: "3" };

						const currentStepData = data[state.currentStep - 1];

						// For announcements, load overlay settings from API data and apply globally
						if (state.selectedTemplate === "Announcement") {
							// Load overlay settings from the first step of the saved guide data
							const firstStepData = data[0];
							const savedOverlayEnabled = firstStepData?.Overlay !== undefined
								? firstStepData.Overlay
								: (firstStepData?.Design?.BackdropShadow !== undefined ? firstStepData.Design.BackdropShadow : state.overlayEnabled);
							const savedPageInteraction = firstStepData?.Tooltip?.InteractWithPage !== undefined
								? firstStepData.Tooltip.InteractWithPage
								: state.pageinteraction;

							console.log("🔄 setAnnouncementDataOnEdit: Loading overlay settings from API data", {
								currentStep: state.currentStep,
								savedOverlayEnabled,
								savedPageInteraction,
								firstStepOverlay: firstStepData?.Overlay,
								firstStepBackdropShadow: firstStepData?.Design?.BackdropShadow,
								firstStepInteractWithPage: firstStepData?.Tooltip?.InteractWithPage
							});

							// Set the global overlay state from the loaded data (skip mutual exclusivity during loading)
							state.overlayEnabled = savedOverlayEnabled;
							state.pageinteraction = savedPageInteraction;

							// Apply global overlay synchronization to ensure all steps have consistent settings
							state.syncGlobalOverlayStateForAnnouncements();
						} else {
							// For non-announcements, load overlay settings from individual step data
							if (currentStepData?.Overlay !== undefined) {
								state.overlayEnabled = currentStepData.Overlay;
							} else if (currentStepData?.Design?.BackdropShadow !== undefined) {
								state.overlayEnabled = currentStepData.Design.BackdropShadow;
							}
						}

						state.tooltipPosition = data[state.currentStep - 1]?.Canvas?.Position || "middle-center";
						state.tooltippadding = data[state.currentStep - 1]?.Canvas?.Padding || "12px";
						state.tooltipborderradius = data[state.currentStep - 1]?.Canvas?.Radius || "8px";
						state.tooltipbordersize = data[state.currentStep - 1]?.Canvas?.borderSize || "0px";
						state.tooltipBordercolor = data[state.currentStep - 1]?.Canvas?.BorderColor || "transparent";
						state.tooltipBackgroundcolor = data[state.currentStep - 1]?.Canvas?.BackgroundColor || "#FFFFFF";
						state.tooltipWidth = `${data[state.currentStep - 1]?.Canvas?.Width}` || "500px";
					});
				},

				setTourDataOnEdit: (data, type) => {
					set((state) => {
						state.toolTipGuideMetaData = data.map((step, index) => {
							const containers = [];
							containers.push(
								...step.TextFieldProperties.map((textField) => ({
									id: textField.Id,
									textBoxRef: null,
									style: {
										backgroundColor: "transparent",
									},
									type: "rte",
									placeholder: "Start typing here...",
									rteBoxValue: textField.Text || "",
								}))
							);

							containers.push(
								...step.ButtonSection.map((buttonSection) => ({
									id: buttonSection.Id || "",
									buttons: buttonSection.CustomButtons.map((button) => ({
										id: button.ButtonId || getRandomID(),
										name: button.ButtonName || "",
										position: button.Alignment || "",
										type: button.ButtonStyle || "primary",
										isEditing: false,
										index: 0,
										style: {
											backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || "",
											borderColor: button.ButtonProperties?.ButtonBorderColor || "",
											color: button.ButtonProperties?.ButtonTextColor || "",
										},
										actions: {
											value: button.ButtonAction?.Action,
											tab: button.ButtonAction?.ActionValue,
											targetUrl: button.ButtonAction?.TargetUrl,
											interactions: null,
										},
										survey: null,
									})),
									style: {
										backgroundColor: buttonSection.CustomButtons[0]?.BackgroundColor || "",
									},
									type: "button",
								}))
							);

							containers.push(
								...step.ImageProperties.map((image) => ({
									id: image.Id || "",
									images: image.CustomImage.map((img) => ({
										altText: img.AltText || "",
										id: image.Id || getRandomID(),
										url: img.Url || "",
										backgroundColor: img.BackgroundColor || "",
										objectFit: img.Fit || "contain",
									})),
									style: {
										backgroundColor: image.CustomImage[0]?.BackgroundColor || "transparent",
										height: image.CustomImage[0]?.SectionHeight || 200,
									},
									type: "image",
								}))
							);

							return {
								id: step.TextFieldProperties[0]?.Id || getRandomID(),
								currentStep: parseInt(step?.StepTitle?.replace("Step ", "")) || 1,
								xpath: {
									value: step.ElementPath || "",
									possibleElementPath: step.PossibleElementPath || "",
									position: {
										x: step.Position?.XAxisOffset || 0,
										y: step.Position?.YAxisOffset || 0,
									},
								},
								containers,
								design: {
									gotoNext: {
										NextStep: step?.Design?.GotoNext?.NextStep || "element",
										ButtonId: step?.Design?.GotoNext?.ButtonId || "",
										elementPath: step?.Design?.GotoNext?.ElementPath || "",
										ButtonName: step?.Design?.GotoNext?.ButtonName || "",
									},
									element: {
										isDismiss: false,
										progress: step?.Tooltip?.ProgressTemplate || "",
									},
								},
								canvas: {
									position: step.Canvas?.Position || "",
									padding: step.Canvas?.Padding || "0",
									zIndex: step.Canvas?.Zindex || "0",
									borderRadius: step.Canvas?.Radius || "0",
									borderSize: step.Canvas?.BorderSize || "0",
									borderColor: step.Canvas?.BorderColor || "transparent",
									backgroundColor: step.Canvas?.BackgroundColor || "#FFFFFF",
									width: step.Canvas?.Width || "200px",
									xaxis: step.Canvas?.XAxisOffset || "4px",
									yaxis: step.Canvas?.YAxisOffset || "4px",
								},
								stepName: step?.StepTitle || `Step ${index + 1}`,
								stepDescription: step?.stepDescription,
								stepType: step?.StepType,
								stepId: step?.StepId,
								hotspots: {
									XPosition: step.Hotspot?.HotspotPosition?.XOffset || "4",
									YPosition: step.Hotspot?.HotspotPosition?.YOffset || "4",
									Type: step.Hotspot?.Type || "Question",
									Color: step.Hotspot?.Color || "#bb0c0c",
									Size: step.Hotspot?.Size || "16",
									PulseAnimation: step.Hotspot?.PulseAnimation || true,
									stopAnimationUponInteraction:
										step.Hotspot?.StopAnimation !== undefined ? step.Hotspot.StopAnimation : false,
									ShowUpon: step.Hotspot?.ShowUpon || "Hovering Hotspot",
									ShowByDefault: step.Hotspot?.ShowByDefault || false,
								},
							};
						});

						// Set the state for the current step and tooltip settings
						//state.elementSelected = true;
						const opt = { Template1: "1", Template2: "2", Template3: "3" };
//	state.selectedOption = +opt[data[state.currentStep - 1]?.Tooltip?.ProgressTemplate] || "dismiss";
						state.tooltipPosition = data[state.currentStep - 1]?.Canvas?.Position || "middle-center";
						state.tooltippadding = data[state.currentStep - 1]?.Canvas?.Padding || "12px";
						state.tooltipborderradius = data[state.currentStep - 1]?.Canvas?.Radius || "8px";
						state.tooltipbordersize = data[state.currentStep - 1]?.Canvas?.borderSize || "0px";
						state.tooltipBordercolor = data[state.currentStep - 1]?.Canvas?.BorderColor || "transparent";
						state.tooltipBackgroundcolor = data[state.currentStep - 1]?.Canvas?.BackgroundColor || "#FFFFFF";
						state.tooltipWidth = `${data[state.currentStep - 1]?.Canvas?.Width}` || "300px";
						state.tooltipXaxis = `${data[state.currentStep - 1]?.Canvas?.XAxisOffset}` || "4px";
						state.tooltipYaxis = `${data[state.currentStep - 1]?.Canvas?.YAxisOffset}` || "4px";
					});
				},

				setXpathToTooltipMetaData: (data) => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						state.toolTipGuideMetaData[targetStep].xpath = data;

						// Synchronize AI tooltip data when element path is updated
						if (state.createWithAI && (state.selectedTemplate === "Tooltip" || state.selectedTemplate === "Hotspot")) {
							if (state.interactionData?.GuideStep?.[targetStep]) {
								const currentGuideStep = state.interactionData.GuideStep[targetStep];

								// Update ElementPath
								currentGuideStep.ElementPath = data.value;
								currentGuideStep.PossibleElementPath = data.PossibleElementPath;
							}
						}

						// Synchronize AI tour data when element path is updated for tooltip steps in tours
						if (state.createWithAI && state.selectedTemplate === "Tour" && (state.selectedTemplateTour === "Tooltip" || state.selectedTemplateTour === "Hotspot")) {
							if (state.interactionData?.GuideStep?.[targetStep]) {
								const currentGuideStep = state.interactionData.GuideStep[targetStep];

								// Update ElementPath in interactionData
								currentGuideStep.ElementPath = data.value;
								currentGuideStep.PossibleElementPath = data.PossibleElementPath;

								console.log("Updated XPath for AI tour tooltip step", targetStep, {
									ElementPath: data.value,
									PossibleElementPath: data.PossibleElementPath
								});
							}
						}

						// Synchronize AI tour data when element path is updated for hotspot steps in tours
						if (state.createWithAI && state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Hotspot") {
							if (state.interactionData?.GuideStep?.[targetStep]) {
								const currentGuideStep = state.interactionData.GuideStep[targetStep];

								// Update ElementPath in interactionData
								currentGuideStep.ElementPath = data.value;
								currentGuideStep.PossibleElementPath = data.PossibleElementPath;

								console.log("Updated XPath for AI tour hotspot step", targetStep, {
									ElementPath: data.value,
									PossibleElementPath: data.PossibleElementPath
								});
							}
						}

						// Mark as unsaved changes
						state.isUnSavedChanges = true;
					});
				},
				setOpenTooltip: (data) => {
					set((state) => {
						state.openTooltip = data;
					});
				},
				setTooltipPositionByXpath: (value) => {
					set((state) => {
						state.tooltip.position = value?.position;
						state.tooltip.visible = true;
					});
				},
				setTooltipElementOptions: (keyname, selectedTemplate, selectedTemplateTour, option) => {
					set((state) => {
						if (keyname === "progress" && (selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip")) {
							switch (option) {
								case "1":
									state.toolTipGuideMetaData[state.currentStep - 1].design.element.progress = "Template1";
									break;
								case "2":
									state.toolTipGuideMetaData[state.currentStep - 1].design.element.progress = "Template2";
									break;
								case "3":
									state.toolTipGuideMetaData[state.currentStep - 1].design.element.progress = "Template3";
									break;
								default:
									state.toolTipGuideMetaData[state.currentStep - 1].design.element.progress = "";
									break;
							}
						}
						if (keyname === "dismiss" && (selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip")) {
						}
					});
				},
				createNewTooltipSteps: (stepName, stepType, description) => {
					set((state) => {
						const stepId = state.steps.find((s) => s.name === stepName)?.id;

						// For AI-created tooltips, sync data when adding new steps
						if (state.createWithAI && state.selectedTemplate === "Tooltip") {
							// Call syncAITooltipContainerData to ensure current step data is saved
							const currentStepIndex = state.currentStep - 1;
							const currentTooltipMetadata = state.toolTipGuideMetaData[currentStepIndex];

							if (currentTooltipMetadata && state.interactionData?.GuideStep?.[currentStepIndex]) {
								const currentGuideStep = state.interactionData.GuideStep[currentStepIndex];

								// Update TextFieldProperties from RTE containers
								const rteContainers = currentTooltipMetadata.containers.filter(c => c.type === "rte");
								if (rteContainers.length > 0) {
									currentGuideStep.TextFieldProperties = rteContainers.map((container: any) => ({
										Id: container.id,
										Text: container.rteBoxValue || "",
									}));
								}

								// Update ButtonSection from button containers
								const buttonContainers = currentTooltipMetadata.containers.filter(c => c.type === "button");
								if (buttonContainers.length > 0) {
									console.log("Processing button containers:", buttonContainers);
									currentGuideStep.ButtonSection = buttonContainers.map((container: any) => ({
										Id: container.id,
										CustomButtons: container.buttons?.map((button: any) => {
											const buttonData = {
												ButtonStyle: button.type || "primary",
												ButtonName: button.name,
												Alignment: button.position || "center",
												ButtonId: button.id,
												BackgroundColor: container.style?.backgroundColor || "transparent",
												ButtonAction: {
													Action: button.actions?.value || "",
													ActionValue: button.actions?.tab || "",
													TargetUrl: button.actions?.targetURL || "",
												},
												Padding: {
													Top: 0,
													Right: 0,
													Bottom: 0,
													Left: 0,
												},
												ButtonProperties: {
													Padding: 0,
													Width: 0,
													Font: 0,
													FontSize: 0,
													ButtonTextColor: button.style?.color || "#ffffff",
													ButtonBackgroundColor: button.style?.backgroundColor || "#5F9EA0",
													ButtonBorderColor: button.style?.borderColor || "#5F9EA0",
												},
											};
											console.log("Created button data:", buttonData);
											return buttonData;
										}) || [],
									}));
								}

								// Update Canvas settings
								currentGuideStep.Canvas = {
									Position: currentTooltipMetadata.canvas?.position || "absolute",
									Width: currentTooltipMetadata.canvas?.width || "300px",
									Padding: currentTooltipMetadata.canvas?.padding || "2px",
									BorderRadius: currentTooltipMetadata.canvas?.borderRadius || "8px",
									BorderSize: currentTooltipMetadata.canvas?.borderSize || "0px",
									BorderColor: currentTooltipMetadata.canvas?.borderColor || "transparent",
									BackgroundColor: currentTooltipMetadata.canvas?.backgroundColor || "#ffffff",
								};
							}
						}

						state.toolTipGuideMetaData.push({
							containers:
								stepType === "Tooltip" || stepType === ""
									? [
											{
												id: getRandomID(),
												type: "rte",
												placeholder: DEF_PLACEHOLDER_MSG,
												rteBoxValue: "",
											},
									  ]
									: stepType === "Announcement"
									? [
											{
												id: getRandomID(),
												type: "rte",
												placeholder: DEF_PLACEHOLDER_MSG,
												rteBoxValue: "",
											},
											{
												type: "button",
												...BUTTON_CONT_DEF_VALUE_1,
												id: getRandomID(),
											},
											{
												...IMG_CONT_DEF_VALUE,
												type: "image",
												id: getRandomID(),
											},
									  ]
									: [
											{
												id: getRandomID(),
												type: "rte",
												placeholder: DEF_PLACEHOLDER_MSG,
												rteBoxValue: "",
											},
									  ],
							currentStep: state.currentStep + 1,
							stepName,
							stepDescription: description,
							stepType: stepType,
							stepId: stepId,
							stepTargetURL: "",
							xpath: DEF_XPATH,
							id: crypto.randomUUID(),
							design: {
								gotoNext: {
									NextStep: "",
									ButtonId: "",
									elementPath: "",
									ButtonName: "",
								},
								element: {
									isDismiss: false,
									progress: "",
								},
							},
							canvas:
								stepType === "Tooltip" || stepType === ""
									? CANVAS_DEFAULT_VALUE
									: stepType === "Banner"
									? CANVAS_DEFAULT_VALUE_Banner
									: {},
							hotspots: HOTSPOT_DEFAULT_VALUE,
						});
					});
				},
				updateStepNameInTooltip: (currentStepId, newStepname) => {
					set((state) => {
						const targetStep = state.steps.findIndex((item) => item.id === currentStepId);
						if (targetStep !== -1) {
							// Update the step name in toolTipGuideMetaData
							if (state.toolTipGuideMetaData[targetStep]) {
							state.toolTipGuideMetaData[targetStep].stepName = newStepname;

								// Also update the stepId to ensure consistency
								state.toolTipGuideMetaData[targetStep].stepId = currentStepId;
						}

							// Ensure no duplicate steps exist in toolTipGuideMetaData
							const uniqueMetadata: any[] = [];
							const metadataIds = new Set<string>();

							state.toolTipGuideMetaData.forEach((metadata, index) => {
								// If this is the step we're updating or it has a unique ID
								if (index === targetStep || (metadata.stepId && !metadataIds.has(metadata.stepId))) {
									if (metadata.stepId) {
										metadataIds.add(metadata.stepId);
									}
									uniqueMetadata.push(metadata);
								}
					});

							// Replace the toolTipGuideMetaData with the deduplicated array
							state.toolTipGuideMetaData = uniqueMetadata;
						}
					});
				},
				resetTooltipMetaData: () => {
					set((state) => {
						state.toolTipGuideMetaData = [
							{
								containers: [],
								stepName: "Step 1",
								stepDescription: "",
								stepType: "",
								stepId: "",
								stepTargetURL: "",
								currentStep: 1,
								xpath: DEF_XPATH,
								id: crypto.randomUUID(),
								hotspots: HOTSPOT_DEFAULT_VALUE,
								// Explicitly initialize design element properties
								design: {
									gotoNext: {
										ButtonId: '',
										ElementPath: '',
										NextStep: '',
										ButtonName: '',
										Id: ''
									},
									element: {
										progress: '',
										isDismiss: false,
										progressSelectedOption: 1,
										progressColor: "var(--primarycolor)"
									}
								}
							},
						];

						// Reset canvas settings
						state.tooltipPosition = "absolute";
						state.tooltipXaxis = "4px";
						state.tooltipYaxis = "4px";
						state.tooltipWidth = "300px";
						state.tooltippadding = "2px";
						state.tooltipborderradius = "8px";
						state.tooltipbordersize = "0px";
						state.tooltipBordercolor = "transparent";
						state.tooltipBackgroundcolor = "#FFFFFF";
						state.tooltipWidth = "300px";
						state.tooltipXaxis = "4px";
						state.tooltipYaxis = "4px";
						state.dismissData = true;

						// Reset the progress and dismiss state in the store
						state.progress = false;
						state.dismiss = false;
						state.selectedOption = 1;
						state.ProgressColor = "var(--primarycolor)";
						state.pageinteraction = false;
					});
				},

				// Function to save current URL for the current step only (used by main save button)
				saveCurrentStepURL: () => {
					set((state) => {
						const targetStep = state.currentStep - 1;
						const currentUrl = window.location.href;

						// Ensure the step exists in toolTipGuideMetaData
						if (state.toolTipGuideMetaData[targetStep]) {
							state.toolTipGuideMetaData[targetStep].stepTargetURL = currentUrl;
							state.isUnSavedChanges = true;
							console.log(`✅ Saved URL for current step ${state.currentStep}: ${currentUrl}`);
						}
					});
				},

				// Function to synchronize AI tooltip data with tooltipguidemetadata
				syncAITooltipDataForPreview: () => {
					set((state) => {
						// Only sync if we have AI data and selected template is tooltip
						if (!state.createWithAI || !state.interactionData || (state.selectedTemplate !== "Tooltip" && state.selectedTemplate !== "Hotspot")) {
							return;
						}

						const aiGuideSteps = state.interactionData?.GuideStep || [];
						if (aiGuideSteps.length === 0) {
							return;
						}

						// Create tooltip metadata from AI data
						const newTooltipMetadata = aiGuideSteps.map((step: any, index: number) => {
							const containers = [];

							// Add RTE containers from TextFieldProperties
							if (step.TextFieldProperties && step.TextFieldProperties.length > 0) {
								step.TextFieldProperties.forEach((textField: any) => {
									containers.push({
										id: textField.Id || getRandomID(),
										type: "rte",
										placeholder: DEF_PLACEHOLDER_MSG,
										rteBoxValue: textField.Text || "",
										style: {
											backgroundColor: "transparent",
										},
									});
								});
							}

							// Add button containers from ButtonSection
							if (step.ButtonSection && step.ButtonSection.length > 0) {
								step.ButtonSection.forEach((buttonSection: any) => {
									if (buttonSection.CustomButtons && buttonSection.CustomButtons.length > 0) {
										const buttons = buttonSection.CustomButtons.map((button: any) => ({
											id: button.ButtonId || getRandomID(),
											name: button.ButtonName || "Button",
											position: button.Alignment || "center",
											type: button.ButtonStyle || "primary",
											isEditing: false,
											index: 0,
											style: {
												backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || button.BackgroundColor || "#5F9EA0",
												borderColor: button.ButtonProperties?.ButtonBorderColor || button.BorderColor || "#5F9EA0",
												color: button.ButtonProperties?.ButtonTextColor || button.TextColor || "#ffffff",
											},
											actions: {
												value: button.ButtonAction?.Action || button.Actions?.value || "close",
												targetURL: button.ButtonAction?.TargetUrl || button.Actions?.targetURL || "",
												tab: button.ButtonAction?.ActionValue || button.Actions?.tab || "new-tab",
												interaction: button.Actions?.interaction || null,
											},
											survey: null,
										}));

										containers.push({
											id: buttonSection.Id || getRandomID(),
											type: "button",
											buttons: buttons,
											style: {
												backgroundColor: "transparent",
											},
										});
									}
								});
							}

							// Add image containers from ImageProperties
							if (step.ImageProperties && step.ImageProperties.length > 0) {
								step.ImageProperties.forEach((imageProperty: any) => {
									if (imageProperty.CustomImage && imageProperty.CustomImage.length > 0) {
										const images = imageProperty.CustomImage.map((image: any) => ({
											id: getRandomID(),
											url: image.Url || "",
											altText: image.AltText || "",
											backgroundColor: "transparent",
											objectFit: "contain",
										}));

										containers.push({
											id: imageProperty.Id || getRandomID(),
											type: "image",
											images: images,
											style: {
												backgroundColor: "transparent",
												height: IMG_CONTAINER_DEFAULT_HEIGHT,
											},
											hyperlink: undefined,
										});
									}
								});
							}

							// If no containers were created, add a default RTE container
							if (containers.length === 0) {
								containers.push({
									id: getRandomID(),
									type: "rte",
									placeholder: DEF_PLACEHOLDER_MSG,
									rteBoxValue: "",
									style: {
										backgroundColor: "transparent",
									},
								});
							}

							return {
								id: step.StepId || getRandomID(),
								containers: containers,
								currentStep: index + 1,
								stepName: step.StepTitle || `Step ${index + 1}`,
								stepDescription: step.StepDescription || "",
								stepType: step.StepType || "Tooltip",
								stepId: step.StepId || getRandomID(),
								stepTargetURL: step.StepTargetURL || "",
								xpath: {
									value: step.ElementPath || "",
									PossibleElementPath: step.PossibleElementPath || "",
									position: { x: 0, y: 0 },
								},
								canvas: {
									position: step.Canvas?.Position || "absolute",
									autoposition: step.AutoPosition || false,
									xaxis: step.Position?.XAxisOffset || "1px",
									yaxis: step.Position?.YAxisOffset || "1px",
									width: step.Canvas?.Width || "300px",
									padding: step.Canvas?.Padding || "2px",
									borderRadius: step.Canvas?.BorderRadius || "8px",
									borderSize: step.Canvas?.BorderSize || "0px",
									borderColor: step.Canvas?.BorderColor || "transparent",
									backgroundColor: step.Canvas?.BackgroundColor || "#ffffff",
								},
								hotspots: step.Hotspot || HOTSPOT_DEFAULT_VALUE,
								design: {
									gotoNext: {
										NextStep: step.Design?.GotoNext?.NextStep || "",
										ButtonId: step.Design?.GotoNext?.ButtonId || "",
										elementPath: step.Design?.GotoNext?.ElementPath || step.ElementPath || "",
										ElementPath: step.Design?.GotoNext?.ElementPath || step.ElementPath || "",
										ButtonName: step.Design?.GotoNext?.ButtonName || "",
									},
									element: {
										progress: step.Tooltip?.EnableProgress ? "Template1" : "",
										isDismiss: step.Modal?.DismissOption || false,
										progressSelectedOption: step.Tooltip?.ProgressTemplate || 1,
										progressColor: step.Modal?.ProgressColor || "var(--primarycolor)",
									},
								},
							};
						});

						// Update tooltip metadata
						state.toolTipGuideMetaData = newTooltipMetadata;

						// Update canvas settings from the first step
						if (newTooltipMetadata.length > 0) {
							const firstStep = newTooltipMetadata[0];
							state.tooltipPosition = firstStep.canvas.position;
							state.tooltipXaxis = firstStep.canvas.xaxis;
							state.tooltipYaxis = firstStep.canvas.yaxis;
							state.tooltipWidth = firstStep.canvas.width;
							state.tooltippadding = firstStep.canvas.padding;
							state.tooltipborderradius = firstStep.canvas.borderRadius;
							state.tooltipbordersize = firstStep.canvas.borderSize;
							state.tooltipBordercolor = firstStep.canvas.borderColor;
							state.tooltipBackgroundcolor = firstStep.canvas.backgroundColor;

							// Update progress and dismiss settings
							state.progress = firstStep.design.element.progress !== "";
							state.dismiss = firstStep.design.element.isDismiss;
							state.selectedOption = firstStep.design.element.progressSelectedOption;
							state.ProgressColor = firstStep.design.element.progressColor;
						}
					});
				},

				// Function to sync tooltip container data for AI guides
				syncAITooltipContainerData: () => {
					set((state) => {
						if (!state.createWithAI || !state.interactionData || (state.selectedTemplate !== "Tooltip" && state.selectedTemplate !== "Hotspot")) {
							console.log("AI tooltip sync skipped - conditions not met", {
								createWithAI: state.createWithAI,
								hasInteractionData: !!state.interactionData,
								selectedTemplate: state.selectedTemplate
							});
							return;
						}
// Handle both standalone AI tooltips and AI tour tooltip steps
						const isStandaloneTooltip = state.selectedTemplate === "Tooltip" || state.selectedTemplate === "Hotspot";
						const isAITourTooltipStep = state.selectedTemplate === "Tour" &&
							(state.selectedTemplateTour === "Tooltip" || state.selectedTemplateTour === "Hotspot");

						if (!isStandaloneTooltip && !isAITourTooltipStep) {
							console.log("AI tooltip sync skipped - not a tooltip template", {
								selectedTemplate: state.selectedTemplate,
								selectedTemplateTour: state.selectedTemplateTour
							});
							return;
						}

						const currentStepIndex = state.currentStep - 1;
						const currentTooltipMetadata = state.toolTipGuideMetaData[currentStepIndex];

						if (!currentTooltipMetadata) {
							console.log("AI tooltip sync skipped - no tooltip metadata for step", currentStepIndex);
							return;
						}

						console.log("Syncing AI tooltip data for step", currentStepIndex, {
							tooltipMetadata: currentTooltipMetadata,
							interactionData: state.interactionData,
							isStandaloneTooltip,
							isAITourTooltipStep
						});

						// Update interactionData with current tooltip metadata
						if (state.interactionData.GuideStep && state.interactionData.GuideStep[currentStepIndex]) {
							const currentGuideStep = state.interactionData.GuideStep[currentStepIndex];

							// Update TextFieldProperties from RTE containers
							const rteContainers = currentTooltipMetadata.containers.filter(c => c.type === "rte");
							if (rteContainers.length > 0) {
								currentGuideStep.TextFieldProperties = rteContainers.map((container: any) => ({
									Id: container.id,
									Text: container.rteBoxValue || "",
								}));
							}

							// Update ButtonSection from button containers
							const buttonContainers = currentTooltipMetadata.containers.filter(c => c.type === "button");
							if (buttonContainers.length > 0) {
								console.log("Processing button containers:", buttonContainers);
								currentGuideStep.ButtonSection = buttonContainers.map((container: any) => ({
									Id: container.id,
									CustomButtons: container.buttons?.map((button: any) => {
										const buttonData = {
											ButtonStyle: button.type || "primary",
											ButtonName: button.name,
											Alignment: button.position || "center",
											ButtonId: button.id,
											BackgroundColor: container.style?.backgroundColor || "transparent",
											ButtonAction: {
												Action: button.actions?.value || "",
												ActionValue: button.actions?.tab || "",
												TargetUrl: button.actions?.targetURL || "",
											},
											Padding: {
												Top: 0,
												Right: 0,
												Bottom: 0,
												Left: 0,
											},
											ButtonProperties: {
												Padding: 0,
												Width: 0,
												Font: 0,
												FontSize: 0,
												ButtonTextColor: button.style?.color || "#ffffff",
												ButtonBackgroundColor: button.style?.backgroundColor || "#5F9EA0",
												ButtonBorderColor: button.style?.borderColor || "#5F9EA0",
											},
										};
										console.log("Created button data:", buttonData);
										return buttonData;
									}) || [],
								}));
							}

							// Update ImageProperties from image containers
							const imageContainers = currentTooltipMetadata.containers.filter(c => c.type === "image");
							if (imageContainers.length > 0) {
								currentGuideStep.ImageProperties = imageContainers.map((container: any) => ({
									Id: container.id,
									CustomImage: container.images?.map((image: any) => ({
										Url: image.url,
										AltText: image.altText,
									})) || [],
								}));
							}

							// Update Canvas settings
							currentGuideStep.Canvas = {
								Position: currentTooltipMetadata.canvas.position,
								Width: currentTooltipMetadata.canvas.width,
								Padding: currentTooltipMetadata.canvas.padding,
								BorderRadius: currentTooltipMetadata.canvas.borderRadius,
								BorderSize: currentTooltipMetadata.canvas.borderSize,
								BorderColor: currentTooltipMetadata.canvas.borderColor,
								BackgroundColor: currentTooltipMetadata.canvas.backgroundColor,
							};

							// Update Position settings
							currentGuideStep.Position = {
								XAxisOffset: currentTooltipMetadata.canvas.xaxis,
								YAxisOffset: currentTooltipMetadata.canvas.yaxis,
							};

							// Update AutoPosition
							currentGuideStep.AutoPosition = currentTooltipMetadata.canvas.autoposition;

							// Update ElementPath
							currentGuideStep.ElementPath = currentTooltipMetadata.xpath.value;
							currentGuideStep.PossibleElementPath = currentTooltipMetadata.xpath.PossibleElementPath;

							// Update Design settings - ensure ElementPath is set for button click
							const gotoNextConfig = { ...currentTooltipMetadata.design.gotoNext };

							// For button click functionality, ElementPath should point to tooltip's target element
							if (gotoNextConfig.NextStep === "button" && gotoNextConfig.ButtonId && currentTooltipMetadata.xpath?.value) {
								gotoNextConfig.ElementPath = currentTooltipMetadata.xpath.value;
							}

							currentGuideStep.Design = {
								GotoNext: gotoNextConfig,
							};

							// Update Tooltip settings
							currentGuideStep.Tooltip = {
								EnableProgress: currentGuideStep?.Tooltip?.EnableProgress!= undefined ? currentGuideStep.Tooltip.EnableProgress : currentTooltipMetadata.design.element.progress !== "",
								ProgressTemplate: currentTooltipMetadata.design.element.progressSelectedOption,
								InteractWithPage: state.pageinteraction,
							};

							// Update Modal settings
							currentGuideStep.Modal = {
								DismissOption: currentTooltipMetadata.design.element.isDismiss,
								ProgressColor: currentTooltipMetadata.design.element.progressColor,
							};

							// Update Hotspot settings
							currentGuideStep.Hotspot = currentTooltipMetadata.hotspots;
						}
						// CRITICAL FIX: Also update savedGuideData for AI tooltips to ensure preview mode works
						if (state.savedGuideData?.GuideStep?.[currentStepIndex]) {
							const savedGuideStep = state.savedGuideData.GuideStep[currentStepIndex];

							// Update Design.GotoNext in savedGuideData - this is what preview components use
							const gotoNextConfig = { ...currentTooltipMetadata.design.gotoNext };

							// For button click functionality, ElementPath should point to tooltip's target element
							if (gotoNextConfig.NextStep === "button" && gotoNextConfig.ButtonId && currentTooltipMetadata.xpath?.value) {
								gotoNextConfig.ElementPath = currentTooltipMetadata.xpath.value;
							}

							// Ensure Design object exists in savedGuideData
							if (!savedGuideStep.Design) {
								savedGuideStep.Design = {};
							}

							savedGuideStep.Design.GotoNext = gotoNextConfig;

							// Also update other essential data in savedGuideData
							savedGuideStep.ElementPath = currentTooltipMetadata.xpath.value;
							savedGuideStep.PossibleElementPath = currentTooltipMetadata.xpath.PossibleElementPath;

							// Update Canvas and Position data
							savedGuideStep.Canvas = {
								Position: currentTooltipMetadata.canvas.position,
								Width: currentTooltipMetadata.canvas.width,
								Padding: currentTooltipMetadata.canvas.padding,
								BorderRadius: currentTooltipMetadata.canvas.borderRadius,
								BorderSize: currentTooltipMetadata.canvas.borderSize,
								BorderColor: currentTooltipMetadata.canvas.borderColor,
								BackgroundColor: currentTooltipMetadata.canvas.backgroundColor,
							};

							savedGuideStep.Position = {
								XAxisOffset: currentTooltipMetadata.canvas.xaxis,
								YAxisOffset: currentTooltipMetadata.canvas.yaxis,
							};

							// Update TextFieldProperties from RTE containers
							const rteContainers = currentTooltipMetadata.containers.filter(c => c.type === "rte");
							if (rteContainers.length > 0) {
								savedGuideStep.TextFieldProperties = rteContainers.map((container: any) => ({
									Id: container.id,
									Text: container.rteBoxValue || "",
								}));
							}

							// Update ButtonSection from button containers
							const buttonContainers = currentTooltipMetadata.containers.filter(c => c.type === "button");
							if (buttonContainers.length > 0) {
								savedGuideStep.ButtonSection = buttonContainers.map((container: any) => ({
									Id: container.id,
									CustomButtons: container.buttons?.map((button: any) => ({
										ButtonStyle: button.type || "primary",
										ButtonName: button.name,
										Alignment: button.position || "center",
										ButtonId: button.id,
										BackgroundColor: container.style?.backgroundColor || "transparent",
										ButtonAction: {
											Action: button.actions?.value || "",
											ActionValue: button.actions?.tab || "",
											TargetUrl: button.actions?.targetURL || "",
										},
										Padding: {
											Top: 0,
											Right: 0,
											Bottom: 0,
											Left: 0,
										},
										ButtonProperties: {
											Padding: 0,
											Width: 0,
											Font: 0,
											FontSize: 0,
											ButtonTextColor: button.style?.color || "#ffffff",
											ButtonBackgroundColor: button.style?.backgroundColor || "#5F9EA0",
											ButtonBorderColor: button.style?.borderColor || "#5F9EA0",
										},
									})) || [],
								}));
							}

							console.log("✅ FIXED: Updated savedGuideData.Design.GotoNext for AI tooltip step", currentStepIndex, {
								Design: savedGuideStep.Design,
								gotoNext: gotoNextConfig,
								elementPath: savedGuideStep.ElementPath
							});
						}

						// For AI tour tooltip steps, also ensure updatedGuideData is synchronized
						if (isAITourTooltipStep && state.updatedGuideData?.GuideStep?.[currentStepIndex]) {
							const updatedGuideStep = state.updatedGuideData.GuideStep[currentStepIndex];

							// Update Design.GotoNext in updatedGuideData
							const gotoNextConfig = { ...currentTooltipMetadata.design.gotoNext };

							// For button click functionality, ElementPath should point to tooltip's target element
							if (gotoNextConfig.NextStep === "button" && gotoNextConfig.ButtonId && currentTooltipMetadata.xpath?.value) {
								gotoNextConfig.ElementPath = currentTooltipMetadata.xpath.value;
							}

							// Ensure Design object exists in updatedGuideData
							if (!updatedGuideStep.Design) {
								updatedGuideStep.Design = {};
							}

							updatedGuideStep.Design.GotoNext = gotoNextConfig;

							console.log("✅ FIXED: Updated updatedGuideData.Design.GotoNext for AI tour tooltip step", currentStepIndex, {
								Design: updatedGuideStep.Design,
								gotoNext: gotoNextConfig
							});
						}
					});
				},

				// Function to restore tooltip element click state from interactionData back to toolTipGuideMetaData
				restoreTooltipElementClickState: () => {
					set((state) => {
						if (!state.createWithAI || !state.interactionData ||
							(state.selectedTemplate !== "Tooltip" &&
							 !(state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Tooltip"))) {
							console.log("Tooltip element click restoration skipped - conditions not met", {
								createWithAI: state.createWithAI,
								hasInteractionData: !!state.interactionData,
								selectedTemplate: state.selectedTemplate,
								selectedTemplateTour: state.selectedTemplateTour
							});
							return;
						}

						const currentStepIndex = state.currentStep - 1;
						const currentGuideStep = state.interactionData.GuideStep?.[currentStepIndex];
						const currentTooltipMetadata = state.toolTipGuideMetaData[currentStepIndex];

						if (!currentGuideStep || !currentTooltipMetadata) {
							console.log("Tooltip element click restoration skipped - missing data for step", currentStepIndex);
							return;
						}

						console.log("Restoring tooltip element click state from interactionData for step", currentStepIndex, {
							gotoNext: currentGuideStep.Design?.GotoNext
						});

						// Restore Design.GotoNext from interactionData back to toolTipGuideMetaData
						if (currentGuideStep.Design?.GotoNext) {
							// Ensure design object exists
							if (!currentTooltipMetadata.design) {
								currentTooltipMetadata.design = {
									gotoNext: {},
									element: {
										progress: "",
										isDismiss: false
									}
								};
							}

							// Restore the gotoNext object
							currentTooltipMetadata.design.gotoNext = {
								NextStep: currentGuideStep.Design.GotoNext.NextStep || "",
								ButtonId: currentGuideStep.Design.GotoNext.ButtonId || "",
								ElementPath: currentGuideStep.Design.GotoNext.ElementPath || "",
								ButtonName: currentGuideStep.Design.GotoNext.ButtonName || "",
								Id: currentGuideStep.Design.GotoNext.Id || currentGuideStep.Design.GotoNext.ButtonId || ""
							};

							// Update UI state based on restored data
							const hasButtonClick = currentGuideStep.Design.GotoNext.ButtonId &&
								currentGuideStep.Design.GotoNext.ButtonId.trim() !== "";

							if (hasButtonClick) {
								state.elementClick = "button";
								state.dropdownValue = currentGuideStep.Design.GotoNext.ButtonId;
								state.elementButtonName = currentGuideStep.Design.GotoNext.ButtonName || "";
								state.buttonClick = true;
								state.elementbuttonClick = true;
								// CRITICAL FIX: Also update btnidss to ensure dropdown binding works correctly
								state.btnidss = currentGuideStep.Design.GotoNext.ButtonId;
							} else {
								state.elementClick = "element";
								state.dropdownValue = "";
								state.elementButtonName = "";
								state.buttonClick = false;
								state.elementbuttonClick = false;
								state.btnidss = "";
							}

							console.log("Successfully restored tooltip element click state:", {
								gotoNext: currentTooltipMetadata.design.gotoNext,
								elementClick: state.elementClick,
								buttonClick: state.buttonClick,
								dropdownValue: state.dropdownValue,
								btnidss: state.btnidss,
								elementButtonName: state.elementButtonName
							});
						}
					});
				},

				// Function to restore manual tooltip element click state from savedGuideData
				restoreManualTooltipElementClickState: () => {
					set((state) => {
						// Only for manual (non-AI) tooltips
						if (state.createWithAI ||
							(state.selectedTemplate !== "Tooltip" &&
							 !(state.selectedTemplate === "Tour" && state.selectedTemplateTour === "Tooltip"))) {
							console.log("Manual tooltip element click restoration skipped - conditions not met", {
								createWithAI: state.createWithAI,
								selectedTemplate: state.selectedTemplate,
								selectedTemplateTour: state.selectedTemplateTour
							});
							return;
						}

						const currentStepIndex = state.currentStep - 1;
						const currentTooltipMetadata = state.toolTipGuideMetaData[currentStepIndex];
						const savedGuideStep = state.savedGuideData?.GuideStep?.[currentStepIndex];

						if (!currentTooltipMetadata || !savedGuideStep) {
							console.log("Manual tooltip element click restoration skipped - missing data for step", currentStepIndex);
							return;
						}

						console.log("Restoring manual tooltip element click state from savedGuideData for step", currentStepIndex, {
							gotoNext: savedGuideStep.Design?.GotoNext
						});

						// Restore Design.GotoNext from savedGuideData to toolTipGuideMetaData
						if (savedGuideStep.Design?.GotoNext) {
							// Ensure design object exists
							if (!currentTooltipMetadata.design) {
								currentTooltipMetadata.design = {
									gotoNext: {},
									element: {
										progress: "",
										isDismiss: false
									}
								};
							}

							// Restore the gotoNext object
							currentTooltipMetadata.design.gotoNext = {
								NextStep: savedGuideStep.Design.GotoNext.NextStep || "",
								ButtonId: savedGuideStep.Design.GotoNext.ButtonId || "",
								ElementPath: savedGuideStep.Design.GotoNext.ElementPath || "",
								ButtonName: savedGuideStep.Design.GotoNext.ButtonName || "",
								Id: savedGuideStep.Design.GotoNext.Id || savedGuideStep.Design.GotoNext.ButtonId || ""
							};

							// Update UI state based on restored data
							const hasButtonClick = savedGuideStep.Design.GotoNext.ButtonId &&
								savedGuideStep.Design.GotoNext.ButtonId.trim() !== "";

							if (hasButtonClick) {
								state.elementClick = "button";
								state.dropdownValue = savedGuideStep.Design.GotoNext.ButtonId;
								state.elementButtonName = savedGuideStep.Design.GotoNext.ButtonName || "";
								state.buttonClick = true;
								state.elementbuttonClick = true;
								// CRITICAL FIX: Also update btnidss to ensure dropdown binding works correctly
								state.btnidss = savedGuideStep.Design.GotoNext.ButtonId;
							} else {
								state.elementClick = "element";
								state.dropdownValue = "";
								state.elementButtonName = "";
								state.buttonClick = false;
								state.elementbuttonClick = false;
								state.btnidss = "";
							}

							console.log("Successfully restored manual tooltip element click state:", {
								gotoNext: currentTooltipMetadata.design.gotoNext,
								elementClick: state.elementClick,
								buttonClick: state.buttonClick,
								dropdownValue: state.dropdownValue,
								btnidss: state.btnidss,
								elementButtonName: state.elementButtonName
							});
						}
					});
				},

				// Function to synchronize AI announcement data with announcementGuideMetaData
				syncAIAnnouncementDataForPreview: (preserveGlobalState = true) => {
					set((state) => {
						// Only sync if we have AI data and selected template is announcement or tour with announcement steps
						if (!state.createWithAI || !state.interactionData) {
							return;
						}

						const aiGuideSteps = state.interactionData?.GuideStep || [];
						if (aiGuideSteps.length === 0) {
							return;
						}

						console.log("Syncing AI announcement data for preview", {
							aiGuideSteps: aiGuideSteps,
							currentTemplate: state.selectedTemplate,
							currentTemplateTour: state.selectedTemplateTour
						});

						// Create announcement metadata from AI data
						const newAnnouncementMetadata = aiGuideSteps.map((step: any, index: number) => {
							const existingMetadata = state.announcementGuideMetaData[index];
							const existingContainers = existingMetadata?.containers || [];
							const containers = [];

							// Add RTE containers from TextFieldProperties
							if (step.TextFieldProperties && step.TextFieldProperties.length > 0) {
								containers.push(
									...step.TextFieldProperties.map((textField: any) => ({
										id: textField.Id,
										type: "rte",
										placeholder: "Start typing here...",
										rteBoxValue: textField.Text || "",
										style: {
											backgroundColor: "transparent",
										},
									}))
								);
							}

							// Add button containers from ButtonSection
							if (step.ButtonSection && step.ButtonSection.length > 0) {
								containers.push(
									...step.ButtonSection.map((buttonSection: any) => ({
										id: buttonSection.Id,
										type: "button",
										buttons: buttonSection.CustomButtons?.map((button: any) => ({
											id: button.ButtonId,
											name: button.ButtonName,
											type: button.ButtonStyle,
											position: button.Alignment,
											style: {
												backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || button.ButtonAction?.BackgroundColor || "#5F9EA0",
												color: button.ButtonProperties?.ButtonTextColor || button.ButtonAction?.ButtonTextColor || "#ffffff",
												borderColor: button.ButtonProperties?.ButtonBorderColor || button.ButtonAction?.ButtonBorderColor || "#5F9EA0",
											},
											actions: {
												value: button.ButtonAction?.Action || "close",
												targetURL: button.ButtonAction?.TargetUrl || "",
												tab: button.ButtonAction?.ActionValue || "same-tab",
												interaction: null,
											},
										})) || [],
										style: {
											backgroundColor: buttonSection?.BackgroundColor || buttonSection.CustomButtons?.[0]?.ButtonAction?.BackgroundColor || "#f0f0f0",
										},
									}))
								);
							}
							const aiButtonContainerIds = new Set(step.ButtonSection?.map((bs: any) => bs.Id) || []);
							const manualButtonContainers = existingContainers.filter((container: any) =>
								container.type === "button" && !aiButtonContainerIds.has(container.id)
							);

							containers.push(...manualButtonContainers);
							// Add image containers from ImageProperties
							if (step.ImageProperties && step.ImageProperties.length > 0) {
								containers.push(
									...step.ImageProperties.map((imageProperty: any) => ({
										id: imageProperty.Id,
										type: "image",
										images: imageProperty.CustomImage?.map((image: any) => ({
											url: image.Url,
											altText: image.AltText,
										})) || [],
									}))
								);
							}
							const aiImageContainerIds = new Set(step.ImageProperties?.map((ip: any) => ip.Id) || []);
							const manualImageContainers = existingContainers.filter((container: any) =>
								container.type === "image" && !aiImageContainerIds.has(container.id)
							);
							containers.push(...manualImageContainers);

							// Preserve any other manually added containers (not RTE, button, or image)
							const manualOtherContainers = existingContainers.filter((container: any) =>
								!["rte", "button", "image"].includes(container.type)
							);
							containers.push(...manualOtherContainers);
							// If no containers exist, add default RTE container
							if (containers.length === 0) {
								containers.push({
									id: crypto.randomUUID(),
									type: "rte",
									placeholder: "Start typing here...",
									rteBoxValue: "",
									style: {
										backgroundColor: "transparent",
									},
								});
							}

							return {
								containers: containers,
								stepName: step.StepTitle || `Step ${index + 1}`,
								currentStep: index + 1,
								stepId: step.StepId,
								stepDescription: step.Description || "",
								stepType: step.StepType || "Announcement",
								stepTargetURL: step.StepTargetURL || "",
								xpath: {
									value: step.ElementPath || "",
									PossibleElementPath: step.PossibleElementPath || "",
									position: { x: 0, y: 0 },
								},
								id: step.StepId || crypto.randomUUID(),
								hotspots: step.Hotspot || [],
								canvas: {
									position: step.Canvas?.Position || "center-center",
									width: step.Canvas?.Width || 500,
									padding: step.Canvas?.Padding || "12",
									borderRadius: step.Canvas?.Radius || 8,
									borderSize: step.Canvas?.BorderSize || 0,
									borderColor: step.Canvas?.BorderColor || "#000000",
									backgroundColor: step.Canvas?.BackgroundColor || "#ffffff",
									xaxis: step.Position?.XAxisOffset || "4px",
									yaxis: step.Position?.YAxisOffset || "4px",
								},
								design: {
									gotoNext: step.Design?.GotoNext || {},
									element: {
										// Default to enabled progress for AI announcements if not explicitly set
										progress: step.Tooltip?.EnableProgress !== undefined ?
											(step.Tooltip.EnableProgress ? "Template1" : "") :
											"Template1", // Default enabled for new AI announcements
										progressSelectedOption: step.Tooltip?.ProgressTemplate || 1,
										progressColor: step.Modal?.ProgressColor || "var(--primarycolor)",
										isDismiss: step.Modal?.DismissOption || false,
									},
								},
							};
						});

						// Update announcementGuideMetaData with the new data
						state.announcementGuideMetaData = newAnnouncementMetadata;

						// Only update global progress state during initial setup, not during mode transitions
						// This prevents overriding user's explicit changes to progress settings
						if (newAnnouncementMetadata.length > 0) {
							const firstStepProgress = newAnnouncementMetadata[0].design?.element?.progress;
							const firstStepProgressTemplate = newAnnouncementMetadata[0].design?.element?.progressSelectedOption;

							if (!preserveGlobalState) {
								// Set progress state based on AI data during initial setup
								if (firstStepProgress !== undefined) {
									state.progress = firstStepProgress !== "";
								}

								// Set progress template option
								if (firstStepProgressTemplate !== undefined) {
									state.selectedOption = firstStepProgressTemplate;
								}

								console.log("Updated global progress state from AI data (initial setup):", {
									progress: state.progress,
									selectedOption: state.selectedOption
								});
							} else {
								console.log("AI announcement data sync - preserving global progress state:", {
									globalProgress: state.progress,
									globalSelectedOption: state.selectedOption,
									metadataProgress: firstStepProgress !== "",
									metadataTemplate: firstStepProgressTemplate
								});
							}
						}

						console.log("Updated announcementGuideMetaData from AI data:", state.announcementGuideMetaData);

						// Also sync announcementJson with the updated metadata
						const updatedGuideSteps = newAnnouncementMetadata.map((metadata: any, index: number) => {
							return {
								stepName: index + 1, // Use step number as stepName
								StepId: metadata.id,
								StepTitle: metadata.stepName,
								StepCount: index + 1,
								StepType: metadata.stepType || "Announcement",
								Description: metadata.stepDescription || "",
								Canvas: {
									Position: metadata.canvas?.position || "center-center",
									Width: metadata.canvas?.width || 500,
									Padding: metadata.canvas?.padding || "12",
									Radius: metadata.canvas?.borderRadius || 8,
									BorderSize: metadata.canvas?.borderSize || 0,
									BorderColor: metadata.canvas?.borderColor || "#000000",
									BackgroundColor: metadata.canvas?.backgroundColor || "#ffffff",
								},
								TextFieldProperties: metadata.containers
									?.filter((c: any) => c.type === "rte")
									?.map((container: any) => ({
										Id: container.id,
										Text: container.rteBoxValue || "",
									})) || [],
								ButtonSection: metadata.containers
									?.filter((c: any) => c.type === "button")
									?.map((container: any) => ({
										Id: container.id,
										CustomButtons: container.buttons?.map((button: any) => ({
											ButtonStyle: button.type || "primary",
											ButtonName: button.name,
											Alignment: button.position || "center",
											ButtonId: button.id,
											BackgroundColor: container.style?.backgroundColor || "transparent",
											ButtonAction: {
												Action: button.actions?.value || button.action?.Action || "",
												ActionValue: button.actions?.tab || button.action?.ActionValue || "",
												TargetUrl: button.actions?.targetURL || button.action?.TargetUrl || "",
											},
											Padding: {
												Top: 0,
												Right: 0,
												Bottom: 0,
												Left: 0,
											},
											ButtonProperties: {
												Padding: 0,
												Width: 0,
												Font: 0,
												FontSize: 0,
												ButtonTextColor: button.style?.color || "#ffffff",
												ButtonBackgroundColor: button.style?.backgroundColor || "#5F9EA0",
												ButtonBorderColor: button.style?.borderColor || "#5F9EA0",
											},
										})) || [],
									})) || [],
								ImageProperties: metadata.containers
									?.filter((c: any) => c.type === "image")
									?.map((container: any) => ({
										Id: container.id,
										CustomImage: container.images?.map((image: any) => ({
											Url: image.url,
											AltText: image.altText,
										})) || [],
									})) || [],
								Overlay: state.overlayEnabled,
								Tooltip: {
									EnableProgress: metadata.design?.element?.progress !== "",
									ProgressTemplate: metadata.design?.element?.progressSelectedOption?.toString() || "",
									InteractWithPage: state.pageinteraction,
								},
								Modal: {
									DismissOption: metadata.design?.element?.isDismiss || false,
									ProgressColor: metadata.design?.element?.progressColor || "#5F9EA0",
								},
								ElementPath: metadata.xpath?.value || "",
								PossibleElementPath: metadata.xpath?.PossibleElementPath || "",
								Hotspot: metadata.hotspots || [],
							};
						});

						// Update announcementJson with the new GuideStep data
						state.announcementJson = {
							...state.announcementJson,
							GuideStep: updatedGuideSteps,
						};

						console.log("Updated announcementJson from announcementGuideMetaData:", state.announcementJson);
					});
				},

				// Function to sync announcement container data for AI guides
				syncAIAnnouncementContainerData: () => {
					set((state) => {
						if (!state.createWithAI || !state.interactionData) {
							console.log("AI announcement sync skipped - conditions not met", {
								createWithAI: state.createWithAI,
								hasInteractionData: !!state.interactionData,
							});
							return;
						}

						const currentStepIndex = state.currentStep - 1;
						const currentAnnouncementMetadata = state.announcementGuideMetaData[currentStepIndex];

						if (!currentAnnouncementMetadata) {
							console.log("AI announcement sync skipped - no announcement metadata for step", currentStepIndex);
							return;
						}

						console.log("Syncing AI announcement data for step", currentStepIndex, {
							announcementMetadata: currentAnnouncementMetadata,
							interactionData: state.interactionData
						});

						// Update interactionData with current announcement metadata
						if (state.interactionData.GuideStep && state.interactionData.GuideStep[currentStepIndex]) {
							const currentGuideStep = state.interactionData.GuideStep[currentStepIndex];

							// Update TextFieldProperties from RTE containers
							const rteContainers = currentAnnouncementMetadata.containers.filter(c => c.type === "rte");
							if (rteContainers.length > 0) {
								const newTextFieldProperties = rteContainers.map((container: any) => ({
									Id: container.id,
									Text: container.rteBoxValue || "",
								}));

								currentGuideStep.TextFieldProperties = newTextFieldProperties;
								console.log("Updated TextFieldProperties in interactionData:", newTextFieldProperties);
							}

							// Update ButtonSection from button containers
							const buttonContainers = currentAnnouncementMetadata.containers.filter(c => c.type === "button");
							if (buttonContainers.length > 0) {
								console.log("Processing AI announcement button containers:", buttonContainers);
								currentGuideStep.ButtonSection = buttonContainers.map((container: any) => ({
									Id: container.id,
									CustomButtons: container.buttons?.map((button: any) => {
										const buttonData = {
											ButtonStyle: button.type || "primary",
											ButtonName: button.name,
											Alignment: button.position || "center",
											ButtonId: button.id,
											BackgroundColor: container.style?.backgroundColor || "transparent",
											ButtonAction: {
												Action: button.actions?.value || button.action?.Action || "",
												ActionValue: button.actions?.tab || button.action?.ActionValue || "",
												TargetUrl: button.actions?.targetURL || button.action?.TargetUrl || "",
											},
											Padding: {
												Top: 0,
												Right: 0,
												Bottom: 0,
												Left: 0,
											},
											ButtonProperties: {
												Padding: 0,
												Width: 0,
												Font: 0,
												FontSize: 0,
												ButtonTextColor: button.style?.color || "#ffffff",
												ButtonBackgroundColor: button.style?.backgroundColor || "#5F9EA0",
												ButtonBorderColor: button.style?.borderColor || "#5F9EA0",
											},
										};
										console.log("Created AI announcement button data:", buttonData);
										return buttonData;
									}) || [],
								}));
							}

							// Update ImageProperties from image containers
							const imageContainers = currentAnnouncementMetadata.containers.filter(c => c.type === "image");
							if (imageContainers.length > 0) {
								currentGuideStep.ImageProperties = imageContainers.map((container: any) => ({
									Id: container.id,
									CustomImage: container.images?.map((image: any) => ({
										Url: image.url,
										AltText: image.altText,
									})) || [],
								}));
							}

							// Update Canvas settings
							currentGuideStep.Canvas = {
								Position: currentAnnouncementMetadata.canvas.position,
								Width: currentAnnouncementMetadata.canvas.width,
								Padding: currentAnnouncementMetadata.canvas.padding,
								Radius: currentAnnouncementMetadata.canvas.borderRadius,
								BorderSize: currentAnnouncementMetadata.canvas.borderSize,
								BorderColor: currentAnnouncementMetadata.canvas.borderColor,
								BackgroundColor: currentAnnouncementMetadata.canvas.backgroundColor,
							};

							// Update Position settings
							currentGuideStep.Position = {
								XAxisOffset: currentAnnouncementMetadata.canvas.xaxis,
								YAxisOffset: currentAnnouncementMetadata.canvas.yaxis,
							};

							// Update ElementPath
							currentGuideStep.ElementPath = currentAnnouncementMetadata.xpath.value;
							currentGuideStep.PossibleElementPath = currentAnnouncementMetadata.xpath.PossibleElementPath;

							// Update Design settings
							currentGuideStep.Design = {
								GotoNext: currentAnnouncementMetadata.design.gotoNext,
							};

							// Update Tooltip settings
							currentGuideStep.Tooltip = {
								EnableProgress: currentAnnouncementMetadata.design.element.progress !== "",
								ProgressTemplate: currentAnnouncementMetadata.design.element.progressSelectedOption,
								InteractWithPage: state.pageinteraction,
							};

							// Update Modal settings
							currentGuideStep.Modal = {
								DismissOption: currentAnnouncementMetadata.design.element.isDismiss,
								ProgressColor: currentAnnouncementMetadata.design.element.progressColor,
							};

							// Update Hotspot settings
							currentGuideStep.Hotspot = currentAnnouncementMetadata.hotspots;

							console.log("Updated interactionData GuideStep for announcement:", currentGuideStep);
						}

						// Update savedGuideData for ALL steps to ensure consistency with global progress state
						// Progress bar state should be global across all announcement steps
						if (state.savedGuideData?.GuideStep) {
							state.savedGuideData.GuideStep.forEach((step, stepIndex) => {
								const metadata = state.announcementGuideMetaData[stepIndex];
								if (metadata?.design?.element) {
									const progressEnabled = metadata.design.element.progress !== "";
									const progressTemplate = metadata.design.element.progressSelectedOption;

									// Update savedGuideData for ALL steps to ensure preview consistency
									state.savedGuideData.GuideStep[stepIndex] = {
										...state.savedGuideData.GuideStep[stepIndex],
										Overlay: state.overlayEnabled,
										Tooltip: {
											...state.savedGuideData.GuideStep[stepIndex].Tooltip,
											EnableProgress: progressEnabled,
											ProgressTemplate: progressTemplate || 1,
											InteractWithPage: state.pageinteraction,
										},
										Modal: {
											...state.savedGuideData.GuideStep[stepIndex].Modal,
											ProgressColor: metadata.design.element.progressColor || "var(--primarycolor)",
											DismissOption: metadata.design.element.isDismiss || false,
										},
									};
								}
							});

							console.log("🔄 syncAIAnnouncementContainerData: Updated savedGuideData for ALL steps", {
								globalProgress: state.progress,
								globalSelectedOption: state.selectedOption,
								allStepsProgress: state.savedGuideData.GuideStep.map(step => step?.Tooltip?.EnableProgress),
								totalStepsUpdated: state.savedGuideData.GuideStep.length
							});
						}
					});
				},

				// Function to sync announcementJson with announcementGuideMetaData for AI announcements
				syncAnnouncementJsonWithMetaData: () => {
					set((state) => {
						if (!state.createWithAI || !state.announcementGuideMetaData || state.announcementGuideMetaData.length === 0) {
							return;
						}

						console.log("Syncing announcementJson with announcementGuideMetaData", {
							announcementGuideMetaData: state.announcementGuideMetaData,
							currentAnnouncementJson: state.announcementJson
						});

						// Create or update announcementJson.GuideStep from announcementGuideMetaData
						const updatedGuideSteps = state.announcementGuideMetaData.map((metadata: any, index: number) => {
							return {
								stepName: index + 1, // Use step number as stepName
								StepId: metadata.id,
								StepTitle: metadata.stepName,
								StepCount: index + 1,
								StepType: metadata.stepType || "Announcement",
								Description: metadata.stepDescription || "",
								Canvas: {
									Position: metadata.canvas?.position || "center-center",
									Width: metadata.canvas?.width || 500,
									Padding: metadata.canvas?.padding || "12",
									Radius: metadata.canvas?.borderRadius || 8,
									BorderSize: metadata.canvas?.borderSize || 0,
									BorderColor: metadata.canvas?.borderColor || "#000000",
									BackgroundColor: metadata.canvas?.backgroundColor || "#ffffff",
								},
								TextFieldProperties: metadata.containers
									?.filter((c: any) => c.type === "rte")
									?.map((container: any) => ({
										Id: container.id,
										Text: container.rteBoxValue || "",
									})) || [],
								ButtonSection: metadata.containers
									?.filter((c: any) => c.type === "button")
									?.map((container: any) => ({
										Id: container.id,
										CustomButtons: container.buttons?.map((button: any) => ({
											ButtonStyle: button.type || "primary",
											ButtonName: button.name,
											Alignment: button.position || "center",
											ButtonId: button.id,
											BackgroundColor: container.style?.backgroundColor || "transparent",
											ButtonAction: {
												Action: button.actions?.value || button.action?.Action || "",
												ActionValue: button.actions?.tab || button.action?.ActionValue || "",
												TargetUrl: button.actions?.targetURL || button.action?.TargetUrl || "",
											},
											Padding: {
												Top: 0,
												Right: 0,
												Bottom: 0,
												Left: 0,
											},
											ButtonProperties: {
												Padding: 0,
												Width: 0,
												Font: 0,
												FontSize: 0,
												ButtonTextColor: button.style?.color || "#ffffff",
												ButtonBackgroundColor: button.style?.backgroundColor || "#5F9EA0",
												ButtonBorderColor: button.style?.borderColor || "#5F9EA0",
											},
										})) || [],
									})) || [],
								ImageProperties: metadata.containers
									?.filter((c: any) => c.type === "image")
									?.map((container: any) => ({
										Id: container.id,
										CustomImage: container.images?.map((image: any) => ({
											Url: image.url,
											AltText: image.altText,
										})) || [],
									})) || [],
								Overlay: state.overlayEnabled,
								Tooltip: {
									EnableProgress: metadata.design?.element?.progress !== "",
									ProgressTemplate: metadata.design?.element?.progressSelectedOption?.toString() || "",
									InteractWithPage: state.pageinteraction,
								},
								Modal: {
									DismissOption: metadata.design?.element?.isDismiss || false,
									ProgressColor: metadata.design?.element?.progressColor || "#5F9EA0",
								},
								ElementPath: metadata.xpath?.value || "",
								PossibleElementPath: metadata.xpath?.PossibleElementPath || "",
								Hotspot: metadata.hotspots || [],
							};
						});

						// Update announcementJson with the new GuideStep data
						state.announcementJson = {
							...state.announcementJson,
							GuideStep: updatedGuideSteps,
						};

						console.log("Updated announcementJson from announcementGuideMetaData:", state.announcementJson);

						// Also update savedGuideData to ensure preview shows correct progress bar state
						if (state.savedGuideData?.GuideStep) {
							state.savedGuideData.GuideStep = state.savedGuideData.GuideStep.map((step: any, index: number) => {
								const metadata = state.announcementGuideMetaData[index];
								if (metadata?.design?.element) {
									const newEnableProgress = metadata.design.element.progress !== "";

									return {
										...step,
										Overlay: state.overlayEnabled,
										Tooltip: {
											...step.Tooltip,
											EnableProgress: newEnableProgress,
											ProgressTemplate: metadata.design.element.progressSelectedOption || 1,
											InteractWithPage: state.pageinteraction,
										},
										Modal: {
											...step.Modal,
											ProgressColor: metadata.design.element.progressColor || "var(--primarycolor)",
											DismissOption: metadata.design.element.isDismiss || false,
										},
									};
								}
								return step;
							});

							console.log("🔄 syncAnnouncementJsonWithMetaData: Updated savedGuideData from metadata", {
								savedGuideDataProgress: state.savedGuideData.GuideStep.map(step => step?.Tooltip?.EnableProgress)
							});
						}
					});
				},

				// Function to synchronize global overlay state across all announcement steps
				syncGlobalOverlayStateForAnnouncements: () => {
					set((state) => {
						if (state.selectedTemplate !== "Announcement") {
							return;
						}

						console.log("🔄 Syncing global overlay state for announcements", {
							globalOverlayEnabled: state.overlayEnabled,
							globalPageInteraction: state.pageinteraction,
							totalSteps: state.savedGuideData?.GuideStep?.length || 0
						});

						// Apply global overlay state to savedGuideData for all steps
						if (state.savedGuideData?.GuideStep) {
							state.savedGuideData.GuideStep.forEach((step, stepIndex) => {
								step.Overlay = state.overlayEnabled;
								if (step.Tooltip) {
									step.Tooltip.InteractWithPage = state.pageinteraction;
								}
							});
						}

						// Apply global overlay state to announcementJson for all steps
						if (state.announcementJson?.GuideStep) {
							state.announcementJson.GuideStep.forEach((step, stepIndex) => {
								step.Overlay = state.overlayEnabled;
								if (step.Tooltip) {
									step.Tooltip.InteractWithPage = state.pageinteraction;
								}
							});
						}

						// For AI-created announcements, also sync interactionData
						if (state.createWithAI && state.interactionData?.GuideStep) {
							state.interactionData.GuideStep.forEach((step, stepIndex) => {
								step.Overlay = state.overlayEnabled;
								if (step.Tooltip) {
									step.Tooltip.InteractWithPage = state.pageinteraction;
								}
							});
						}

						console.log("✅ Global overlay state synchronized for all announcement steps");
					});
				},

				// Function to sync AI announcement canvas settings after canvas update
				syncAIAnnouncementCanvasSettings: (canvasData: any) => {
					set((state) => {
						if (!state.createWithAI || !(state.selectedTemplate === "Announcement" || state.selectedTemplateTour === "Announcement")) {
							return;
						}

						const currentStepIndex = state.currentStep - 1;

						// Update announcementGuideMetaData with current canvas settings
						if (state.announcementGuideMetaData[currentStepIndex]) {
							state.announcementGuideMetaData[currentStepIndex].canvas = {
								position: canvasData.Position || "center-center",
								width: canvasData.Width || 500,
								padding: canvasData.Padding || "12",
								borderRadius: canvasData.Radius || 8,
								borderSize: canvasData.BorderSize || 0,
								borderColor: canvasData.BorderColor || "#000000",
								backgroundColor: canvasData.BackgroundColor || "#ffffff",
								xaxis: state.announcementGuideMetaData[currentStepIndex].canvas?.xaxis || "4px",
								yaxis: state.announcementGuideMetaData[currentStepIndex].canvas?.yaxis || "4px",
							};

							console.log("Updated AI announcement canvas metadata for step", currentStepIndex, {
								canvasData: canvasData,
								announcementMetadata: state.announcementGuideMetaData[currentStepIndex].canvas
							});
						}

						// Update interactionData with current canvas settings
						if (state.interactionData?.GuideStep?.[currentStepIndex]) {
							const currentGuideStep = state.interactionData.GuideStep[currentStepIndex];
							currentGuideStep.Canvas = {
								Position: canvasData.Position || "center-center",
								Width: canvasData.Width || 500,
								Padding: canvasData.Padding || "12",
								Radius: canvasData.Radius || 8,
								BorderSize: canvasData.BorderSize || 0,
								BorderColor: canvasData.BorderColor || "#000000",
								BackgroundColor: canvasData.BackgroundColor || "#ffffff",
							};

							console.log("Updated AI announcement interactionData for step", currentStepIndex, {
								canvasData: canvasData,
								interactionData: currentGuideStep.Canvas
							});
						}
					});
				},

				// Function to initialize tooltip metadata for AI tours
				initializeAITourTooltipMetadata: () => {
					set((state) => {
						if (!state.createWithAI || !state.interactionData || state.selectedTemplate !== "Tour") {
							return;
						}

						const aiGuideSteps = state.interactionData?.GuideStep || [];
						if (aiGuideSteps.length === 0) {
							return;
						}

						console.log("Initializing tooltip/hotspot metadata for AI tour with", aiGuideSteps.length, "steps");

						// Ensure toolTipGuideMetaData array is large enough for all steps
						while (state.toolTipGuideMetaData.length < aiGuideSteps.length) {
							state.toolTipGuideMetaData.push(null);
						}

						// Initialize tooltip/hotspot metadata for each tooltip and hotspot step
						aiGuideSteps.forEach((stepData: any, stepIndex: number) => {
							if (stepData.StepType === "Tooltip" || stepData.StepType === "Hotspot") {
								// Only initialize if metadata doesn't exist
								if (!state.toolTipGuideMetaData[stepIndex]) {
									console.log(`Initializing ${stepData.StepType} metadata for step`, stepIndex);

									const containers = [];

									// Add RTE containers from TextFieldProperties
									if (stepData.TextFieldProperties && stepData.TextFieldProperties.length > 0) {
										stepData.TextFieldProperties.forEach((textField: any) => {
											containers.push({
												id: textField.Id || crypto.randomUUID(),
												type: "rte",
												placeholder: "Enter your text here...",
												rteBoxValue: textField.Text || "",
												style: {
													backgroundColor: "transparent",
												},
											});
										});
									}

									// Add button containers from ButtonSection
									if (stepData.ButtonSection && stepData.ButtonSection.length > 0) {
										stepData.ButtonSection.forEach((section: any) => {
											if (section.CustomButtons && section.CustomButtons.length > 0) {
												const buttons = section.CustomButtons.map((button: any) => ({
													id: button.ButtonId || crypto.randomUUID(),
													name: button.ButtonName || "Button",
													position: button.Alignment || "center",
													type: button.ButtonStyle || "primary",
													isEditing: false,
													index: 0,
													style: {
														backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || "#5F9EA0",
														borderColor: button.ButtonProperties?.ButtonBorderColor || "#5F9EA0",
														color: button.ButtonProperties?.ButtonTextColor || "#ffffff",
													},
													actions: {
														value: button.ButtonAction?.Action || "close",
														targetURL: button.ButtonAction?.TargetUrl || "",
														tab: button.ButtonAction?.ActionValue || "same-tab",
														interaction: null,
													},
													survey: null,
												}));

												containers.push({
													id: section.Id || crypto.randomUUID(),
													type: "button",
													buttons: buttons,
													style: {
														backgroundColor: "transparent",
													},
												});
											}
										});
									}

									// If no containers were created, add a default RTE container
									if (containers.length === 0) {
										containers.push({
											id: crypto.randomUUID(),
											type: "rte",
											placeholder: "Enter your text here...",
											rteBoxValue: "",
											style: {
												backgroundColor: "transparent",
											},
										});
									}

									// Create new tooltip/hotspot metadata
									const newTooltipMetadata = {
										containers: containers,
										stepName: stepData.StepTitle || `Step ${stepIndex + 1}`,
										stepDescription: stepData.Description || "",
										stepId: stepData.StepId || crypto.randomUUID(),
										stepType: stepData.StepType, // Use the actual step type (Tooltip or Hotspot)
										currentStep: stepIndex + 1,
										xpath: {
											value: stepData.ElementPath || "",
											PossibleElementPath: stepData.PossibleElementPath || "",
											position: { x: 0, y: 0 }
										},
										id: crypto.randomUUID(),
										hotspots: stepData.Hotspot || {
											backgroundColor: "rgba(255, 255, 255, 0.8)",
											borderColor: "#007bff",
											borderSize: "2px",
											borderRadius: "8px",
											animation: "pulse",
											animationDuration: "2s",
											zIndex: 1000,
										},
										canvas: stepData.Canvas || {
											width: "300px",
											height: "auto",
											backgroundColor: "#ffffff",
											borderColor: "#e0e0e0",
											borderSize: "1px",
											borderRadius: "8px",
											padding: "16px",
											position: "bottom",
											xaxis: "0px",
											yaxis: "0px",
											zindex: 1000,
											autoposition: stepData.AutoPosition || false,
										},
										design: {
											gotoNext: {
												NextStep: stepData.Design?.GotoNext?.NextStep || "",
												ButtonId: stepData.Design?.GotoNext?.ButtonId || "",
												elementPath: stepData.Design?.GotoNext?.elementPath || "",
												ButtonName: stepData.Design?.GotoNext?.ButtonName || "",
											},
											element: {
												progress: stepData.Tooltip?.EnableProgress ? "Template1" : "",
												isDismiss: stepData.Modal?.DismissOption || false,
												progressSelectedOption: stepData.Tooltip?.ProgressTemplate || 1,
												progressColor: stepData.Modal?.ProgressColor || "var(--primarycolor)",
											},
										},
									};

									state.toolTipGuideMetaData[stepIndex] = newTooltipMetadata;
									console.log(`Initialized ${stepData.StepType} metadata for step`, stepIndex, newTooltipMetadata);
								}
							}
						});

						console.log("AI tour tooltip/hotspot metadata initialization completed");
					});
				},

				// Function to synchronize AI tour data for mixed step types
				syncAITourDataForPreview: () => {
					set((state) => {
						if (!state.createWithAI || !state.interactionData || state.selectedTemplate !== "Tour") {
							return;
						}

						const aiGuideSteps = state.interactionData?.GuideStep || [];
						if (aiGuideSteps.length === 0) {
							return;
						}

						// First, ensure tooltip metadata is initialized for all tooltip steps
						state.initializeAITourTooltipMetadata();

						// Sync data for all steps, not just current step
						aiGuideSteps.forEach((stepData: any, stepIndex: number) => {
							const stepType = stepData.StepType;

							console.log("Processing step", stepIndex, "with type", stepType, {
								existingElementPath: stepData.ElementPath,
								existingPossibleElementPath: stepData.PossibleElementPath,
								hasMetadata: !!state.toolTipGuideMetaData[stepIndex],
								metadataXPath: state.toolTipGuideMetaData[stepIndex]?.xpath?.value
							});

							// For tooltip and hotspot steps, ensure bidirectional sync between toolTipGuideMetaData and interactionData
							if (stepType === "Tooltip" || stepType === "Hotspot") {
								const existingMetadata = state.toolTipGuideMetaData[stepIndex];

								// If we have tooltip/hotspot metadata with XPath data, sync it back to interactionData
								if (existingMetadata?.xpath?.value && existingMetadata.xpath.value.trim() !== "") {
									// Update interactionData with the latest XPath from toolTipGuideMetaData
									stepData.ElementPath = existingMetadata.xpath.value;
									stepData.PossibleElementPath = existingMetadata.xpath.PossibleElementPath || "";

									console.log(`Synced XPath from toolTipGuideMetaData to interactionData for ${stepType} step`, stepIndex, {
										ElementPath: stepData.ElementPath,
										PossibleElementPath: stepData.PossibleElementPath
									});
								} else {
									console.log(`No XPath data in toolTipGuideMetaData for ${stepType} step`, stepIndex, {
										hasMetadata: !!existingMetadata,
										xpathValue: existingMetadata?.xpath?.value,
										originalElementPath: stepData.ElementPath
									});
								}

								// Sync button actions and design settings from toolTipGuideMetaData to interactionData
								if (existingMetadata) {
									// Update Design.GotoNext settings
									if (existingMetadata.design?.gotoNext) {
										stepData.Design = {
											GotoNext: existingMetadata.design.gotoNext
										};
									}

									// Update ButtonSection from button containers
									const buttonContainers = existingMetadata.containers?.filter(c => c.type === "button") || [];
									if (buttonContainers.length > 0) {
										stepData.ButtonSection = buttonContainers.map((container: any) => ({
											Id: container.id,
											CustomButtons: container.buttons?.map((button: any) => ({
												ButtonStyle: button.type || "primary",
												ButtonName: button.name,
												Alignment: button.position || "center",
												ButtonId: button.id,
												BackgroundColor: container.style?.backgroundColor || "transparent",
												ButtonAction: {
													Action: button.actions?.value || "close",
													TargetUrl: button.actions?.targetURL || "",
													ActionValue: button.actions?.tab || "same-tab"
												},
												Padding: {
													Top: 0,
													Right: 0,
													Bottom: 0,
													Left: 0,
												},
												ButtonProperties: {
													Padding: 0,
													Width: 0,
													Font: 0,
													FontSize: 0,
													ButtonTextColor: button.style?.color || "#ffffff",
													ButtonBackgroundColor: button.style?.backgroundColor || "#5F9EA0",
													ButtonBorderColor: button.style?.borderColor || "#5F9EA0",
												}
											})) || [],
										}));

										console.log("Synced button actions from toolTipGuideMetaData to interactionData for step", stepIndex, {
											ButtonSection: stepData.ButtonSection
										});
									}
								} else if (stepData.ElementPath && stepData.ElementPath.trim() !== "") {
									// If interactionData has XPath but metadata doesn't, create/update metadata
									if (!existingMetadata) {
										// Create new tooltip/hotspot metadata
										const containers = [];

										// Add RTE containers from TextFieldProperties
										if (stepData.TextFieldProperties && stepData.TextFieldProperties.length > 0) {
											stepData.TextFieldProperties.forEach((textField: any) => {
												containers.push({
													id: textField.Id || crypto.randomUUID(),
													type: "rte",
													placeholder: "Enter your text here...",
													rteBoxValue: textField.Text || "",
													style: {
														backgroundColor: "transparent",
													},
												});
											});
										}

										// Add button containers from ButtonSection
										if (stepData.ButtonSection && stepData.ButtonSection.length > 0) {
											stepData.ButtonSection.forEach((section: any) => {
												containers.push({
													id: section.Id || crypto.randomUUID(),
													type: "button",
													buttons: section.CustomButtons || [],
												});
											});
										}

										// Create new tooltip/hotspot metadata
										const newTooltipMetadata = {
											containers: containers,
											stepName: stepData.StepTitle || `Step ${stepIndex + 1}`,
											stepDescription: stepData.Description || "",
											stepId: stepData.StepId || crypto.randomUUID(),
											stepType: stepType, // Use the actual step type (Tooltip or Hotspot)
											currentStep: stepIndex + 1,
											xpath: {
												value: stepData.ElementPath || "",
												PossibleElementPath: stepData.PossibleElementPath || "",
												position: { x: 0, y: 0 }
											},
											id: crypto.randomUUID(),
											hotspots: stepData.Hotspot || {
												backgroundColor: "rgba(255, 255, 255, 0.8)",
												borderColor: "#007bff",
												borderSize: "2px",
												borderRadius: "8px",
												animation: "pulse",
												animationDuration: "2s",
												zIndex: 1000,
											},
											canvas: stepData.Canvas || {
												width: "300px",
												height: "auto",
												backgroundColor: "#ffffff",
												borderColor: "#e0e0e0",
												borderSize: "1px",
												borderRadius: "8px",
												padding: "16px",
												position: "bottom",
												xaxis: "0px",
												yaxis: "0px",
												zindex: 1000,
												autoposition: stepData.AutoPosition || false,
											},
											design: {
												gotoNext: {
													NextStep: stepData.Design?.GotoNext?.NextStep || "",
													ButtonId: stepData.Design?.GotoNext?.ButtonId || "",
													elementPath: stepData.Design?.GotoNext?.elementPath || "",
													ButtonName: stepData.Design?.GotoNext?.ButtonName || "",
												},
												element: {
													progress: stepData.Tooltip?.EnableProgress ? "Template1" : "",
													isDismiss: stepData.Modal?.DismissOption || false,
													progressSelectedOption: stepData.Tooltip?.ProgressTemplate || 1,
													progressColor: stepData.Modal?.ProgressColor || "var(--primarycolor)",
												},
											},
										};

										// Ensure the array is large enough
										while (state.toolTipGuideMetaData.length <= stepIndex) {
											state.toolTipGuideMetaData.push(null);
										}

										state.toolTipGuideMetaData[stepIndex] = newTooltipMetadata;
									} else {
										// Update existing metadata with XPath from interactionData
										existingMetadata.xpath = {
											value: stepData.ElementPath || "",
											PossibleElementPath: stepData.PossibleElementPath || "",
											position: existingMetadata.xpath?.position || { x: 0, y: 0 }
										};
									}
								}
							}
							// For banner and announcement steps in tours, sync RTE data from toolTipGuideMetaData to interactionData
							else if (stepType === "Banner" || stepType === "Announcement") {
								const existingMetadata = state.toolTipGuideMetaData[stepIndex];

								console.log(`Processing ${stepType} step`, stepIndex, {
									hasMetadata: !!existingMetadata,
									containers: existingMetadata?.containers?.length || 0
								});

								// Sync RTE data from toolTipGuideMetaData to interactionData
								if (existingMetadata?.containers) {
									// Update TextFieldProperties from RTE containers
									const rteContainers = existingMetadata.containers.filter(c => c.type === "rte");
									if (rteContainers.length > 0) {
										stepData.TextFieldProperties = rteContainers.map((container: any) => ({
											Id: container.id,
											Text: container.rteBoxValue || "",
											Alignment: "",
											Hyperlink: "",
											Emoji: "",
											TextProperties: {
												Bold: false,
												Italic: false,
												BulletPoints: false,
												TextFormat: "",
												TextColor: "",
											},
										}));

										console.log(`Synced RTE data from toolTipGuideMetaData to interactionData for ${stepType} step`, stepIndex, {
											TextFieldProperties: stepData.TextFieldProperties
										});
									}

									// Update ButtonSection from button containers
									const buttonContainers = existingMetadata.containers?.filter(c => c.type === "button") || [];
									if (buttonContainers.length > 0) {
										stepData.ButtonSection = buttonContainers.map((container: any) => ({
											Id: container.id,
											BackgroundColor: container.style?.backgroundColor || "transparent",
											CustomButtons: container.buttons?.map((button: any) => ({
												ButtonStyle: button.type || "primary",
												ButtonName: button.name,
												Alignment: button.position || "center",
												ButtonId: button.id,
												BackgroundColor: container.style?.backgroundColor || "transparent",
												ButtonAction: {
													Action: button.actions?.value || "close",
													TargetUrl: button.actions?.targetURL || "",
													ActionValue: button.actions?.tab || "same-tab"
												},
												Padding: {
													Top: 0,
													Right: 0,
													Bottom: 0,
													Left: 0,
												},
												ButtonProperties: {
													Padding: 0,
													Width: 0,
													Font: 0,
													FontSize: 0,
													ButtonTextColor: button.style?.color || "#ffffff",
													ButtonBackgroundColor: button.style?.backgroundColor || "#5F9EA0",
													ButtonBorderColor: button.style?.borderColor || "#5F9EA0",
												}
											})) || [],
										}));

										console.log(`Synced button data from toolTipGuideMetaData to interactionData for ${stepType} step`, stepIndex, {
											ButtonSection: stepData.ButtonSection
										});
									}

									// Update Canvas settings from metadata
									if (existingMetadata.canvas) {
										stepData.Canvas = {
											...stepData.Canvas,
											...existingMetadata.canvas
										};

										console.log(`Synced canvas data from toolTipGuideMetaData to interactionData for ${stepType} step`, stepIndex, {
											Canvas: stepData.Canvas
										});
									}
								} else if (stepData.TextFieldProperties && stepData.TextFieldProperties.length > 0) {
									// If interactionData has RTE data but metadata doesn't, create/update metadata
									if (!existingMetadata) {
										// Create new metadata for banner/announcement step
										const containers = [];

										// Add RTE containers from TextFieldProperties
										stepData.TextFieldProperties.forEach((textField: any) => {
											containers.push({
												id: textField.Id || crypto.randomUUID(),
												type: "rte",
												placeholder: "Enter your text here...",
												rteBoxValue: textField.Text || "",
												style: {
													backgroundColor: "transparent",
												},
											});
										});

										// Add button containers from ButtonSection
										if (stepData.ButtonSection && stepData.ButtonSection.length > 0) {
											stepData.ButtonSection.forEach((section: any) => {
												containers.push({
													id: section.Id || crypto.randomUUID(),
													type: "button",
													buttons: section.CustomButtons || [],
													style: {
														backgroundColor: section.BackgroundColor || "transparent",
													},
												});
											});
										}

										// Create new metadata
										const newMetadata = {
											containers: containers,
											stepName: stepData.StepTitle || `Step ${stepIndex + 1}`,
											stepDescription: stepData.Description || "",
											stepId: stepData.StepId || crypto.randomUUID(),
											stepType: stepType,
											currentStep: stepIndex + 1,
											xpath: {
												value: "",
												PossibleElementPath: "",
												position: { x: 0, y: 0 }
											},
											id: crypto.randomUUID(),
											hotspots: {
												backgroundColor: "rgba(255, 255, 255, 0.8)",
												borderColor: "#007bff",
												borderSize: "2px",
												borderRadius: "8px",
												animation: "pulse",
												animationDuration: "2s",
												zIndex: 1000,
											},
											canvas: stepData.Canvas || {
												width: stepType === "Banner" ? "100%" : "500px",
												height: "auto",
												backgroundColor: "#ffffff",
												borderColor: "#e0e0e0",
												borderSize: "1px",
												borderRadius: "8px",
												padding: "16px",
												position: stepType === "Banner" ? "Cover Top" : "center",
												xaxis: "0px",
												yaxis: "0px",
												zindex: 1000,
											},
											design: {
												gotoNext: {
													NextStep: stepData.Design?.GotoNext?.NextStep || "",
													ButtonId: stepData.Design?.GotoNext?.ButtonId || "",
													elementPath: stepData.Design?.GotoNext?.elementPath || "",
													ButtonName: stepData.Design?.GotoNext?.ButtonName || "",
												},
												element: {
													progress: "",
													isDismiss: stepData.Modal?.DismissOption || false,
													progressSelectedOption: 1,
													progressColor: stepData.Modal?.ProgressColor || "var(--primarycolor)",
												},
											},
										};

										// Ensure the array is large enough
										while (state.toolTipGuideMetaData.length <= stepIndex) {
											state.toolTipGuideMetaData.push(null);
										}

										state.toolTipGuideMetaData[stepIndex] = newMetadata;

										console.log(`Created new metadata for ${stepType} step`, stepIndex, {
											metadata: newMetadata
										});
									}
								}
							}
						});

						console.log("AI tour data sync completed for all steps");
					});
				},
				// Function to sync button data from buttonsContainer to metadata containers
				syncButtonContainerToMetadata: () => {
					set((state) => {
						if (state.createWithAI) {
							return; // Skip for AI guides as they have their own sync logic
						}

						const currentStepIndex = state.currentStep - 1;
						let targetMetadata: any = null;

						// Determine which metadata to update based on template type
						if (state.selectedTemplate === "Announcement") {
							if (!state.announcementGuideMetaData[currentStepIndex]) {
								state.announcementGuideMetaData[currentStepIndex] = {
									containers: [],
									stepName: `Step ${currentStepIndex + 1}`,
									stepDescription: "",
									stepType: "Announcement",
									currentStep: currentStepIndex + 1,
									xpath: { value: "", PossibleElementPath: "", position: { x: 0, y: 0 } },
									id: crypto.randomUUID(),
									hotspots: {},
									canvas: {},
									design: {},
								};
							}
							targetMetadata = state.announcementGuideMetaData[currentStepIndex];
						} else if (state.selectedTemplate === "Tooltip") {
							if (!state.toolTipGuideMetaData[currentStepIndex]) {
								state.toolTipGuideMetaData[currentStepIndex] = {
									containers: [],
									stepName: `Step ${currentStepIndex + 1}`,
									stepDescription: "",
									stepType: "Tooltip",
									currentStep: currentStepIndex + 1,
									xpath: { value: "", PossibleElementPath: "", position: { x: 0, y: 0 } },
									id: crypto.randomUUID(),
									hotspots: {},
									canvas: {},
									design: {},
								};
							}
							targetMetadata = state.toolTipGuideMetaData[currentStepIndex];
						} else if (state.selectedTemplate === "Tour") {
							if (state.selectedTemplateTour === "Announcement" || state.selectedTemplateTour === "Tooltip" || state.selectedTemplateTour === "Banner") {
								if (!state.toolTipGuideMetaData[currentStepIndex]) {
									state.toolTipGuideMetaData[currentStepIndex] = {
										containers: [],
										stepName: `Step ${currentStepIndex + 1}`,
										stepDescription: "",
										stepType: state.selectedTemplateTour,
										currentStep: currentStepIndex + 1,
										xpath: { value: "", PossibleElementPath: "", position: { x: 0, y: 0 } },
										id: crypto.randomUUID(),
										hotspots: {},
										canvas: {},
										design: {},
									};
								}
								targetMetadata = state.toolTipGuideMetaData[currentStepIndex];
							}
						}

						if (!targetMetadata) {
							return;
						}

						// Remove existing button containers from metadata
						targetMetadata.containers = targetMetadata.containers.filter((c: any) => c.type !== "button");

						// Add button containers from buttonsContainer with proper action preservation
						state.buttonsContainer.forEach((buttonContainer: any) => {
							const metadataContainer = {
								id: buttonContainer.id,
								type: "button",
								buttons: (buttonContainer.buttons || []).map((button: any) => ({
									...button,
									// Ensure button actions are properly preserved
									actions: {
										value: button.actions?.value || "close",
										targetURL: button.actions?.targetURL || "",
										tab: button.actions?.tab || "same-tab",
										interaction: button.actions?.interaction || null,
									}
								})),
								style: buttonContainer.style || {},
							};
							targetMetadata.containers.push(metadataContainer);
						});

						console.log("Synced button data from buttonsContainer to metadata", {
							template: state.selectedTemplate,
							templateTour: state.selectedTemplateTour,
							stepIndex: currentStepIndex,
							buttonContainers: state.buttonsContainer.length,
							metadataContainers: targetMetadata.containers.filter((c: any) => c.type === "button").length
						});
					});
				},

				// Function to sync button data from metadata containers to buttonsContainer
				syncMetadataToButtonContainer: () => {
					set((state) => {
						if (state.createWithAI) {
							return; // Skip for AI guides as they have their own sync logic
						}

						const currentStepIndex = state.currentStep - 1;
						let sourceMetadata: any = null;

						// Determine which metadata to read from based on template type
						if (state.selectedTemplate === "Announcement") {
							sourceMetadata = state.announcementGuideMetaData[currentStepIndex];
						} else if (state.selectedTemplate === "Tooltip") {
							sourceMetadata = state.toolTipGuideMetaData[currentStepIndex];
						} else if (state.selectedTemplate === "Tour") {
							if (state.selectedTemplateTour === "Announcement" || state.selectedTemplateTour === "Tooltip" || state.selectedTemplateTour === "Banner") {
								sourceMetadata = state.toolTipGuideMetaData[currentStepIndex];
							}
						}

						if (!sourceMetadata || !sourceMetadata.containers) {
							// Clear buttonsContainer if no metadata exists
							state.buttonsContainer = [];
							return;
						}

						// Extract button containers from metadata
						const buttonContainers = sourceMetadata.containers.filter((c: any) => c.type === "button");

						// Update buttonsContainer with data from metadata, preserving button actions
						state.buttonsContainer = buttonContainers.map((metadataContainer: any) => ({
							id: metadataContainer.id,
							buttons: (metadataContainer.buttons || []).map((button: any) => ({
								...button,
								// Ensure button actions are properly loaded
								actions: {
									value: button.actions?.value || "close",
									targetURL: button.actions?.targetURL || "",
									tab: button.actions?.tab || "same-tab",
									interaction: button.actions?.interaction || null,
								}
							})),
							style: metadataContainer.style || {},
							type: "button",
						}));

						console.log("Synced button data from metadata to buttonsContainer", {
							template: state.selectedTemplate,
							templateTour: state.selectedTemplateTour,
							stepIndex: currentStepIndex,
							metadataContainers: buttonContainers.length,
							buttonContainers: state.buttonsContainer.length
						});
					});
				},

				// Function to force save any pending button configuration changes
				forceSaveButtonConfiguration: () => {
					set((state) => {
						if (state.createWithAI) {
							return; // Skip for AI guides as they have their own sync logic
						}

						// Force sync button data to ensure no configuration is lost
						state.syncButtonContainerToMetadata();
						state.syncMetadataToSavedGuideData();

						console.log("🔄 Force saved button configuration for step", state.currentStep);
					});
				},

				// Function to sync button data from metadata to savedGuideData for preview
				syncMetadataToSavedGuideData: () => {
					set((state) => {
						if (state.createWithAI) {
							return; // Skip for AI guides as they have their own sync logic
						}

						const currentStepIndex = state.currentStep - 1;
						let sourceMetadata: any = null;

						// Determine which metadata to read from based on template type
						if (state.selectedTemplate === "Announcement") {
							sourceMetadata = state.announcementGuideMetaData[currentStepIndex];
						} else if (state.selectedTemplate === "Tooltip") {
							sourceMetadata = state.toolTipGuideMetaData[currentStepIndex];
						} else if (state.selectedTemplate === "Tour") {
							if (state.selectedTemplateTour === "Announcement" || state.selectedTemplateTour === "Tooltip" || state.selectedTemplateTour === "Banner") {
								sourceMetadata = state.toolTipGuideMetaData[currentStepIndex];
							}
						}

						if (!sourceMetadata || !sourceMetadata.containers || !state.savedGuideData?.GuideStep?.[currentStepIndex]) {
							return;
						}

						// Extract button containers from metadata
						const buttonContainers = sourceMetadata.containers.filter((c: any) => c.type === "button");

						// Update savedGuideData ButtonSection
						if (buttonContainers.length > 0) {
							state.savedGuideData.GuideStep[currentStepIndex].ButtonSection = buttonContainers.map((container: any) => ({
								Id: container.id,
								CustomButtons: container.buttons?.map((button: any) => ({
									ButtonStyle: button.type || "primary",
									ButtonName: button.name,
									Alignment: button.position || "center",
									ButtonId: button.id,
									BackgroundColor: container.style?.backgroundColor || "transparent",
									ButtonAction: {
										Action: button.actions?.value || "close",
										TargetUrl: button.actions?.targetURL || "",
										ActionValue: button.actions?.tab || "same-tab"
									},
									Padding: {
										Top: 0,
										Right: 0,
										Bottom: 0,
										Left: 0,
									},
									ButtonProperties: {
										Padding: 0,
										Width: 0,
										Font: 0,
										FontSize: 0,
										ButtonTextColor: button.style?.color || "#ffffff",
										ButtonBackgroundColor: button.style?.backgroundColor || "#5F9EA0",
										ButtonBorderColor: button.style?.borderColor || "#5F9EA0",
									},
									BorderColor: button.style?.borderColor || "#5F9EA0",
									TextColor: button.style?.color || "#ffffff",
								})) || [],
							}));
						} else {
							// Clear ButtonSection if no button containers exist
							state.savedGuideData.GuideStep[currentStepIndex].ButtonSection = [];
						}

						console.log("Synced button data from metadata to savedGuideData", {
							template: state.selectedTemplate,
							templateTour: state.selectedTemplateTour,
							stepIndex: currentStepIndex,
							buttonContainers: buttonContainers.length,
							buttonSections: state.savedGuideData.GuideStep[currentStepIndex].ButtonSection?.length || 0
						});
					});
				},
				// Function to sync current step data before navigation in AI tours
				syncCurrentStepDataForAITour: () => {
					set((state) => {
						if (!state.createWithAI || !state.interactionData || state.selectedTemplate !== "Tour") {
							return;
						}

						const currentStepIndex = state.currentStep - 1;
						const currentStepMetadata = state.toolTipGuideMetaData[currentStepIndex];

						if (!currentStepMetadata || !state.interactionData.GuideStep?.[currentStepIndex]) {
							return;
						}

						const currentGuideStep = state.interactionData.GuideStep[currentStepIndex];
						const stepType = currentGuideStep.StepType;

						console.log(`Syncing current step data before navigation for ${stepType} step`, currentStepIndex, {
							hasMetadata: !!currentStepMetadata,
							containers: currentStepMetadata.containers?.length || 0,
							rteContainers: currentStepMetadata.containers?.filter(c => c.type === "rte").map(c => ({ id: c.id, rteBoxValue: c.rteBoxValue })) || []
						});

						// Update TextFieldProperties from RTE containers
						const rteContainers = currentStepMetadata.containers?.filter(c => c.type === "rte") || [];
						if (rteContainers.length > 0) {
							currentGuideStep.TextFieldProperties = rteContainers.map((container: any) => ({
								Id: container.id,
								Text: container.rteBoxValue || "",
								Alignment: "",
								Hyperlink: "",
								Emoji: "",
								TextProperties: {
									Bold: false,
									Italic: false,
									BulletPoints: false,
									TextFormat: "",
									TextColor: "",
								},
							}));

							console.log(`Updated TextFieldProperties for ${stepType} step`, currentStepIndex, {
								TextFieldProperties: currentGuideStep.TextFieldProperties
							});
						}

						// Update ButtonSection from button containers
						const buttonContainers = currentStepMetadata.containers?.filter(c => c.type === "button") || [];
						if (buttonContainers.length > 0) {
							currentGuideStep.ButtonSection = buttonContainers.map((container: any) => ({
								Id: container.id,
								BackgroundColor: container.style?.backgroundColor || "transparent",
								CustomButtons: container.buttons?.map((button: any) => ({
									ButtonStyle: button.type || "primary",
									ButtonName: button.name,
									Alignment: button.position || "center",
									ButtonId: button.id,
									BackgroundColor: container.style?.backgroundColor || "transparent",
									ButtonAction: {
										Action: button.actions?.value || "close",
										TargetUrl: button.actions?.targetURL || "",
										ActionValue: button.actions?.tab || "same-tab"
									},
									Padding: {
										Top: 0,
										Right: 0,
										Bottom: 0,
										Left: 0,
									},
									ButtonProperties: {
										Padding: 0,
										Width: 0,
										Font: 0,
										FontSize: 0,
										ButtonTextColor: button.style?.color || "#ffffff",
										ButtonBackgroundColor: button.style?.backgroundColor || "#5F9EA0",
										ButtonBorderColor: button.style?.borderColor || "#5F9EA0",
									}
								})) || [],
							}));

							console.log(`Updated ButtonSection for ${stepType} step`, currentStepIndex, {
								ButtonSection: currentGuideStep.ButtonSection
							});
						}

						// Update Canvas settings
						if (currentStepMetadata.canvas) {
							currentGuideStep.Canvas = {
								...currentGuideStep.Canvas,
								...currentStepMetadata.canvas
							};

							console.log(`Updated Canvas for ${stepType} step`, currentStepIndex, {
								Canvas: currentGuideStep.Canvas
							});
						}

						// Update Design settings
						if (currentStepMetadata.design?.gotoNext) {
							currentGuideStep.Design = {
								...currentGuideStep.Design,
								GotoNext: currentStepMetadata.design.gotoNext
							};

							console.log(`Updated Design for ${stepType} step`, currentStepIndex, {
								Design: currentGuideStep.Design
							});
						}

						// Update progress bar settings from GLOBAL state (not step-specific metadata)
						// This ensures that the global progress bar setting is maintained across all steps
						if (currentStepMetadata.design?.element) {
							// Use global progress bar state instead of step-specific metadata
							// This prevents individual step settings from overriding the global state
							currentGuideStep.Tooltip = {
								...currentGuideStep.Tooltip,
								EnableProgress: state.progress || false,
								ProgressTemplate: state.selectedOption?.toString() || "1",
								InteractWithPage: state.pageinteraction,
							};

							// Update Modal settings with global progress bar state
							currentGuideStep.Modal = {
								...currentGuideStep.Modal,
								DismissOption: state.dismiss || false,
								ProgressColor: state.ProgressColor || "var(--primarycolor)",
							};

							console.log(`Updated progress bar settings for ${stepType} step using GLOBAL state`, currentStepIndex, {
								EnableProgress: state.progress || false,
								ProgressTemplate: state.selectedOption || 1,
								DismissOption: state.dismiss || false,
								ProgressColor: state.ProgressColor || "var(--primarycolor)",
								note: "Using global state to maintain consistency across all steps"
							});
						}

						console.log(`Completed syncing current step data for ${stepType} step`, currentStepIndex);
					});
				},

				// Function to restore RTE data from interactionData back to toolTipGuideMetaData for AI tours
				restoreAITourRTEData: () => {
					set((state) => {
						if (!state.createWithAI || !state.interactionData || state.selectedTemplate !== "Tour") {
							console.log("Skipping AI tour RTE restoration - conditions not met:", {
								createWithAI: state.createWithAI,
								hasInteractionData: !!state.interactionData,
								selectedTemplate: state.selectedTemplate
							});
							return;
						}

						const aiGuideSteps = state.interactionData?.GuideStep || [];
						if (aiGuideSteps.length === 0) {
							console.log("Skipping AI tour RTE restoration - no guide steps");
							return;
						}

						console.log("Restoring AI tour RTE data from interactionData to toolTipGuideMetaData", {
							totalSteps: aiGuideSteps.length,
							currentStep: state.currentStep,
							stepTypes: aiGuideSteps.map((step: any, index: number) => ({ index, stepType: step.StepType }))
						});

						// Create a new array to avoid mutating the existing one
						const updatedToolTipGuideMetaData = [...state.toolTipGuideMetaData];

						aiGuideSteps.forEach((stepData: any, stepIndex: number) => {
							const stepType = stepData.StepType;

							// Restore data for all step types in tours
							if (stepType === "Tooltip" || stepType === "Hotspot" || stepType === "Banner" || stepType === "Announcement") {
								const existingMetadata = updatedToolTipGuideMetaData[stepIndex];

								console.log(`Processing ${stepType} step ${stepIndex} for restoration:`, {
									hasExistingMetadata: !!existingMetadata,
									hasTextFieldProperties: !!(stepData.TextFieldProperties && stepData.TextFieldProperties.length > 0),
									textFieldCount: stepData.TextFieldProperties?.length || 0,
									existingContainerCount: existingMetadata?.containers?.length || 0
								});

								if (existingMetadata && stepData.TextFieldProperties && stepData.TextFieldProperties.length > 0) {
									// Create a new containers array to avoid mutation
									const updatedContainers = existingMetadata.containers?.map(container => {
										if (container.type === "rte") {
											const matchingTextField = stepData.TextFieldProperties?.find((tf: any) => tf.Id === container.id);
											if (matchingTextField) {
												console.log(`Restoring RTE data for container ${container.id}:`, {
													oldValue: container.rteBoxValue,
													newValue: matchingTextField.Text
												});
												return {
													...container,
													rteBoxValue: matchingTextField.Text || ""
												};
											} else {
												console.log(`No matching text field found for container ${container.id}`);
											}
										}
										return container;
									}) || [];

									// Update the metadata with restored data
									updatedToolTipGuideMetaData[stepIndex] = {
										...existingMetadata,
										containers: updatedContainers
									};

									console.log(`Successfully restored RTE data for ${stepType} step ${stepIndex}:`, {
										textFields: stepData.TextFieldProperties.map((tf: any) => ({ id: tf.Id, text: tf.Text })),
										updatedContainers: updatedContainers.filter(c => c.type === "rte").map(c => ({ id: c.id, rteBoxValue: c.rteBoxValue }))
									});
								} else {
									console.log(`Skipping restoration for ${stepType} step ${stepIndex} - missing data:`, {
										hasExistingMetadata: !!existingMetadata,
										hasTextFieldProperties: !!(stepData.TextFieldProperties && stepData.TextFieldProperties.length > 0)
									});
								}
							}
						});

						// Update the state with the new array
						state.toolTipGuideMetaData = updatedToolTipGuideMetaData;
						console.log("Completed AI tour RTE data restoration");

						// Log the final state for announcement steps specifically
						updatedToolTipGuideMetaData.forEach((metadata, index) => {
							if (aiGuideSteps[index]?.StepType === "Announcement") {
								console.log(`Final announcement step ${index} metadata after restoration:`, {
									stepType: aiGuideSteps[index].StepType,
									containers: metadata.containers?.filter(c => c.type === "rte").map(c => ({ id: c.id, rteBoxValue: c.rteBoxValue })) || []
								});
							}
						});
					});
				},

				// Function to apply global progress bar state to ALL step types in AI tours
				syncGlobalProgressBarStateForAITour: () => {
					set((state) => {
						if (!state.createWithAI || !state.interactionData || state.selectedTemplate !== "Tour") {
							return;
						}

						const aiGuideSteps = state.interactionData?.GuideStep || [];
						if (aiGuideSteps.length === 0) {
							return;
						}

						console.log("🔄 syncGlobalProgressBarStateForAITour: Applying global progress bar state to all step types", {
							totalSteps: aiGuideSteps.length,
							stepTypes: aiGuideSteps.map((step: any, index: number) => ({ index, stepType: step.StepType })),
							globalProgressState: {
								progress: state.progress,
								selectedOption: state.selectedOption,
								dismiss: state.dismiss,
								progressColor: state.ProgressColor
							}
						});

						// Apply progress bar settings to ALL step types in the tour
						aiGuideSteps.forEach((stepData: any, stepIndex: number) => {
							const stepType = stepData.StepType;

							// Update interactionData for all step types
							stepData.Tooltip = {
								...stepData.Tooltip,
								EnableProgress: state.progress || false,
								ProgressTemplate: state.selectedOption?.toString() || "1",
								InteractWithPage: state.pageinteraction,
							};

							stepData.Modal = {
								...stepData.Modal,
								DismissOption: state.dismiss || false,
								ProgressColor: state.ProgressColor || "var(--primarycolor)",
							};

							// Update toolTipGuideMetaData for all step types
							if (state.toolTipGuideMetaData[stepIndex]) {
								const metadata = state.toolTipGuideMetaData[stepIndex];

								// Ensure design structure exists
								if (!metadata.design) {
									metadata.design = {
										gotoNext: {},
										element: {
											progress: "",
											isDismiss: false,
											progressSelectedOption: 1,
											progressColor: "var(--primarycolor)"
										}
									};
								}

								if (!metadata.design.element) {
									metadata.design.element = {
										progress: "",
										isDismiss: false,
										progressSelectedOption: 1,
										progressColor: "var(--primarycolor)"
									};
								}

								// Update metadata with global progress bar state
								metadata.design.element.progress = state.progress ? "Template1" : "";
								metadata.design.element.progressSelectedOption = state.selectedOption || 1;
								metadata.design.element.isDismiss = state.dismiss || false;
								metadata.design.element.progressColor = state.ProgressColor || "var(--primarycolor)";
							}

							// Update savedGuideData for all step types
							if (state.savedGuideData?.GuideStep?.[stepIndex]) {
								state.savedGuideData.GuideStep[stepIndex] = {
									...state.savedGuideData.GuideStep[stepIndex],
									Tooltip: {
										...state.savedGuideData.GuideStep[stepIndex].Tooltip,
										EnableProgress: state.progress || false,
										ProgressTemplate: state.selectedOption?.toString() || "1",
									},
									Modal: {
										...state.savedGuideData.GuideStep[stepIndex].Modal,
										DismissOption: state.dismiss || false,
										ProgressColor: state.ProgressColor || "var(--primarycolor)",
									},
								};
							}

							// Update updatedGuideData for all step types (critical for preview mode)
							if (state.updatedGuideData?.GuideStep?.[stepIndex]) {
								state.updatedGuideData.GuideStep[stepIndex] = {
									...state.updatedGuideData.GuideStep[stepIndex],
									Tooltip: {
										...state.updatedGuideData.GuideStep[stepIndex].Tooltip,
										EnableProgress: state.progress || false,
										ProgressTemplate: state.selectedOption?.toString() || "1",
									},
									Modal: {
										...state.updatedGuideData.GuideStep[stepIndex].Modal,
										DismissOption: state.dismiss || false,
										ProgressColor: state.ProgressColor || "var(--primarycolor)",
									},
								};
							}

							console.log(`✅ Applied global progress bar state to ${stepType} step ${stepIndex}:`, {
								stepType,
								EnableProgress: state.progress || false,
								ProgressTemplate: state.selectedOption || 1,
								DismissOption: state.dismiss || false,
								ProgressColor: state.ProgressColor || "var(--primarycolor)"
							});
						});

						console.log("✅ syncGlobalProgressBarStateForAITour: Completed global progress bar synchronization", {
							totalStepsUpdated: aiGuideSteps.length,
							allStepsProgress: state.interactionData?.GuideStep?.map(step => step?.Tooltip?.EnableProgress),
							globalState: {
								progress: state.progress,
								selectedOption: state.selectedOption,
								dismiss: state.dismiss,
								progressColor: state.ProgressColor
							}
						});
					});
				},

				// Function to restore progress bar state from interactionData back to toolTipGuideMetaData for AI tours
				restoreAITourProgressBarState: () => {
					set((state) => {
						if (!state.createWithAI || !state.interactionData || state.selectedTemplate !== "Tour") {
							console.log("Skipping AI tour progress bar restoration - conditions not met:", {
								createWithAI: state.createWithAI,
								hasInteractionData: !!state.interactionData,
								selectedTemplate: state.selectedTemplate
							});
							return;
						}

						const aiGuideSteps = state.interactionData?.GuideStep || [];
						if (aiGuideSteps.length === 0) {
							console.log("Skipping AI tour progress bar restoration - no guide steps");
							return;
						}

						console.log("🔄 restoreAITourProgressBarState: Restoring progress bar state from interactionData", {
							totalSteps: aiGuideSteps.length,
							stepTypes: aiGuideSteps.map((step: any, index: number) => ({ index, stepType: step.StepType }))
						});

						// Find the first step with progress bar data to determine global state
						let globalProgressState = {
							progress: false,
							selectedOption: 1,
							dismiss: false,
							progressColor: "var(--primarycolor)"
						};

						for (const stepData of aiGuideSteps) {
							if (stepData.Tooltip?.EnableProgress !== undefined) {
								globalProgressState.progress = stepData.Tooltip.EnableProgress;
								globalProgressState.selectedOption = parseInt(stepData.Tooltip.ProgressTemplate) || 1;
								globalProgressState.dismiss = stepData.Modal?.DismissOption || false;
								globalProgressState.progressColor = stepData.Modal?.ProgressColor || "var(--primarycolor)";
								break;
							}
						}

						// Update global state variables
						state.progress = globalProgressState.progress;
						state.selectedOption = globalProgressState.selectedOption;
						state.dismiss = globalProgressState.dismiss;
						state.ProgressColor = globalProgressState.progressColor;

						// Restore progress bar state to toolTipGuideMetaData for all step types
						aiGuideSteps.forEach((stepData: any, stepIndex: number) => {
							const stepType = stepData.StepType;
							const existingMetadata = state.toolTipGuideMetaData[stepIndex];

							if (existingMetadata) {
								// Ensure design structure exists
								if (!existingMetadata.design) {
									existingMetadata.design = {
										gotoNext: {},
										element: {
											progress: "",
											isDismiss: false,
											progressSelectedOption: 1,
											progressColor: "var(--primarycolor)"
										}
									};
								}

								if (!existingMetadata.design.element) {
									existingMetadata.design.element = {
										progress: "",
										isDismiss: false,
										progressSelectedOption: 1,
										progressColor: "var(--primarycolor)"
									};
								}

								// Restore progress bar settings from interactionData
								existingMetadata.design.element.progress = globalProgressState.progress ? "Template1" : "";
								existingMetadata.design.element.progressSelectedOption = globalProgressState.selectedOption;
								existingMetadata.design.element.isDismiss = globalProgressState.dismiss;
								existingMetadata.design.element.progressColor = globalProgressState.progressColor;

								console.log(`✅ Restored progress bar state for ${stepType} step ${stepIndex}:`, {
									stepType,
									progress: globalProgressState.progress,
									selectedOption: globalProgressState.selectedOption,
									dismiss: globalProgressState.dismiss,
									progressColor: globalProgressState.progressColor
								});
							}
						});

						console.log("✅ restoreAITourProgressBarState: Completed progress bar state restoration", {
							restoredGlobalState: globalProgressState,
							totalStepsRestored: aiGuideSteps.length,
							globalStateVariables: {
								progress: state.progress,
								selectedOption: state.selectedOption,
								dismiss: state.dismiss,
								progressColor: state.ProgressColor
							}
						});
					});
				},

				setHotspotDataOnEdit: (data) => {
					set((state) => {
						state.toolTipGuideMetaData = data.map((step, index) => {
							const containers = [];

							containers.push(
								...step.TextFieldProperties.map((textField) => ({
									id: textField.Id,
									textBoxRef: null,
									style: {
										backgroundColor: "transparent",
									},
									type: "rte",
									placeholder: "Start typing here...",
									rteBoxValue: textField.Text || "",
								}))
							);

							containers.push(
								...step.ButtonSection.map((buttonSection) => ({
									id: buttonSection.Id || "",
									buttons: buttonSection.CustomButtons.map((button) => ({
										id: button.Id || getRandomID(),
										name: button.ButtonName || "",
										position: button.Alignment || "",
										type: button.ButtonStyle || "primary",
										isEditing: false,
										index: 0,
										style: {
											backgroundColor: button.ButtonProperties.ButtonBackgroundColor || "",
											borderColor: button.ButtonProperties.ButtonBorderColor || "",
											color: button.ButtonProperties.ButtonTextColor || "",
										},
										actions: {
											value: button.ButtonAction.Action,
											tab: button.ButtonAction.ActionValue,
											targetUrl: button.ButtonAction.TargetUrl,
											interactions: null,
										},
										survey: null,
									})),
									style: {
										backgroundColor: buttonSection.CustomButtons[0]?.BackgroundColor || "",
									},
									type: "button",
								}))
							);

							containers.push(
								...step.ImageProperties.map((image) => ({
									id: image.Id || "",
									images: image.CustomImage.map((img) => ({
										altText: img.AltText || "",
										id: image.Id || getRandomID(),
										url: img.Url || "",
										backgroundColor: img.BackgroundColor || "",
										objectFit: img.Fit || "contain",
									})),
									style: {
										backgroundColor: image.CustomImage[0]?.BackgroundColor || "transparent",
										height: image.CustomImage[0]?.SectionHeight || 200,
									},
									type: "image",
								}))
							);

							return {
								id: step.TextFieldProperties[0]?.Id || getRandomID(),
								currentStep: parseInt(step?.StepTitle?.replace("Step ", "")) || 1,
								xpath: {
									value: step.ElementPath || "",
									PossibleElementPath: step.PossibleElementPath || "",
									position: {
										x: step.Position?.XAxisOffset || 0,
										y: step.Position?.YAxisOffset || 0,
									},
								},
								containers,
								design: {
									gotoNext: {
										NextStep: step.Design?.GotoNext?.NextStep || "element",
										ButtonId: step.Design?.GotoNext?.ButtonId || "",
										elementPath: step.Design?.GotoNext?.ElementPath || "",
										ButtonName: step.Design?.GotoNext?.ButtonName || "",
									},
									element: {
										isDismiss: false,
										progress: step?.Tooltip?.ProgressTemplate || "",
									},
								},
								canvas: {
									position: step.Canvas?.Position || "",
									padding: step.Canvas?.Padding || "0",
									zIndex: step.Canvas?.Zindex || "0",
									borderRadius: step.Canvas?.Radius || "0",
									borderSize: step.Canvas?.BorderSize || "0",
									borderColor: step.Canvas?.BorderColor || "transparent",
									backgroundColor: step.Canvas?.BackgroundColor || "#FFFFFF",
									width: step.Canvas?.Width || "300px",
									xaxis: step.Canvas?.XAxisOffset || "100px",
									yaxis: step.Canvas?.YAxisOffset || "100px",
								},
								stepName: step?.StepTitle || `Step ${index + 1}`,
								stepDescription: step?.Description || step?.stepDescription,
								stepType: step?.StepType,
								stepId: step?.StepId,
								hotspots: {
									XPosition: step.Hotspot?.HotspotPosition?.XOffset || "4",
									YPosition: step.Hotspot?.HotspotPosition?.YOffset || "4",
									Type: step.Hotspot?.Type || "Question",
									Color: step.Hotspot?.Color || "#bb0c0c",
									Size: step.Hotspot?.Size || "16",
									PulseAnimation: step.Hotspot?.PulseAnimation || true,
									stopAnimationUponInteraction:
										step.Hotspot?.StopAnimation !== undefined ? step.Hotspot.StopAnimation : false,
									ShowUpon: step.Hotspot?.ShowUpon || "Hovering Hotspot",
									ShowByDefault: step.Hotspot?.ShowByDefault || false,
								},
							};
						});

						// Set the state for the current step and hotspot settings
						const currentStepData = data[state.currentStep - 1];

						// Set overlay settings from API response
						if (currentStepData?.Overlay !== undefined) {
							state.overlayEnabled = currentStepData.Overlay;
						} else if (currentStepData?.Design?.BackdropShadow !== undefined) {
							state.overlayEnabled = currentStepData.Design.BackdropShadow;
						}

						// Set page interaction setting from API response
						if (currentStepData?.Tooltip?.InteractWithPage !== undefined) {
							state.pageinteraction = currentStepData.Tooltip.InteractWithPage;
						}

						// Set progress color from API response
						if (currentStepData?.Modal?.ProgressColor) {
							state.ProgressColor = currentStepData.Modal.ProgressColor;
						}

						// Set dismiss setting from API response
						if (currentStepData?.Modal?.DismissOption !== undefined) {
							state.dismiss = currentStepData.Modal.DismissOption;
							// Also update dismissData for the UI
							state.dismissData = {
								Actions: "",
								DisplayType: "Cross Icon",
								Color: "#000000",
								DontShowAgain: true,
								dismisssel: currentStepData.Modal.DismissOption,
							};
						}

						state.tooltipPosition = data[state.currentStep - 1]?.Canvas?.Position || "middle-center";
						state.tooltippadding = data[state.currentStep - 1]?.Canvas?.Padding || "12px";
						state.tooltipborderradius = data[state.currentStep - 1]?.Canvas?.Radius || "8px";
						state.tooltipbordersize = data[state.currentStep - 1]?.Canvas?.borderSize || "0px";
						state.tooltipBordercolor = data[state.currentStep - 1]?.Canvas?.BorderColor || "transparent";
						state.tooltipBackgroundcolor = data[state.currentStep - 1]?.Canvas?.BackgroundColor || "#FFFFFF";
						state.tooltipWidth = `${data[state.currentStep - 1]?.Canvas?.Width}` || "300px";
						state.tooltipXaxis = `${data[state.currentStep - 1]?.Canvas?.XAxisOffset}` || "100px";
						state.tooltipYaxis = `${data[state.currentStep - 1]?.Canvas?.YAxisOffset}` || "100px";
					});
				},

				setCurrentHoveredElement: (ele: HTMLElement) => {
					set((state) => {
						state.currentHoveredElement = ele;
					});
				},
				activeMenu: null,
				searchText: "",
				setActiveMenu: (menuId: string | null) => {
					set((state) => {
						state.activeMenu = menuId;
					});
				},
				setSearchText: (text: string) => {
					set((state) => {
						state.searchText = text;
					});
				},
				isExtensionClosed: false,
				setIsExtensionClosed: (isExtensionClosed) => {
					set((state) => {
						state.isExtensionClosed = isExtensionClosed;
						if (isExtensionClosed) {
							// When closing, save the current menu state
							if (state.drawerActiveMenu) {
								// Save the current state before closing
								state.activeMenu = state.drawerActiveMenu;
								state.searchText = state.drawerSearchText;
								// Then clear the current state to close popup
								state.drawerActiveMenu = null;
								state.drawerSearchText = "";
								state.isPopupOpen = false;
							}
						} else {
							// When reopening, restore the menu state if we have saved state
							if (state.activeMenu) {
								state.drawerActiveMenu = state.activeMenu;
								state.drawerSearchText = state.searchText;
								state.isPopupOpen = true;
							}
						}
					});
				},
			})),
			{
				name: "drawer-storage",
				partialize: (state) => ({
					activeMenu: state.activeMenu,
					searchText: state.searchText,
					isPopupOpen: state.isPopupOpen,
					drawerActiveMenu: state.drawerActiveMenu,
					drawerSearchText: state.drawerSearchText,
					isExtensionClosed: state.isExtensionClosed,

					// Add overlay and related settings to persist
					overlayEnabled: state.overlayEnabled,
					pageinteraction: state.pageinteraction,
					progress: state.progress,
					selectedOption: state.selectedOption,
					ProgressColor: state.ProgressColor,
					dismiss: state.dismiss,
					dismissData: state.dismissData,

					//banner-related state to persist
					bannerPopup: state.bannerPopup,
					selectedTemplate: state.selectedTemplate,
					selectedTemplateTour: state.selectedTemplateTour,
					rtesContainer: state.rtesContainer,
					bannerButtonSelected: state.bannerButtonSelected,
					buttonsContainer: state.buttonsContainer
				}),
			}
		),
		{ name: "DrawerStore" }
	)
);

export default useDrawerStore;
