{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\RTEsection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, forwardRef, useMemo } from \"react\";\nimport { Box, Tooltip, IconButton } from \"@mui/material\";\nimport JoditEditor from \"jodit-react\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport { copyicon, deleteicon, editicon } from \"../../../assets/icons/icons\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RTEsection = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  textBoxRef,\n  isBanner,\n  handleDeleteRTESection,\n  index,\n  guidePopUpRef,\n  onClone,\n  isCloneDisabled\n}, ref) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    rtesContainer,\n    updateRTEContainer,\n    setIsUnSavedChanges,\n    cloneRTEContainer,\n    clearRteDetails,\n    selectedTemplate,\n    selectedTemplateTour,\n    announcementGuideMetaData,\n    toolTipGuideMetaData,\n    handleAnnouncementRTEValue,\n    handleTooltipRTEValue,\n    createWithAI,\n    currentStep,\n    ensureAnnouncementRTEContainer\n  } = useDrawerStore();\n\n  // Individual state management for each RTE\n  const [editingRTEId, setEditingRTEId] = useState(null);\n  const [toolbarVisibleRTEId, setToolbarVisibleRTEId] = useState(null);\n  const contentRef = useRef(\"\");\n\n  // State to track content for dynamic icon positioning\n  const [contentStates, setContentStates] = useState(new Map());\n\n  // Map to store individual refs for each RTE\n  const editorRefs = useRef(new Map());\n  const containerRefs = useRef(new Map());\n\n  // Helper function to get or create editor ref for specific RTE\n  const getEditorRef = rteId => {\n    if (!editorRefs.current.has(rteId)) {\n      editorRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return editorRefs.current.get(rteId);\n  };\n\n  // Helper function to get or create container ref for specific RTE\n  const getContainerRef = rteId => {\n    if (!containerRefs.current.has(rteId)) {\n      containerRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return containerRefs.current.get(rteId);\n  };\n\n  // Handle clicks outside the editor - now works with individual RTEs\n  useEffect(() => {\n    const handleClickOutside = event => {\n      var _document$querySelect, _document$querySelect2, _document$querySelect3, _document$querySelect4;\n      if (!editingRTEId) return; // No RTE is currently being edited\n\n      const isInsideJoditPopupContent = event.target.closest(\".jodit-popup__content\") !== null;\n      const isInsideAltTextPopup = event.target.closest(\".jodit-ui-input\") !== null;\n      const isInsidePopup = (_document$querySelect = document.querySelector(\".jodit-popup\")) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.contains(event.target);\n      const isInsideJoditPopup = (_document$querySelect2 = document.querySelector(\".jodit-wysiwyg\")) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.contains(event.target);\n      const isInsideWorkplacePopup = isInsideJoditPopup || ((_document$querySelect3 = document.querySelector(\".jodit-dialog__panel\")) === null || _document$querySelect3 === void 0 ? void 0 : _document$querySelect3.contains(event.target));\n      const isSelectionMarker = event.target.id.startsWith(\"jodit-selection_marker_\");\n      const isLinkPopup = (_document$querySelect4 = document.querySelector(\".jodit-ui-input__input\")) === null || _document$querySelect4 === void 0 ? void 0 : _document$querySelect4.contains(event.target);\n      const isInsideToolbarButton = event.target.closest(\".jodit-toolbar-button__button\") !== null;\n      const isInsertButton = event.target.closest(\"button[aria-pressed='false']\") !== null;\n\n      // Get the container ref for the currently editing RTE\n      const currentContainerRef = getContainerRef(editingRTEId);\n\n      // Check if the target is inside the currently editing RTE or related elements\n      if (currentContainerRef !== null && currentContainerRef !== void 0 && currentContainerRef.current && !currentContainerRef.current.contains(event.target) &&\n      // Click outside the current editor container\n      !isInsidePopup &&\n      // Click outside the popup\n      !isInsideJoditPopup &&\n      // Click outside the WYSIWYG editor\n      !isInsideWorkplacePopup &&\n      // Click outside the workplace popup\n      !isSelectionMarker &&\n      // Click outside selection markers\n      !isLinkPopup &&\n      // Click outside link input popup\n      !isInsideToolbarButton &&\n      // Click outside the toolbar button\n      !isInsertButton && !isInsideJoditPopupContent && !isInsideAltTextPopup) {\n        setEditingRTEId(null); // Close the currently editing RTE\n        setToolbarVisibleRTEId(null); // Also hide toolbar when clicking outside\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n  }, [editingRTEId]);\n  useEffect(() => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        setTimeout(() => {\n          //(editorRef.current as any).editor.focus();\n        }, 50);\n      }\n    }\n  }, [editingRTEId]);\n\n  // Helper function to check if content is empty (only whitespace, <p></p>, <br>, etc.)\n  const isContentEmpty = content => {\n    if (!content) return true;\n    // Remove HTML tags and check if there's actual text content\n    const textContent = content.replace(/<[^>]*>/g, '').trim();\n    return textContent.length === 0;\n  };\n\n  // Helper function to check if content is scrollable\n  const isContentScrollable = containerId => {\n    const containerRef = getContainerRef(containerId);\n    if (containerRef !== null && containerRef !== void 0 && containerRef.current) {\n      const workplace = containerRef.current.querySelector('.jodit-workplace');\n      if (workplace) {\n        return workplace.scrollHeight > workplace.clientHeight;\n      }\n    }\n    return false;\n  };\n\n  // Update content state for dynamic icon positioning\n  const updateContentState = (containerId, content) => {\n    const isEmpty = isContentEmpty(content);\n    const isScrollable = isContentScrollable(containerId);\n    setContentStates(prev => {\n      const newMap = new Map(prev);\n      newMap.set(containerId, {\n        isEmpty,\n        isScrollable\n      });\n      return newMap;\n    });\n  };\n  const handleUpdate = (newContent, rteId, containerId) => {\n    contentRef.current = newContent;\n\n    // Check if this is an AI-created guide\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    const isAITour = createWithAI && selectedTemplate === \"Tour\";\n    const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n    const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n    const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n    console.log(\"RTEsection handleUpdate:\", {\n      createWithAI,\n      selectedTemplate,\n      selectedTemplateTour,\n      isAIAnnouncement,\n      isAITour,\n      isTourBanner,\n      containerId,\n      newContent: newContent.substring(0, 50) + \"...\"\n    });\n    if (isAIAnnouncement) {\n      const currentStepIndex = currentStep - 1;\n      if (isTourAnnouncement) {\n        var _toolTipGuideMetaData, _toolTipGuideMetaData2;\n        // For Tour+Announcement, use toolTipGuideMetaData\n        const tooltipContainer = (_toolTipGuideMetaData = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.containers) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.find(container => container.id === containerId && container.type === \"rte\");\n        if (tooltipContainer) {\n          // Use the tooltip-specific handler for tour announcements\n          handleTooltipRTEValue(containerId, newContent);\n        }\n      } else {\n        var _announcementGuideMet, _announcementGuideMet2;\n        // For pure Announcements, use announcementGuideMetaData\n        const announcementContainer = (_announcementGuideMet = announcementGuideMetaData[currentStepIndex]) === null || _announcementGuideMet === void 0 ? void 0 : (_announcementGuideMet2 = _announcementGuideMet.containers) === null || _announcementGuideMet2 === void 0 ? void 0 : _announcementGuideMet2.find(container => container.id === containerId && container.type === \"rte\");\n        if (announcementContainer) {\n          // Use the announcement-specific handler\n          handleAnnouncementRTEValue(containerId, newContent);\n        }\n      }\n    } else if (isAITour && (isTourBanner || isTourTooltip)) {\n      var _toolTipGuideMetaData3, _toolTipGuideMetaData4;\n      // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\n      const currentStepIndex = currentStep - 1;\n      const tooltipContainer = (_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : (_toolTipGuideMetaData4 = _toolTipGuideMetaData3.containers) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.find(container => container.id === containerId && container.type === \"rte\");\n      if (tooltipContainer) {\n        // Use the tooltip-specific handler for all tour step types\n        handleTooltipRTEValue(containerId, newContent);\n        console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);\n      } else {\n        var _toolTipGuideMetaData5, _toolTipGuideMetaData6;\n        console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {\n          currentStepIndex,\n          containerId,\n          availableContainers: (_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData5 === void 0 ? void 0 : (_toolTipGuideMetaData6 = _toolTipGuideMetaData5.containers) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.map(c => ({\n            id: c.id,\n            type: c.type\n          }))\n        });\n      }\n    } else {\n      // For non-AI content or other cases, use the regular RTE container system\n      updateRTEContainer(containerId, rteId, newContent);\n      console.log(\"Used updateRTEContainer for non-AI content\");\n    }\n    setIsUnSavedChanges(true);\n\n    // Update content state for dynamic icon positioning\n    updateContentState(containerId, newContent);\n  };\n  const handleCloneContainer = containerId => {\n    // Check if cloning is disabled due to section limits\n    if (isCloneDisabled) {\n      return; // Don't clone if limit is reached\n    }\n\n    // Call the clone function from the store\n    cloneRTEContainer(containerId);\n\n    // Call the onClone callback if provided\n    if (onClone) {\n      onClone();\n    }\n  };\n  const handleDeleteSection = (containerId, rteId) => {\n    // Check if this is an AI-created announcement\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    if (isAIAnnouncement) {\n      // For AI announcements, we need to remove from announcementGuideMetaData\n      // This would require a new function in the store, for now just call the existing one\n      clearRteDetails(containerId, rteId);\n    } else {\n      // For banners and non-AI content, use the regular clear function\n      clearRteDetails(containerId, rteId);\n    }\n\n    // Call the handleDeleteRTESection callback to update section counts\n    handleDeleteRTESection(index);\n  };\n  const handlePaste = event => {\n    event.preventDefault();\n    const clipboardData = event.clipboardData;\n    const pastedText = clipboardData.getData(\"text/plain\");\n    const pastedHtml = clipboardData.getData(\"text/html\");\n    if (pastedHtml) {\n      const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\n      if (isRTEContent) {\n        insertContent(pastedHtml);\n      } else {\n        insertContent(pastedHtml);\n      }\n    } else {\n      insertContent(pastedText);\n    }\n  };\n  const insertContent = content => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        const editor = editorRef.current.editor;\n        editor.selection.insertHTML(content);\n      }\n    }\n  };\n  const toggleToolbar = rteId => {\n    if (toolbarVisibleRTEId === rteId) {\n      setToolbarVisibleRTEId(null);\n    } else {\n      setToolbarVisibleRTEId(rteId);\n      // Don't set editing state, just show toolbar\n    }\n  };\n  const [isRtlDirection, setIsRtlDirection] = useState(false);\n  useEffect(() => {\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\n  }, []);\n  const config = useMemo(() => ({\n    readonly: false,\n    // all options from https://xdsoft.net/jodit/docs/,\n    direction: isRtlDirection ? 'rtl' : 'ltr',\n    // Jodit uses 'direction' not just 'rtl'\n    language: 'en',\n    // Optional: change language as well\n    toolbarSticky: false,\n    toolbarAdaptive: false,\n    // Hide toolbar by default, will be controlled by toolbarVisibleRTEId state\n    toolbar: toolbarVisibleRTEId !== null,\n    // Enhanced height and scrolling behavior - consistent for all templates\n    height: 'auto',\n    minHeight: 28,\n    // 1-line minimum for all templates\n    maxHeight: 112,\n    // 4-line maximum for all templates\n    buttons: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link', {\n      name: 'more',\n      iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\n      list: ['source', 'image', 'video', 'table', 'align', 'undo', 'redo', '|', 'hr', 'eraser', 'copyformat', 'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|', 'outdent', 'indent', 'paragraph']\n    }],\n    autofocus: true,\n    showCharsCounter: false,\n    showWordsCounter: false,\n    showXPathInStatusbar: false,\n    statusbar: false,\n    pastePlain: true,\n    askBeforePasteHTML: false,\n    askBeforePasteFromWord: false,\n    // Fix dialog positioning by setting popup root to document body\n    popupRoot: document.body,\n    // Fix popup positioning issues\n    zIndex: 100000,\n    globalFullSize: false,\n    // Fix link dialog positioning\n    link: {\n      followOnDblClick: false,\n      processVideoLink: true,\n      processPastedLink: true,\n      openInNewTabCheckbox: true,\n      noFollowCheckbox: false,\n      modeClassName: 'input'\n    },\n    // Dialog configuration\n    dialog: {\n      zIndex: 100001\n    },\n    cursorAfterAutofocus: 'end',\n    events: {\n      onPaste: handlePaste // Attach custom onPaste handler\n    },\n    controls: {\n      font: {\n        list: {\n          \"Poppins, sans-serif\": \"Poppins\",\n          \"Roboto, sans-serif\": \"Roboto\",\n          \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\n          \"Open Sans, sans-serif\": \"Open Sans\",\n          \"Calibri, sans-serif\": \"Calibri\",\n          \"Century Gothic, sans-serif\": \"Century Gothic\"\n        }\n      }\n    }\n  }), [isRtlDirection, toolbarVisibleRTEId]);\n\n  // Determine which containers to use based on guide type\n  const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n  const isAITour = createWithAI && selectedTemplate === \"Tour\";\n  const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n  const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n  const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n  const currentStepIndex = currentStep - 1;\n  let containersToRender = [];\n  if (isAIAnnouncement && !isTourAnnouncement) {\n    // For pure AI announcements (not in tours), use announcementGuideMetaData\n    containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\n  } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n    var _toolTipGuideMetaData7;\n    // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\n    if ((_toolTipGuideMetaData7 = toolTipGuideMetaData[currentStepIndex]) !== null && _toolTipGuideMetaData7 !== void 0 && _toolTipGuideMetaData7.containers) {\n      containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\n      console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {\n        totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,\n        rteContainers: containersToRender.length,\n        rteData: containersToRender.map(c => ({\n          id: c.id,\n          rteBoxValue: c.rteBoxValue\n        }))\n      });\n    } else {\n      console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);\n      containersToRender = [];\n    }\n  } else {\n    // For non-AI content, use rtesContainer\n    containersToRender = rtesContainer;\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: containersToRender.map(item => {\n      let rteText = \"\";\n      let rteId = \"\";\n      let id = \"\";\n      if (isAIAnnouncement && !isTourAnnouncement || isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\n        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\n        rteText = item.rteBoxValue || \"\";\n        rteId = item.id;\n        id = item.id;\n      } else {\n        var _item$rtes, _item$rtes$, _item$rtes2, _item$rtes2$;\n        // For non-AI content, get data from rtesContainer\n        rteText = ((_item$rtes = item.rtes) === null || _item$rtes === void 0 ? void 0 : (_item$rtes$ = _item$rtes[0]) === null || _item$rtes$ === void 0 ? void 0 : _item$rtes$.text) || \"\";\n        rteId = (_item$rtes2 = item.rtes) === null || _item$rtes2 === void 0 ? void 0 : (_item$rtes2$ = _item$rtes2[0]) === null || _item$rtes2$ === void 0 ? void 0 : _item$rtes2$.id;\n        id = item.id;\n      }\n      if (!id) return null;\n      const currentContainerRef = getContainerRef(id);\n      const currentEditorRef = getEditorRef(id);\n\n      // Get content state for dynamic icon positioning\n      const contentState = contentStates.get(id) || {\n        isEmpty: isContentEmpty(rteText),\n        isScrollable: false\n      };\n      return /*#__PURE__*/_jsxDEV(Box, {\n        ref: currentContainerRef,\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          position: \"relative\",\n          \"& .jodit-add-new-line\": {\n            display: \"none !important\"\n          },\n          \"& .jodit-status-bar-link\": {\n            display: \"none !important\"\n          },\n          \"& .jodit-editor\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-editor span\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-toolbar-button button\": {\n            minWidth: \"29px !important\"\n          },\n          \".jodit-react-container\": {\n            width: selectedTemplate === \"Banner\" ? \"100%\" : \"100%\",\n            whiteSpace: \"pre-wrap\",\n            wordBreak: \"break-word\"\n          },\n          \".jodit-workplace\": {\n            minHeight: \"28px !important\",\n            // 1-line minimum for all templates\n            maxHeight: \"112px !important\",\n            // 4-line maximum for all templates\n            overflow: \"auto !important\",\n            // Consistent scrolling for all templates\n            lineHeight: \"1.4 !important\"\n          },\n          \".jodit-container\": {\n            minHeight: \"28px !important\",\n            // Consistent minimum height for all templates\n            border: \"none !important\"\n          },\n          \".jodit-toolbar__box\": {\n            display: \"flex !important\",\n            justifyContent: \"center !important\",\n            height: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"32px !important\" : null,\n            maxHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"32px !important\" : null\n          },\n          // Fix Jodit dialog positioning - target correct classes\n          \".jodit.jodit-dialog\": {\n            position: \"fixed !important\",\n            zIndex: \"100001 !important\",\n            top: \"50% !important\",\n            left: \"50% !important\",\n            transform: \"translate(-50%, -50%) !important\"\n          },\n          \".jodit-dialog .jodit-dialog__panel\": {\n            position: \"relative !important\",\n            top: \"auto !important\",\n            left: \"auto !important\",\n            transform: \"none !important\",\n            maxWidth: \"400px !important\",\n            background: \"white !important\",\n            border: \"1px solid #ccc !important\",\n            borderRadius: \"4px !important\",\n            boxShadow: \"0 4px 12px rgba(0,0,0,0.15) !important\"\n          },\n          // Fix for link dialog specifically\n          \".jodit-dialog_alert\": {\n            position: \"fixed !important\",\n            zIndex: \"100001 !important\",\n            top: \"50% !important\",\n            left: \"50% !important\",\n            transform: \"translate(-50%, -50%) !important\"\n          },\n          // Enhanced scrollbar styling for consistent behavior\n          \".jodit-workplace::-webkit-scrollbar\": {\n            width: \"6px !important\"\n          },\n          \".jodit-workplace::-webkit-scrollbar-track\": {\n            background: \"#f1f1f1 !important\",\n            borderRadius: \"3px !important\"\n          },\n          \".jodit-workplace::-webkit-scrollbar-thumb\": {\n            background: \"#c1c1c1 !important\",\n            borderRadius: \"3px !important\"\n          },\n          \".jodit-workplace::-webkit-scrollbar-thumb:hover\": {\n            background: \"#a8a8a8 !important\"\n          },\n          // Ensure proper line height and text styling\n          \".jodit-wysiwyg\": {\n            lineHeight: \"1.4 !important\",\n            padding: \"8px !important\"\n          },\n          \".jodit-wysiwyg p\": {\n            margin: \"0 0 4px 0 !important\",\n            lineHeight: \"1.4 !important\"\n          }\n        },\n        className: \"qadpt-rte\",\n        children: selectedTemplate === \"Announcement\" || selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Announcement\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\" ? /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => handleCloneContainer(item.id),\n              disabled: isCloneDisabled,\n              title: isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\"),\n              sx: {\n                \"&:hover\": {\n                  backgroundColor: \"transparent !important\"\n                },\n                svg: {\n                  height: \"24px\",\n                  path: {\n                    fill: \"var(--primarycolor)\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: copyicon\n                },\n                style: {\n                  opacity: isCloneDisabled ? 0.5 : 1,\n                  height: '24px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 45\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => handleDeleteSection(item.id, rteId),\n              sx: {\n                \"&:hover\": {\n                  backgroundColor: \"transparent !important\"\n                },\n                svg: {\n                  path: {\n                    fill: \"var(--primarycolor)\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: deleteicon\n                },\n                style: {\n                  height: '24px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 45\n            }, this)]\n          }, void 0, true),\n          placement: \"top\",\n          slotProps: {\n            tooltip: {\n              sx: {\n                backgroundColor: 'white',\n                color: 'black',\n                borderRadius: '4px',\n                padding: '0px 4px',\n                border: \"1px dashed var(--primarycolor)\",\n                marginBottom: '30px !important'\n              }\n            }\n          },\n          PopperProps: {\n            modifiers: [{\n              name: 'preventOverflow',\n              options: {\n                boundary: 'viewport'\n              }\n            }, {\n              name: 'flip',\n              options: {\n                enabled: true\n              }\n            }]\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: \"100%\",\n              position: \"relative\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(JoditEditor, {\n              ref: currentEditorRef,\n              value: rteText,\n              config: config,\n              onChange: newContent => handleUpdate(newContent, rteId, id)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: e => {\n                e.stopPropagation();\n                toggleToolbar(id);\n              },\n              sx: {\n                position: \"absolute\",\n                // Dynamic positioning based on content state\n                bottom: contentState.isEmpty ? \"50%\" : contentState.isScrollable ? \"8px\" : \"2px\",\n                right: contentState.isEmpty ? \"auto\" : \"2px\",\n                left: contentState.isEmpty ? \"calc(100% - 32px)\" : \"auto\",\n                transform: contentState.isEmpty ? \"translateY(50%)\" : \"none\",\n                width: \"24px\",\n                height: \"24px\",\n                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n                zIndex: contentState.isScrollable ? 1001 : 1000,\n                // Higher z-index when scrollable to stay on top\n                \"&:hover\": {\n                  backgroundColor: \"rgba(255, 255, 255, 1)\"\n                },\n                \"& svg\": {\n                  width: \"16px\",\n                  height: \"16px\"\n                }\n              },\n              title: translate(\"Toggle Toolbar\"),\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: editicon\n                },\n                style: {\n                  height: '16px',\n                  width: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 37\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 33\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: \"100%\",\n            position: \"relative\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(JoditEditor, {\n            ref: currentEditorRef,\n            value: rteText,\n            config: config,\n            onChange: newContent => handleUpdate(newContent, rteId, id)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 37\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: e => {\n              e.stopPropagation();\n              toggleToolbar(id);\n            },\n            sx: {\n              position: \"absolute\",\n              // Dynamic positioning based on content state\n              bottom: contentState.isEmpty ? \"50%\" : contentState.isScrollable ? \"8px\" : \"2px\",\n              right: contentState.isEmpty ? \"auto\" : \"2px\",\n              left: contentState.isEmpty ? \"calc(100% - 32px)\" : \"auto\",\n              transform: contentState.isEmpty ? \"translateY(50%)\" : \"none\",\n              width: \"24px\",\n              height: \"24px\",\n              backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n              zIndex: contentState.isScrollable ? 1001 : 1000,\n              // Higher z-index when scrollable to stay on top\n              \"&:hover\": {\n                backgroundColor: \"rgba(255, 255, 255, 1)\"\n              },\n              \"& svg\": {\n                width: \"16px\",\n                height: \"16px\"\n              }\n            },\n            title: translate(\"Toggle Toolbar\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: editicon\n              },\n              style: {\n                height: '16px',\n                width: '16px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 689,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 654,\n          columnNumber: 33\n        }, this)\n      }, id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 25\n      }, this);\n    })\n  }, void 0, false);\n}, \"XLviSwk6hPj+kbeOo63kCMM92BE=\", false, function () {\n  return [useTranslation, useDrawerStore];\n})), \"XLviSwk6hPj+kbeOo63kCMM92BE=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c2 = RTEsection;\nexport default RTEsection;\nvar _c, _c2;\n$RefreshReg$(_c, \"RTEsection$forwardRef\");\n$RefreshReg$(_c2, \"RTEsection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "forwardRef", "useMemo", "Box", "<PERSON><PERSON><PERSON>", "IconButton", "JoditEditor", "useDrawerStore", "copyicon", "deleteicon", "editicon", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RTEsection", "_s", "_c", "textBoxRef", "isBanner", "handleDeleteRTESection", "index", "guidePopUpRef", "onClone", "isCloneDisabled", "ref", "t", "translate", "rtesContainer", "updateRTEContainer", "setIsUnSavedChanges", "cloneRTEContainer", "clearRteDetails", "selectedTemplate", "selectedTemplateTour", "announcementGuideMetaData", "toolTipGuideMetaData", "handleAnnouncementRTEValue", "handleTooltipRTEValue", "createWithAI", "currentStep", "ensureAnnouncementRTEContainer", "editingRTEId", "setEditingRTEId", "toolbarVisibleRTEId", "setToolbarVisibleRTEId", "contentRef", "contentStates", "setContentStates", "Map", "editor<PERSON><PERSON><PERSON>", "containerRefs", "getEditorRef", "rteId", "current", "has", "set", "createRef", "get", "getContainerRef", "handleClickOutside", "event", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsideJoditPopupContent", "target", "closest", "isInsideAltTextPopup", "isInsidePopup", "document", "querySelector", "contains", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "id", "startsWith", "isLinkPopup", "isInsideToolbarButton", "isInsertButton", "currentContainerRef", "addEventListener", "removeEventListener", "editor<PERSON><PERSON>", "setTimeout", "isContentEmpty", "content", "textContent", "replace", "trim", "length", "isContentScrollable", "containerId", "containerRef", "workplace", "scrollHeight", "clientHeight", "updateContentState", "isEmpty", "isScrollable", "prev", "newMap", "handleUpdate", "newContent", "isAIAnnouncement", "isAITour", "isTourAnnouncement", "isTourBanner", "isTourTooltip", "console", "log", "substring", "currentStepIndex", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "tooltipContainer", "containers", "find", "container", "type", "_announcementGuideMet", "_announcementGuideMet2", "announcementC<PERSON>r", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "warn", "availableContainers", "map", "c", "handleCloneContainer", "handleDeleteSection", "handlePaste", "preventDefault", "clipboardData", "pastedText", "getData", "pastedHtml", "isRTEContent", "includes", "insertContent", "editor", "selection", "insertHTML", "toggleToolbar", "isRtlDirection", "setIsRtlDirection", "dir", "body", "getAttribute", "toLowerCase", "config", "readonly", "direction", "language", "toolbarSticky", "toolbarAdaptive", "toolbar", "height", "minHeight", "maxHeight", "buttons", "name", "iconURL", "list", "autofocus", "showCharsCounter", "showWordsCounter", "showXPathInStatusbar", "statusbar", "paste<PERSON>lain", "askBeforePasteHTML", "askBeforePasteFromWord", "popupRoot", "zIndex", "globalFullSize", "link", "followOnDblClick", "processVideoLink", "processPastedLink", "openInNewTabCheckbox", "noFollowCheckbox", "modeClassName", "dialog", "cursorAfterAutofocus", "events", "onPaste", "controls", "font", "containersToRender", "_toolTipGuideMetaData7", "filter", "totalContainers", "rteContainers", "rteData", "rteBoxValue", "children", "item", "rteText", "_item$rtes", "_item$rtes$", "_item$rtes2", "_item$rtes2$", "rtes", "text", "currentEditorRef", "contentState", "sx", "display", "alignItems", "position", "fontFamily", "min<PERSON><PERSON><PERSON>", "width", "whiteSpace", "wordBreak", "overflow", "lineHeight", "border", "justifyContent", "top", "left", "transform", "max<PERSON><PERSON><PERSON>", "background", "borderRadius", "boxShadow", "padding", "margin", "className", "title", "size", "onClick", "disabled", "backgroundColor", "svg", "path", "fill", "dangerouslySetInnerHTML", "__html", "style", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placement", "slotProps", "tooltip", "color", "marginBottom", "PopperProps", "modifiers", "options", "boundary", "enabled", "value", "onChange", "e", "stopPropagation", "bottom", "right", "_c2", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/RTEsection.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, forwardRef,useMemo } from \"react\";\r\nimport { Box, TextField, Tooltip, IconButton } from \"@mui/material\";\r\nimport JoditEditor from \"jodit-react\";\r\nimport useDrawerStore from \"../../../store/drawerStore\";\r\nimport { copyicon, deleteicon, editicon } from \"../../../assets/icons/icons\";\r\nimport { selectedtemp } from \"../../drawer/Drawer\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface RTEsectionProps {\r\n    textBoxRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    guidePopUpRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    isBanner: boolean;\r\n    handleDeleteRTESection: (params: number) => void;\r\n    index: number;\r\n    onClone?: () => void;\r\n    isCloneDisabled?: boolean;\r\n}\r\n\r\nconst RTEsection: React.FC<RTEsectionProps> = forwardRef(\r\n    ({ textBoxRef, isBanner, handleDeleteRTESection, index, guidePopUpRef, onClone, isCloneDisabled }, ref) => {\r\n        const { t: translate } = useTranslation();\r\n        const {\r\n            rtesContainer,\r\n            updateRTEContainer,\r\n            setIsUnSavedChanges,\r\n            cloneRTEContainer,\r\n            clearRteDetails,\r\n            selectedTemplate,\r\n            selectedTemplateTour,\r\n            announcementGuideMetaData,\r\n            toolTipGuideMetaData,\r\n            handleAnnouncementRTEValue,\r\n            handleTooltipRTEValue,\r\n            createWithAI,\r\n            currentStep,\r\n            ensureAnnouncementRTEContainer\r\n        } = useDrawerStore();\r\n\r\n        // Individual state management for each RTE\r\n        const [editingRTEId, setEditingRTEId] = useState<string | null>(null);\r\n        const [toolbarVisibleRTEId, setToolbarVisibleRTEId] = useState<string | null>(null);\r\n        const contentRef = useRef<string>(\"\");\r\n\r\n        // State to track content for dynamic icon positioning\r\n        const [contentStates, setContentStates] = useState<Map<string, { isEmpty: boolean, isScrollable: boolean }>>(new Map());\r\n\r\n        // Map to store individual refs for each RTE\r\n        const editorRefs = useRef<Map<string, React.RefObject<any>>>(new Map());\r\n        const containerRefs = useRef<Map<string, React.RefObject<HTMLDivElement>>>(new Map());\r\n\r\n        // Helper function to get or create editor ref for specific RTE\r\n        const getEditorRef = (rteId: string) => {\r\n            if (!editorRefs.current.has(rteId)) {\r\n                editorRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return editorRefs.current.get(rteId);\r\n        };\r\n\r\n        // Helper function to get or create container ref for specific RTE\r\n        const getContainerRef = (rteId: string) => {\r\n            if (!containerRefs.current.has(rteId)) {\r\n                containerRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return containerRefs.current.get(rteId);\r\n        };\r\n\r\n        // Handle clicks outside the editor - now works with individual RTEs\r\n        useEffect(() => {\r\n            const handleClickOutside = (event: MouseEvent) => {\r\n                if (!editingRTEId) return; // No RTE is currently being edited\r\n\r\n                const isInsideJoditPopupContent = (event.target as HTMLElement).closest(\".jodit-popup__content\") !== null;\r\n                const isInsideAltTextPopup = (event.target as HTMLElement).closest(\".jodit-ui-input\") !== null;\r\n                const isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n                const isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n                const isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\r\n                const isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n                const isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n                const isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n                const isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\r\n                // Get the container ref for the currently editing RTE\r\n                const currentContainerRef = getContainerRef(editingRTEId);\r\n\r\n                // Check if the target is inside the currently editing RTE or related elements\r\n                if (\r\n                    currentContainerRef?.current &&\r\n                    !currentContainerRef.current.contains(event.target as Node) && // Click outside the current editor container\r\n                    !isInsidePopup && // Click outside the popup\r\n                    !isInsideJoditPopup && // Click outside the WYSIWYG editor\r\n                    !isInsideWorkplacePopup && // Click outside the workplace popup\r\n                    !isSelectionMarker && // Click outside selection markers\r\n                    !isLinkPopup && // Click outside link input popup\r\n                    !isInsideToolbarButton && // Click outside the toolbar button\r\n                    !isInsertButton &&\r\n                    !isInsideJoditPopupContent &&\r\n                    !isInsideAltTextPopup\r\n                ) {\r\n                    setEditingRTEId(null); // Close the currently editing RTE\r\n                    setToolbarVisibleRTEId(null); // Also hide toolbar when clicking outside\r\n                }\r\n            };\r\n\r\n            document.addEventListener(\"mousedown\", handleClickOutside);\r\n            return () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n        }, [editingRTEId]);\r\n\r\n        useEffect(() => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    setTimeout(() => {\r\n                        //(editorRef.current as any).editor.focus();\r\n                    }, 50);\r\n                }\r\n            }\r\n        }, [editingRTEId]);\r\n\r\n        // Helper function to check if content is empty (only whitespace, <p></p>, <br>, etc.)\r\n        const isContentEmpty = (content: string): boolean => {\r\n            if (!content) return true;\r\n            // Remove HTML tags and check if there's actual text content\r\n            const textContent = content.replace(/<[^>]*>/g, '').trim();\r\n            return textContent.length === 0;\r\n        };\r\n\r\n        // Helper function to check if content is scrollable\r\n        const isContentScrollable = (containerId: string): boolean => {\r\n            const containerRef = getContainerRef(containerId);\r\n            if (containerRef?.current) {\r\n                const workplace = containerRef.current.querySelector('.jodit-workplace');\r\n                if (workplace) {\r\n                    return workplace.scrollHeight > workplace.clientHeight;\r\n                }\r\n            }\r\n            return false;\r\n        };\r\n\r\n        // Update content state for dynamic icon positioning\r\n        const updateContentState = (containerId: string, content: string) => {\r\n            const isEmpty = isContentEmpty(content);\r\n            const isScrollable = isContentScrollable(containerId);\r\n\r\n            setContentStates(prev => {\r\n                const newMap = new Map(prev);\r\n                newMap.set(containerId, { isEmpty, isScrollable });\r\n                return newMap;\r\n            });\r\n        };\r\n\r\n        const handleUpdate = (newContent: string, rteId: string, containerId: string) => {\r\n            contentRef.current = newContent;\r\n\r\n            // Check if this is an AI-created guide\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n            const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n            const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n            const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n            const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n\r\n            console.log(\"RTEsection handleUpdate:\", {\r\n                createWithAI,\r\n                selectedTemplate,\r\n                selectedTemplateTour,\r\n                isAIAnnouncement,\r\n                isAITour,\r\n                isTourBanner,\r\n                containerId,\r\n                newContent: newContent.substring(0, 50) + \"...\"\r\n            });\r\n\r\n            if (isAIAnnouncement) {\r\n                const currentStepIndex = currentStep - 1;\r\n\r\n                if (isTourAnnouncement) {\r\n                    // For Tour+Announcement, use toolTipGuideMetaData\r\n                    const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (tooltipContainer) {\r\n                        // Use the tooltip-specific handler for tour announcements\r\n                        handleTooltipRTEValue(containerId, newContent);\r\n                    }\r\n                } else {\r\n                    // For pure Announcements, use announcementGuideMetaData\r\n                    const announcementContainer = announcementGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (announcementContainer) {\r\n                        // Use the announcement-specific handler\r\n                        handleAnnouncementRTEValue(containerId, newContent);\r\n                    }\r\n                }\r\n            } else if (isAITour && (isTourBanner || isTourTooltip)) {\r\n                // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\r\n                const currentStepIndex = currentStep - 1;\r\n                const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                    (container: any) => container.id === containerId && container.type === \"rte\"\r\n                );\r\n\r\n                if (tooltipContainer) {\r\n                    // Use the tooltip-specific handler for all tour step types\r\n                    handleTooltipRTEValue(containerId, newContent);\r\n                    console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);\r\n                } else {\r\n                    console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {\r\n                        currentStepIndex,\r\n                        containerId,\r\n                        availableContainers: toolTipGuideMetaData[currentStepIndex]?.containers?.map(c => ({ id: c.id, type: c.type }))\r\n                    });\r\n                }\r\n            } else {\r\n                // For non-AI content or other cases, use the regular RTE container system\r\n                updateRTEContainer(containerId, rteId, newContent);\r\n                console.log(\"Used updateRTEContainer for non-AI content\");\r\n            }\r\n\r\n            setIsUnSavedChanges(true);\r\n\r\n            // Update content state for dynamic icon positioning\r\n            updateContentState(containerId, newContent);\r\n        };\r\n        const handleCloneContainer = (containerId: string) => {\r\n            // Check if cloning is disabled due to section limits\r\n            if (isCloneDisabled) {\r\n                return; // Don't clone if limit is reached\r\n            }\r\n\r\n            // Call the clone function from the store\r\n            cloneRTEContainer(containerId);\r\n\r\n            // Call the onClone callback if provided\r\n            if (onClone) {\r\n                onClone();\r\n            }\r\n        };\r\n        const handleDeleteSection = (containerId: string, rteId:string) => {\r\n            // Check if this is an AI-created announcement\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n\r\n            if (isAIAnnouncement) {\r\n                // For AI announcements, we need to remove from announcementGuideMetaData\r\n                // This would require a new function in the store, for now just call the existing one\r\n                clearRteDetails(containerId, rteId);\r\n            } else {\r\n                // For banners and non-AI content, use the regular clear function\r\n                clearRteDetails(containerId, rteId);\r\n            }\r\n\r\n            // Call the handleDeleteRTESection callback to update section counts\r\n            handleDeleteRTESection(index);\r\n        };\r\n        const handlePaste = (event: React.ClipboardEvent<HTMLDivElement>) => {\r\n            event.preventDefault();\r\n\r\n            const clipboardData = event.clipboardData;\r\n            const pastedText = clipboardData.getData(\"text/plain\");\r\n            const pastedHtml = clipboardData.getData(\"text/html\");\r\n\r\n            if (pastedHtml) {\r\n                const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\r\n                if (isRTEContent) {\r\n                    insertContent(pastedHtml);\r\n                } else {\r\n                    insertContent(pastedHtml);\r\n                }\r\n            } else {\r\n                insertContent(pastedText);\r\n            }\r\n        };\r\n\r\n\r\n        const insertContent = (content: string) => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    const editor = (editorRef.current as any).editor;\r\n                    editor.selection.insertHTML(content);\r\n                }\r\n            }\r\n        };\r\n\r\n        const toggleToolbar = (rteId: string) => {\r\n            if (toolbarVisibleRTEId === rteId) {\r\n                setToolbarVisibleRTEId(null);\r\n            } else {\r\n                setToolbarVisibleRTEId(rteId);\r\n                // Don't set editing state, just show toolbar\r\n            }\r\n        };\r\n        const [isRtlDirection, setIsRtlDirection] = useState<boolean>(false);\r\n        useEffect(() => {\r\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\r\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\r\n}, []);\r\n    const config = useMemo(\r\n        (): any => ({\r\n            readonly: false, // all options from https://xdsoft.net/jodit/docs/,\r\n            direction: isRtlDirection ? 'rtl' as const : 'ltr' as const,\r\n\r\n// Jodit uses 'direction' not just 'rtl'\r\n        language:  'en', // Optional: change language as well\r\n            toolbarSticky: false,\r\n            toolbarAdaptive: false,\r\n            // Hide toolbar by default, will be controlled by toolbarVisibleRTEId state\r\n            toolbar: toolbarVisibleRTEId !== null,\r\n            // Enhanced height and scrolling behavior - consistent for all templates\r\n            height: 'auto',\r\n            minHeight: 28, // 1-line minimum for all templates\r\n            maxHeight: 112, // 4-line maximum for all templates\r\n            buttons: [\r\n\r\n        'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',\r\n        'font', 'fontsize', 'link',\r\n        {\r\n            name: 'more',\r\n            iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\r\n            list: [\r\n                        'source',\r\n                        'image', 'video', 'table',\r\n                'align', 'undo', 'redo', '|',\r\n                'hr', 'eraser', 'copyformat',\r\n                'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|',\r\n                'outdent', 'indent', 'paragraph',\r\n            ]\r\n        }\r\n    ],\r\n            autofocus: true,\r\n      showCharsCounter: false,\r\n\t\t\tshowWordsCounter: false,\r\n\t\t\tshowXPathInStatusbar: false,\r\n\t\t\tstatusbar: false,\r\n            pastePlain: true,\r\n\t\t\taskBeforePasteHTML: false,\r\n\t\t\taskBeforePasteFromWord: false,\r\n    // Fix dialog positioning by setting popup root to document body\r\n    popupRoot: document.body,\r\n    // Fix popup positioning issues\r\n    zIndex: 100000,\r\n    globalFullSize: false,\r\n    // Fix link dialog positioning\r\n    link: {\r\n        followOnDblClick: false,\r\n        processVideoLink: true,\r\n        processPastedLink: true,\r\n        openInNewTabCheckbox: true,\r\n        noFollowCheckbox: false,\r\n        modeClassName: 'input' as const,\r\n    },\r\n    // Dialog configuration\r\n    dialog: {\r\n        zIndex: 100001,\r\n    },\r\n    cursorAfterAutofocus: 'end' as const,\r\n    events: {\r\n                onPaste: handlePaste, // Attach custom onPaste handler\r\n    },\r\n    controls: {\r\n        font: {\r\n            list: {\r\n                \"Poppins, sans-serif\": \"Poppins\",\r\n                \"Roboto, sans-serif\": \"Roboto\",\r\n                \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\r\n                \"Open Sans, sans-serif\": \"Open Sans\",\r\n                \"Calibri, sans-serif\": \"Calibri\",\r\n                \"Century Gothic, sans-serif\": \"Century Gothic\",\r\n            }\r\n        }\r\n            }\r\n    }),[isRtlDirection, toolbarVisibleRTEId]\r\n\r\n    );\r\n\r\n        // Determine which containers to use based on guide type\r\n        const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n        const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n        const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n        const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n        const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n        const currentStepIndex = currentStep - 1;\r\n\r\n        let containersToRender: any[] = [];\r\n\r\n        if (isAIAnnouncement && !isTourAnnouncement) {\r\n            // For pure AI announcements (not in tours), use announcementGuideMetaData\r\n            containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\r\n        } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\r\n            // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\r\n            if (toolTipGuideMetaData[currentStepIndex]?.containers) {\r\n                containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\r\n                console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {\r\n                    totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,\r\n                    rteContainers: containersToRender.length,\r\n                    rteData: containersToRender.map(c => ({ id: c.id, rteBoxValue: c.rteBoxValue }))\r\n                });\r\n            } else {\r\n                console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);\r\n                containersToRender = [];\r\n            }\r\n        } else {\r\n            // For non-AI content, use rtesContainer\r\n            containersToRender = rtesContainer;\r\n        }\r\n\r\n        return (\r\n            <>\r\n                {containersToRender.map((item: any) => {\r\n                    let rteText = \"\";\r\n                    let rteId = \"\";\r\n                    let id = \"\";\r\n\r\n                    if ((isAIAnnouncement && !isTourAnnouncement) || (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement))) {\r\n                        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\r\n                        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\r\n                        rteText = item.rteBoxValue || \"\";\r\n                        rteId = item.id;\r\n                        id = item.id;\r\n                    } else {\r\n                        // For non-AI content, get data from rtesContainer\r\n                        rteText = item.rtes?.[0]?.text || \"\";\r\n                        rteId = item.rtes?.[0]?.id;\r\n                        id = item.id;\r\n                    }\r\n\r\n                    if (!id) return null;\r\n\r\n\r\n                    const currentContainerRef = getContainerRef(id);\r\n                    const currentEditorRef = getEditorRef(id);\r\n\r\n                    // Get content state for dynamic icon positioning\r\n                    const contentState = contentStates.get(id) || { isEmpty: isContentEmpty(rteText), isScrollable: false };\r\n\r\n                    return (\r\n                        <Box\r\n                            key={id}\r\n                            ref={currentContainerRef}\r\n                            sx={{\r\n                                display: \"flex\",\r\n                                alignItems: \"center\",\r\n                                position: \"relative\",\r\n                                \"& .jodit-add-new-line\": {\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: \"none !important\",\r\n\t\t\t\t\t\t\t\t\t\t},\r\n                                \"& .jodit-status-bar-link\": {\r\n                                    display: \"none !important\",\r\n                                },\r\n                                \"& .jodit-editor\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-editor span\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-toolbar-button button\": {\r\n                                    minWidth: \"29px !important\",\r\n                                },\r\n                                \".jodit-react-container\": {\r\n                                    width: selectedTemplate === \"Banner\" ? \"100%\" : \"100%\",\r\n                                    whiteSpace: \"pre-wrap\",\r\n                                    wordBreak: \"break-word\",\r\n                                },\r\n                                \".jodit-workplace\": {\r\n                                    minHeight: \"28px !important\", // 1-line minimum for all templates\r\n                                    maxHeight: \"112px !important\", // 4-line maximum for all templates\r\n                                    overflow: \"auto !important\", // Consistent scrolling for all templates\r\n                                    lineHeight: \"1.4 !important\",\r\n                                },\r\n                                \".jodit-container\": {\r\n                                    minHeight: \"28px !important\", // Consistent minimum height for all templates\r\n                                    border: \"none !important\"\r\n                                },\r\n                                \".jodit-toolbar__box\": {\r\n                                    display: \"flex !important\",\r\n                                    justifyContent: \"center !important\",\r\n                                    height: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"32px !important\": null,\r\n                                    maxHeight: selectedTemplate===\"Banner\"|| (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"32px !important\": null\r\n                                },\r\n                                // Fix Jodit dialog positioning - target correct classes\r\n                                \".jodit.jodit-dialog\": {\r\n                                    position: \"fixed !important\",\r\n                                    zIndex: \"100001 !important\",\r\n                                    top: \"50% !important\",\r\n                                    left: \"50% !important\",\r\n                                    transform: \"translate(-50%, -50%) !important\"\r\n                                },\r\n                                \".jodit-dialog .jodit-dialog__panel\": {\r\n                                    position: \"relative !important\",\r\n                                    top: \"auto !important\",\r\n                                    left: \"auto !important\",\r\n                                    transform: \"none !important\",\r\n                                    maxWidth: \"400px !important\",\r\n                                    background: \"white !important\",\r\n                                    border: \"1px solid #ccc !important\",\r\n                                    borderRadius: \"4px !important\",\r\n                                    boxShadow: \"0 4px 12px rgba(0,0,0,0.15) !important\"\r\n                                },\r\n                                // Fix for link dialog specifically\r\n                                \".jodit-dialog_alert\": {\r\n                                    position: \"fixed !important\",\r\n                                    zIndex: \"100001 !important\",\r\n                                    top: \"50% !important\",\r\n                                    left: \"50% !important\",\r\n                                    transform: \"translate(-50%, -50%) !important\"\r\n                                },\r\n                                // Enhanced scrollbar styling for consistent behavior\r\n                                \".jodit-workplace::-webkit-scrollbar\": {\r\n                                    width: \"6px !important\",\r\n                                },\r\n                                \".jodit-workplace::-webkit-scrollbar-track\": {\r\n                                    background: \"#f1f1f1 !important\",\r\n                                    borderRadius: \"3px !important\",\r\n                                },\r\n                                \".jodit-workplace::-webkit-scrollbar-thumb\": {\r\n                                    background: \"#c1c1c1 !important\",\r\n                                    borderRadius: \"3px !important\",\r\n                                },\r\n                                \".jodit-workplace::-webkit-scrollbar-thumb:hover\": {\r\n                                    background: \"#a8a8a8 !important\",\r\n                                },\r\n                                // Ensure proper line height and text styling\r\n                                \".jodit-wysiwyg\": {\r\n                                    lineHeight: \"1.4 !important\",\r\n                                    padding: \"8px !important\",\r\n                                },\r\n                                \".jodit-wysiwyg p\": {\r\n                                    margin: \"0 0 4px 0 !important\",\r\n                                    lineHeight: \"1.4 !important\",\r\n                                }\r\n                            }}\r\n                            className=\"qadpt-rte\"\r\n                        >\r\n                            {/* Always show Jodit editor, but conditionally show tooltips for non-Banner templates */}\r\n                            {(selectedTemplate === \"Announcement\" || selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\") || (selectedTemplateTour === \"Announcement\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\") ? (\r\n\r\n                                <Tooltip\r\n                                    title={\r\n                                        <>\r\n                                            <IconButton\r\n                                                size=\"small\"\r\n                                                onClick={() => handleCloneContainer(item.id)}\r\n                                                disabled={isCloneDisabled}\r\n                                                title={isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\")}\r\n                                                sx={{\r\n                                                    \"&:hover\": {\r\n                                                        backgroundColor: \"transparent !important\",\r\n                                                    },\r\n                                                    svg: {\r\n                                                        height: \"24px\",\r\n                                                        path: {\r\n                                                            fill:\"var(--primarycolor)\"\r\n                                                        }\r\n                                                    },\r\n                                                    }}\r\n                                                >\r\n                                                <span\r\n                                                    dangerouslySetInnerHTML={{ __html: copyicon }}\r\n                                                    style={{\r\n                                                        opacity: isCloneDisabled ? 0.5 : 1,\r\n                                                        height: '24px'\r\n                                                    }}\r\n                                                />\r\n                                            </IconButton>\r\n                                            <IconButton size=\"small\" onClick={() => handleDeleteSection(item.id, rteId)}\r\n                                            sx={{\r\n                                                \"&:hover\": {\r\n                                                    backgroundColor: \"transparent !important\",\r\n                                                    },\r\n                                                    svg: {\r\n                                                        path: {\r\n                                                            fill:\"var(--primarycolor)\"\r\n                                                        }\r\n                                                    },\r\n                                                }}\r\n                                            >\r\n                                                <span dangerouslySetInnerHTML={{ __html: deleteicon }}\r\n                                                    style={{\r\n                                                        height: '24px'\r\n                                                    }}\r\n                                                />\r\n                                            </IconButton>\r\n                                        </>\r\n                                    }\r\n                                    placement=\"top\"\r\n                                    slotProps={{\r\n                                        tooltip: {\r\n                                            sx: {\r\n                                                backgroundColor: 'white',\r\n                                                color: 'black',\r\n                                                borderRadius: '4px',\r\n                                                padding: '0px 4px',\r\n                                                border: \"1px dashed var(--primarycolor)\",\r\n                                                marginBottom: '30px !important'\r\n                                            },\r\n                                        },\r\n                                    }}\r\n                                    PopperProps={{\r\n                                        modifiers: [\r\n                                            {\r\n                                                name: 'preventOverflow',\r\n                                                options: { boundary: 'viewport' },\r\n                                            },\r\n                                            {\r\n                                                name: 'flip',\r\n                                                options: { enabled: true },\r\n                                            },\r\n                                        ],\r\n                                    }}\r\n                                >\r\n                                    <div style={{ width: \"100%\", position: \"relative\" }}>\r\n                                        <JoditEditor\r\n                                            ref={currentEditorRef}\r\n                                            value={rteText}\r\n                                            config={config}\r\n                                            onChange={(newContent) => handleUpdate(newContent, rteId, id)}\r\n                                        />\r\n                                        {/* Edit icon for toolbar toggle - positioned at bottom right */}\r\n                                        <IconButton\r\n                                            size=\"small\"\r\n                                            onClick={(e) => {\r\n                                                e.stopPropagation();\r\n                                                toggleToolbar(id);\r\n                                            }}\r\n                                            sx={{\r\n                                                position: \"absolute\",\r\n                                                // Dynamic positioning based on content state\r\n                                                bottom: contentState.isEmpty ? \"50%\" : (contentState.isScrollable ? \"8px\" : \"2px\"),\r\n                                                right: contentState.isEmpty ? \"auto\" : \"2px\",\r\n                                                left: contentState.isEmpty ? \"calc(100% - 32px)\" : \"auto\",\r\n                                                transform: contentState.isEmpty ? \"translateY(50%)\" : \"none\",\r\n                                                width: \"24px\",\r\n                                                height: \"24px\",\r\n                                                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n                                                zIndex: contentState.isScrollable ? 1001 : 1000, // Higher z-index when scrollable to stay on top\r\n                                                \"&:hover\": {\r\n                                                    backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n                                                },\r\n                                                \"& svg\": {\r\n                                                    width: \"16px\",\r\n                                                    height: \"16px\",\r\n                                                }\r\n                                            }}\r\n                                            title={translate(\"Toggle Toolbar\")}\r\n                                        >\r\n                                            <span\r\n                                                dangerouslySetInnerHTML={{ __html: editicon }}\r\n                                                style={{ height: '16px', width: '16px' }}\r\n                                            />\r\n                                        </IconButton>\r\n                                    </div>\r\n                                </Tooltip>\r\n                            ) : (\r\n                                <div style={{ width: \"100%\", position: \"relative\" }}>\r\n                                    <JoditEditor\r\n                                        ref={currentEditorRef}\r\n                                        value={rteText}\r\n                                        config={config}\r\n                                        onChange={(newContent) => handleUpdate(newContent, rteId, id)}\r\n                                    />\r\n                                    {/* Edit icon for toolbar toggle - positioned at bottom right */}\r\n                                    <IconButton\r\n                                        size=\"small\"\r\n                                        onClick={(e) => {\r\n                                            e.stopPropagation();\r\n                                            toggleToolbar(id);\r\n                                        }}\r\n                                        sx={{\r\n                                            position: \"absolute\",\r\n                                            // Dynamic positioning based on content state\r\n                                            bottom: contentState.isEmpty ? \"50%\" : (contentState.isScrollable ? \"8px\" : \"2px\"),\r\n                                            right: contentState.isEmpty ? \"auto\" : \"2px\",\r\n                                            left: contentState.isEmpty ? \"calc(100% - 32px)\" : \"auto\",\r\n                                            transform: contentState.isEmpty ? \"translateY(50%)\" : \"none\",\r\n                                            width: \"24px\",\r\n                                            height: \"24px\",\r\n                                            backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n                                            zIndex: contentState.isScrollable ? 1001 : 1000, // Higher z-index when scrollable to stay on top\r\n                                            \"&:hover\": {\r\n                                                backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n                                            },\r\n                                            \"& svg\": {\r\n                                                width: \"16px\",\r\n                                                height: \"16px\",\r\n                                            }\r\n                                        }}\r\n                                        title={translate(\"Toggle Toolbar\")}\r\n                                    >\r\n                                        <span\r\n                                            dangerouslySetInnerHTML={{ __html: editicon }}\r\n                                            style={{ height: '16px', width: '16px' }}\r\n                                        />\r\n                                    </IconButton>\r\n                                </div>\r\n                            )}\r\n                        </Box>\r\n                    );\r\n                })}\r\n            </>\r\n        );\r\n    }\r\n);\r\n\r\nexport default RTEsection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAACC,OAAO,QAAQ,OAAO;AAC9E,SAASC,GAAG,EAAaC,OAAO,EAAEC,UAAU,QAAQ,eAAe;AACnE,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,6BAA6B;AAE5E,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAY/C,MAAMC,UAAqC,gBAAAC,EAAA,cAAGhB,UAAU,CAAAiB,EAAA,GAAAD,EAAA,CACpD,CAAC;EAAEE,UAAU;EAAEC,QAAQ;EAAEC,sBAAsB;EAAEC,KAAK;EAAEC,aAAa;EAAEC,OAAO;EAAEC;AAAgB,CAAC,EAAEC,GAAG,KAAK;EAAAT,EAAA;EACvG,MAAM;IAAEU,CAAC,EAAEC;EAAU,CAAC,GAAGjB,cAAc,CAAC,CAAC;EACzC,MAAM;IACFkB,aAAa;IACbC,kBAAkB;IAClBC,mBAAmB;IACnBC,iBAAiB;IACjBC,eAAe;IACfC,gBAAgB;IAChBC,oBAAoB;IACpBC,yBAAyB;IACzBC,oBAAoB;IACpBC,0BAA0B;IAC1BC,qBAAqB;IACrBC,YAAY;IACZC,WAAW;IACXC;EACJ,CAAC,GAAGnC,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC+C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhD,QAAQ,CAAgB,IAAI,CAAC;EACnF,MAAMiD,UAAU,GAAG/C,MAAM,CAAS,EAAE,CAAC;;EAErC;EACA,MAAM,CAACgD,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAA2D,IAAIoD,GAAG,CAAC,CAAC,CAAC;;EAEvH;EACA,MAAMC,UAAU,GAAGnD,MAAM,CAAoC,IAAIkD,GAAG,CAAC,CAAC,CAAC;EACvE,MAAME,aAAa,GAAGpD,MAAM,CAA+C,IAAIkD,GAAG,CAAC,CAAC,CAAC;;EAErF;EACA,MAAMG,YAAY,GAAIC,KAAa,IAAK;IACpC,IAAI,CAACH,UAAU,CAACI,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MAChCH,UAAU,CAACI,OAAO,CAACE,GAAG,CAACH,KAAK,eAAEzD,KAAK,CAAC6D,SAAS,CAAC,CAAC,CAAC;IACpD;IACA,OAAOP,UAAU,CAACI,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EACxC,CAAC;;EAED;EACA,MAAMM,eAAe,GAAIN,KAAa,IAAK;IACvC,IAAI,CAACF,aAAa,CAACG,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MACnCF,aAAa,CAACG,OAAO,CAACE,GAAG,CAACH,KAAK,eAAEzD,KAAK,CAAC6D,SAAS,CAAC,CAAC,CAAC;IACvD;IACA,OAAON,aAAa,CAACG,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EAC3C,CAAC;;EAED;EACAvD,SAAS,CAAC,MAAM;IACZ,MAAM8D,kBAAkB,GAAIC,KAAiB,IAAK;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MAC9C,IAAI,CAACvB,YAAY,EAAE,OAAO,CAAC;;MAE3B,MAAMwB,yBAAyB,GAAIL,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,uBAAuB,CAAC,KAAK,IAAI;MACzG,MAAMC,oBAAoB,GAAIR,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,iBAAiB,CAAC,KAAK,IAAI;MAC9F,MAAME,aAAa,IAAAR,qBAAA,GAAGS,QAAQ,CAACC,aAAa,CAAC,cAAc,CAAC,cAAAV,qBAAA,uBAAtCA,qBAAA,CAAwCW,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MAC5F,MAAMO,kBAAkB,IAAAX,sBAAA,GAAGQ,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC,cAAAT,sBAAA,uBAAxCA,sBAAA,CAA0CU,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACnG,MAAMQ,sBAAsB,GAAGD,kBAAkB,MAAAV,sBAAA,GAAIO,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC,cAAAR,sBAAA,uBAA9CA,sBAAA,CAAgDS,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACnI,MAAMS,iBAAiB,GAAIf,KAAK,CAACM,MAAM,CAAiBU,EAAE,CAACC,UAAU,CAAC,yBAAyB,CAAC;MAChG,MAAMC,WAAW,IAAAd,sBAAA,GAAGM,QAAQ,CAACC,aAAa,CAAC,wBAAwB,CAAC,cAAAP,sBAAA,uBAAhDA,sBAAA,CAAkDQ,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACpG,MAAMa,qBAAqB,GAAInB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,+BAA+B,CAAC,KAAK,IAAI;MAC7G,MAAMa,cAAc,GAAIpB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,8BAA8B,CAAC,KAAK,IAAI;;MAErG;MACA,MAAMc,mBAAmB,GAAGvB,eAAe,CAACjB,YAAY,CAAC;;MAEzD;MACA,IACIwC,mBAAmB,aAAnBA,mBAAmB,eAAnBA,mBAAmB,CAAE5B,OAAO,IAC5B,CAAC4B,mBAAmB,CAAC5B,OAAO,CAACmB,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MAAI;MAC/D,CAACG,aAAa;MAAI;MAClB,CAACI,kBAAkB;MAAI;MACvB,CAACC,sBAAsB;MAAI;MAC3B,CAACC,iBAAiB;MAAI;MACtB,CAACG,WAAW;MAAI;MAChB,CAACC,qBAAqB;MAAI;MAC1B,CAACC,cAAc,IACf,CAACf,yBAAyB,IAC1B,CAACG,oBAAoB,EACvB;QACE1B,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;QACvBE,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC;MAClC;IACJ,CAAC;IAED0B,QAAQ,CAACY,gBAAgB,CAAC,WAAW,EAAEvB,kBAAkB,CAAC;IAC1D,OAAO,MAAMW,QAAQ,CAACa,mBAAmB,CAAC,WAAW,EAAExB,kBAAkB,CAAC;EAC9E,CAAC,EAAE,CAAClB,YAAY,CAAC,CAAC;EAElB5C,SAAS,CAAC,MAAM;IACZ,IAAI4C,YAAY,EAAE;MACd,MAAM2C,SAAS,GAAGjC,YAAY,CAACV,YAAY,CAAC;MAC5C,IAAI2C,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE/B,OAAO,EAAE;QACpBgC,UAAU,CAAC,MAAM;UACb;QAAA,CACH,EAAE,EAAE,CAAC;MACV;IACJ;EACJ,CAAC,EAAE,CAAC5C,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM6C,cAAc,GAAIC,OAAe,IAAc;IACjD,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IACzB;IACA,MAAMC,WAAW,GAAGD,OAAO,CAACE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC;IAC1D,OAAOF,WAAW,CAACG,MAAM,KAAK,CAAC;EACnC,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAIC,WAAmB,IAAc;IAC1D,MAAMC,YAAY,GAAGpC,eAAe,CAACmC,WAAW,CAAC;IACjD,IAAIC,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEzC,OAAO,EAAE;MACvB,MAAM0C,SAAS,GAAGD,YAAY,CAACzC,OAAO,CAACkB,aAAa,CAAC,kBAAkB,CAAC;MACxE,IAAIwB,SAAS,EAAE;QACX,OAAOA,SAAS,CAACC,YAAY,GAAGD,SAAS,CAACE,YAAY;MAC1D;IACJ;IACA,OAAO,KAAK;EAChB,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAACL,WAAmB,EAAEN,OAAe,KAAK;IACjE,MAAMY,OAAO,GAAGb,cAAc,CAACC,OAAO,CAAC;IACvC,MAAMa,YAAY,GAAGR,mBAAmB,CAACC,WAAW,CAAC;IAErD9C,gBAAgB,CAACsD,IAAI,IAAI;MACrB,MAAMC,MAAM,GAAG,IAAItD,GAAG,CAACqD,IAAI,CAAC;MAC5BC,MAAM,CAAC/C,GAAG,CAACsC,WAAW,EAAE;QAAEM,OAAO;QAAEC;MAAa,CAAC,CAAC;MAClD,OAAOE,MAAM;IACjB,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,YAAY,GAAGA,CAACC,UAAkB,EAAEpD,KAAa,EAAEyC,WAAmB,KAAK;IAC7EhD,UAAU,CAACQ,OAAO,GAAGmD,UAAU;;IAE/B;IACA,MAAMC,gBAAgB,GAAGnE,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IACzH,MAAMyE,QAAQ,GAAGpE,YAAY,IAAIN,gBAAgB,KAAK,MAAM;IAC5D,MAAM2E,kBAAkB,GAAGD,QAAQ,IAAIzE,oBAAoB,KAAK,cAAc;IAC9E,MAAM2E,YAAY,GAAGF,QAAQ,IAAIzE,oBAAoB,KAAK,QAAQ;IAClE,MAAM4E,aAAa,GAAGH,QAAQ,KAAKzE,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;IAE5G6E,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;MACpCzE,YAAY;MACZN,gBAAgB;MAChBC,oBAAoB;MACpBwE,gBAAgB;MAChBC,QAAQ;MACRE,YAAY;MACZf,WAAW;MACXW,UAAU,EAAEA,UAAU,CAACQ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG;IAC9C,CAAC,CAAC;IAEF,IAAIP,gBAAgB,EAAE;MAClB,MAAMQ,gBAAgB,GAAG1E,WAAW,GAAG,CAAC;MAExC,IAAIoE,kBAAkB,EAAE;QAAA,IAAAO,qBAAA,EAAAC,sBAAA;QACpB;QACA,MAAMC,gBAAgB,IAAAF,qBAAA,GAAG/E,oBAAoB,CAAC8E,gBAAgB,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAtCD,qBAAA,CAAwCG,UAAU,cAAAF,sBAAA,uBAAlDA,sBAAA,CAAoDG,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAAC3C,EAAE,KAAKiB,WAAW,IAAI0B,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIJ,gBAAgB,EAAE;UAClB;UACA/E,qBAAqB,CAACwD,WAAW,EAAEW,UAAU,CAAC;QAClD;MACJ,CAAC,MAAM;QAAA,IAAAiB,qBAAA,EAAAC,sBAAA;QACH;QACA,MAAMC,qBAAqB,IAAAF,qBAAA,GAAGvF,yBAAyB,CAAC+E,gBAAgB,CAAC,cAAAQ,qBAAA,wBAAAC,sBAAA,GAA3CD,qBAAA,CAA6CJ,UAAU,cAAAK,sBAAA,uBAAvDA,sBAAA,CAAyDJ,IAAI,CACtFC,SAAc,IAAKA,SAAS,CAAC3C,EAAE,KAAKiB,WAAW,IAAI0B,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIG,qBAAqB,EAAE;UACvB;UACAvF,0BAA0B,CAACyD,WAAW,EAAEW,UAAU,CAAC;QACvD;MACJ;IACJ,CAAC,MAAM,IAAIE,QAAQ,KAAKE,YAAY,IAAIC,aAAa,CAAC,EAAE;MAAA,IAAAe,sBAAA,EAAAC,sBAAA;MACpD;MACA,MAAMZ,gBAAgB,GAAG1E,WAAW,GAAG,CAAC;MACxC,MAAM6E,gBAAgB,IAAAQ,sBAAA,GAAGzF,oBAAoB,CAAC8E,gBAAgB,CAAC,cAAAW,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCP,UAAU,cAAAQ,sBAAA,uBAAlDA,sBAAA,CAAoDP,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAAC3C,EAAE,KAAKiB,WAAW,IAAI0B,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;MAED,IAAIJ,gBAAgB,EAAE;QAClB;QACA/E,qBAAqB,CAACwD,WAAW,EAAEW,UAAU,CAAC;QAC9CM,OAAO,CAACC,GAAG,CAAC,kCAAkC9E,oBAAoB,kBAAkB,CAAC;MACzF,CAAC,MAAM;QAAA,IAAA6F,sBAAA,EAAAC,sBAAA;QACHjB,OAAO,CAACkB,IAAI,CAAC,kCAAkC/F,oBAAoB,OAAO,EAAE;UACxEgF,gBAAgB;UAChBpB,WAAW;UACXoC,mBAAmB,GAAAH,sBAAA,GAAE3F,oBAAoB,CAAC8E,gBAAgB,CAAC,cAAAa,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCT,UAAU,cAAAU,sBAAA,uBAAlDA,sBAAA,CAAoDG,GAAG,CAACC,CAAC,KAAK;YAAEvD,EAAE,EAAEuD,CAAC,CAACvD,EAAE;YAAE4C,IAAI,EAAEW,CAAC,CAACX;UAAK,CAAC,CAAC;QAClH,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH;MACA5F,kBAAkB,CAACiE,WAAW,EAAEzC,KAAK,EAAEoD,UAAU,CAAC;MAClDM,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IAC7D;IAEAlF,mBAAmB,CAAC,IAAI,CAAC;;IAEzB;IACAqE,kBAAkB,CAACL,WAAW,EAAEW,UAAU,CAAC;EAC/C,CAAC;EACD,MAAM4B,oBAAoB,GAAIvC,WAAmB,IAAK;IAClD;IACA,IAAItE,eAAe,EAAE;MACjB,OAAO,CAAC;IACZ;;IAEA;IACAO,iBAAiB,CAAC+D,WAAW,CAAC;;IAE9B;IACA,IAAIvE,OAAO,EAAE;MACTA,OAAO,CAAC,CAAC;IACb;EACJ,CAAC;EACD,MAAM+G,mBAAmB,GAAGA,CAACxC,WAAmB,EAAEzC,KAAY,KAAK;IAC/D;IACA,MAAMqD,gBAAgB,GAAGnE,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IAEzH,IAAIwE,gBAAgB,EAAE;MAClB;MACA;MACA1E,eAAe,CAAC8D,WAAW,EAAEzC,KAAK,CAAC;IACvC,CAAC,MAAM;MACH;MACArB,eAAe,CAAC8D,WAAW,EAAEzC,KAAK,CAAC;IACvC;;IAEA;IACAjC,sBAAsB,CAACC,KAAK,CAAC;EACjC,CAAC;EACD,MAAMkH,WAAW,GAAI1E,KAA2C,IAAK;IACjEA,KAAK,CAAC2E,cAAc,CAAC,CAAC;IAEtB,MAAMC,aAAa,GAAG5E,KAAK,CAAC4E,aAAa;IACzC,MAAMC,UAAU,GAAGD,aAAa,CAACE,OAAO,CAAC,YAAY,CAAC;IACtD,MAAMC,UAAU,GAAGH,aAAa,CAACE,OAAO,CAAC,WAAW,CAAC;IAErD,IAAIC,UAAU,EAAE;MACZ,MAAMC,YAAY,GAAGD,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC;MACtD,IAAID,YAAY,EAAE;QACdE,aAAa,CAACH,UAAU,CAAC;MAC7B,CAAC,MAAM;QACHG,aAAa,CAACH,UAAU,CAAC;MAC7B;IACJ,CAAC,MAAM;MACHG,aAAa,CAACL,UAAU,CAAC;IAC7B;EACJ,CAAC;EAGD,MAAMK,aAAa,GAAIvD,OAAe,IAAK;IACvC,IAAI9C,YAAY,EAAE;MACd,MAAM2C,SAAS,GAAGjC,YAAY,CAACV,YAAY,CAAC;MAC5C,IAAI2C,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE/B,OAAO,EAAE;QACpB,MAAM0F,MAAM,GAAI3D,SAAS,CAAC/B,OAAO,CAAS0F,MAAM;QAChDA,MAAM,CAACC,SAAS,CAACC,UAAU,CAAC1D,OAAO,CAAC;MACxC;IACJ;EACJ,CAAC;EAED,MAAM2D,aAAa,GAAI9F,KAAa,IAAK;IACrC,IAAIT,mBAAmB,KAAKS,KAAK,EAAE;MAC/BR,sBAAsB,CAAC,IAAI,CAAC;IAChC,CAAC,MAAM;MACHA,sBAAsB,CAACQ,KAAK,CAAC;MAC7B;IACJ;EACJ,CAAC;EACD,MAAM,CAAC+F,cAAc,EAAEC,iBAAiB,CAAC,GAAGxJ,QAAQ,CAAU,KAAK,CAAC;EACpEC,SAAS,CAAC,MAAM;IACpB,MAAMwJ,GAAG,GAAG/E,QAAQ,CAACgF,IAAI,CAACC,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;IACtDH,iBAAiB,CAACC,GAAG,CAACG,WAAW,CAAC,CAAC,KAAK,KAAK,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;EACF,MAAMC,MAAM,GAAGzJ,OAAO,CAClB,OAAY;IACR0J,QAAQ,EAAE,KAAK;IAAE;IACjBC,SAAS,EAAER,cAAc,GAAG,KAAK,GAAY,KAAc;IAEvE;IACQS,QAAQ,EAAG,IAAI;IAAE;IACbC,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,KAAK;IACtB;IACAC,OAAO,EAAEpH,mBAAmB,KAAK,IAAI;IACrC;IACAqH,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,EAAE;IAAE;IACfC,SAAS,EAAE,GAAG;IAAE;IAChBC,OAAO,EAAE,CAEb,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EACnE,MAAM,EAAE,UAAU,EAAE,MAAM,EAC1B;MACIC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,+DAA+D;MACxEC,IAAI,EAAE,CACM,QAAQ,EACR,OAAO,EAAE,OAAO,EAAE,OAAO,EACjC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAC5B,IAAI,EAAE,QAAQ,EAAE,YAAY,EAC5B,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,EAC9D,SAAS,EAAE,QAAQ,EAAE,WAAW;IAExC,CAAC,CACJ;IACOC,SAAS,EAAE,IAAI;IACrBC,gBAAgB,EAAE,KAAK;IAC1BC,gBAAgB,EAAE,KAAK;IACvBC,oBAAoB,EAAE,KAAK;IAC3BC,SAAS,EAAE,KAAK;IACPC,UAAU,EAAE,IAAI;IACzBC,kBAAkB,EAAE,KAAK;IACzBC,sBAAsB,EAAE,KAAK;IAC5B;IACAC,SAAS,EAAEzG,QAAQ,CAACgF,IAAI;IACxB;IACA0B,MAAM,EAAE,MAAM;IACdC,cAAc,EAAE,KAAK;IACrB;IACAC,IAAI,EAAE;MACFC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI;MACvBC,oBAAoB,EAAE,IAAI;MAC1BC,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE;IACnB,CAAC;IACD;IACAC,MAAM,EAAE;MACJT,MAAM,EAAE;IACZ,CAAC;IACDU,oBAAoB,EAAE,KAAc;IACpCC,MAAM,EAAE;MACIC,OAAO,EAAEtD,WAAW,CAAE;IAClC,CAAC;IACDuD,QAAQ,EAAE;MACNC,IAAI,EAAE;QACFxB,IAAI,EAAE;UACF,qBAAqB,EAAE,SAAS;UAChC,oBAAoB,EAAE,QAAQ;UAC9B,2BAA2B,EAAE,eAAe;UAC5C,uBAAuB,EAAE,WAAW;UACpC,qBAAqB,EAAE,SAAS;UAChC,4BAA4B,EAAE;QAClC;MACJ;IACI;EACR,CAAC,CAAC,EAAC,CAACnB,cAAc,EAAExG,mBAAmB,CAEvC,CAAC;;EAEG;EACA,MAAM8D,gBAAgB,GAAGnE,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;EACzH,MAAMyE,QAAQ,GAAGpE,YAAY,IAAIN,gBAAgB,KAAK,MAAM;EAC5D,MAAM2E,kBAAkB,GAAGD,QAAQ,IAAIzE,oBAAoB,KAAK,cAAc;EAC9E,MAAM2E,YAAY,GAAGF,QAAQ,IAAIzE,oBAAoB,KAAK,QAAQ;EAClE,MAAM4E,aAAa,GAAGH,QAAQ,KAAKzE,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;EAC5G,MAAMgF,gBAAgB,GAAG1E,WAAW,GAAG,CAAC;EAExC,IAAIwJ,kBAAyB,GAAG,EAAE;EAElC,IAAItF,gBAAgB,IAAI,CAACE,kBAAkB,EAAE;IACzC;IACAoF,kBAAkB,GAAGvJ,8BAA8B,CAACyE,gBAAgB,EAAE,KAAK,CAAC;EAChF,CAAC,MAAM,IAAIP,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAC,EAAE;IAAA,IAAAqF,sBAAA;IAC1E;IACA,KAAAA,sBAAA,GAAI7J,oBAAoB,CAAC8E,gBAAgB,CAAC,cAAA+E,sBAAA,eAAtCA,sBAAA,CAAwC3E,UAAU,EAAE;MACpD0E,kBAAkB,GAAG5J,oBAAoB,CAAC8E,gBAAgB,CAAC,CAACI,UAAU,CAAC4E,MAAM,CAAC9D,CAAC,IAAIA,CAAC,CAACX,IAAI,KAAK,KAAK,CAAC;MACpGV,OAAO,CAACC,GAAG,CAAC,yDAAyD9E,oBAAoB,SAASgF,gBAAgB,GAAG,EAAE;QACnHiF,eAAe,EAAE/J,oBAAoB,CAAC8E,gBAAgB,CAAC,CAACI,UAAU,CAAC1B,MAAM;QACzEwG,aAAa,EAAEJ,kBAAkB,CAACpG,MAAM;QACxCyG,OAAO,EAAEL,kBAAkB,CAAC7D,GAAG,CAACC,CAAC,KAAK;UAAEvD,EAAE,EAAEuD,CAAC,CAACvD,EAAE;UAAEyH,WAAW,EAAElE,CAAC,CAACkE;QAAY,CAAC,CAAC;MACnF,CAAC,CAAC;IACN,CAAC,MAAM;MACHvF,OAAO,CAACkB,IAAI,CAAC,iDAAiD/F,oBAAoB,SAASgF,gBAAgB,EAAE,CAAC;MAC9G8E,kBAAkB,GAAG,EAAE;IAC3B;EACJ,CAAC,MAAM;IACH;IACAA,kBAAkB,GAAGpK,aAAa;EACtC;EAEA,oBACIhB,OAAA,CAAAE,SAAA;IAAAyL,QAAA,EACKP,kBAAkB,CAAC7D,GAAG,CAAEqE,IAAS,IAAK;MACnC,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAIpJ,KAAK,GAAG,EAAE;MACd,IAAIwB,EAAE,GAAG,EAAE;MAEX,IAAK6B,gBAAgB,IAAI,CAACE,kBAAkB,IAAMD,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAE,EAAE;QAClH;QACA;QACA6F,OAAO,GAAGD,IAAI,CAACF,WAAW,IAAI,EAAE;QAChCjJ,KAAK,GAAGmJ,IAAI,CAAC3H,EAAE;QACfA,EAAE,GAAG2H,IAAI,CAAC3H,EAAE;MAChB,CAAC,MAAM;QAAA,IAAA6H,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,YAAA;QACH;QACAJ,OAAO,GAAG,EAAAC,UAAA,GAAAF,IAAI,CAACM,IAAI,cAAAJ,UAAA,wBAAAC,WAAA,GAATD,UAAA,CAAY,CAAC,CAAC,cAAAC,WAAA,uBAAdA,WAAA,CAAgBI,IAAI,KAAI,EAAE;QACpC1J,KAAK,IAAAuJ,WAAA,GAAGJ,IAAI,CAACM,IAAI,cAAAF,WAAA,wBAAAC,YAAA,GAATD,WAAA,CAAY,CAAC,CAAC,cAAAC,YAAA,uBAAdA,YAAA,CAAgBhI,EAAE;QAC1BA,EAAE,GAAG2H,IAAI,CAAC3H,EAAE;MAChB;MAEA,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;MAGpB,MAAMK,mBAAmB,GAAGvB,eAAe,CAACkB,EAAE,CAAC;MAC/C,MAAMmI,gBAAgB,GAAG5J,YAAY,CAACyB,EAAE,CAAC;;MAEzC;MACA,MAAMoI,YAAY,GAAGlK,aAAa,CAACW,GAAG,CAACmB,EAAE,CAAC,IAAI;QAAEuB,OAAO,EAAEb,cAAc,CAACkH,OAAO,CAAC;QAAEpG,YAAY,EAAE;MAAM,CAAC;MAEvG,oBACIzF,OAAA,CAACV,GAAG;QAEAuB,GAAG,EAAEyD,mBAAoB;QACzBgI,EAAE,EAAE;UACAC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,UAAU;UACpB,uBAAuB,EAAE;YAC9CF,OAAO,EAAE;UACV,CAAC;UACqB,0BAA0B,EAAE;YACxBA,OAAO,EAAE;UACb,CAAC;UACD,iBAAiB,EAAE;YACfG,UAAU,EAAE;UAChB,CAAC;UACD,oBAAoB,EAAE;YAClBA,UAAU,EAAE;UAChB,CAAC;UACD,8BAA8B,EAAE;YAC5BC,QAAQ,EAAE;UACd,CAAC;UACD,wBAAwB,EAAE;YACtBC,KAAK,EAAEvL,gBAAgB,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM;YACtDwL,UAAU,EAAE,UAAU;YACtBC,SAAS,EAAE;UACf,CAAC;UACD,kBAAkB,EAAE;YAChBxD,SAAS,EAAE,iBAAiB;YAAE;YAC9BC,SAAS,EAAE,kBAAkB;YAAE;YAC/BwD,QAAQ,EAAE,iBAAiB;YAAE;YAC7BC,UAAU,EAAE;UAChB,CAAC;UACD,kBAAkB,EAAE;YAChB1D,SAAS,EAAE,iBAAiB;YAAE;YAC9B2D,MAAM,EAAE;UACZ,CAAC;UACD,qBAAqB,EAAE;YACnBV,OAAO,EAAE,iBAAiB;YAC1BW,cAAc,EAAE,mBAAmB;YACnC7D,MAAM,EAAEhI,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE,IAAI;YACnIiI,SAAS,EAAElI,gBAAgB,KAAG,QAAQ,IAAIA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE;UACrI,CAAC;UACD;UACA,qBAAqB,EAAE;YACnBmL,QAAQ,EAAE,kBAAkB;YAC5BpC,MAAM,EAAE,mBAAmB;YAC3B8C,GAAG,EAAE,gBAAgB;YACrBC,IAAI,EAAE,gBAAgB;YACtBC,SAAS,EAAE;UACf,CAAC;UACD,oCAAoC,EAAE;YAClCZ,QAAQ,EAAE,qBAAqB;YAC/BU,GAAG,EAAE,iBAAiB;YACtBC,IAAI,EAAE,iBAAiB;YACvBC,SAAS,EAAE,iBAAiB;YAC5BC,QAAQ,EAAE,kBAAkB;YAC5BC,UAAU,EAAE,kBAAkB;YAC9BN,MAAM,EAAE,2BAA2B;YACnCO,YAAY,EAAE,gBAAgB;YAC9BC,SAAS,EAAE;UACf,CAAC;UACD;UACA,qBAAqB,EAAE;YACnBhB,QAAQ,EAAE,kBAAkB;YAC5BpC,MAAM,EAAE,mBAAmB;YAC3B8C,GAAG,EAAE,gBAAgB;YACrBC,IAAI,EAAE,gBAAgB;YACtBC,SAAS,EAAE;UACf,CAAC;UACD;UACA,qCAAqC,EAAE;YACnCT,KAAK,EAAE;UACX,CAAC;UACD,2CAA2C,EAAE;YACzCW,UAAU,EAAE,oBAAoB;YAChCC,YAAY,EAAE;UAClB,CAAC;UACD,2CAA2C,EAAE;YACzCD,UAAU,EAAE,oBAAoB;YAChCC,YAAY,EAAE;UAClB,CAAC;UACD,iDAAiD,EAAE;YAC/CD,UAAU,EAAE;UAChB,CAAC;UACD;UACA,gBAAgB,EAAE;YACdP,UAAU,EAAE,gBAAgB;YAC5BU,OAAO,EAAE;UACb,CAAC;UACD,kBAAkB,EAAE;YAChBC,MAAM,EAAE,sBAAsB;YAC9BX,UAAU,EAAE;UAChB;QACJ,CAAE;QACFY,SAAS,EAAC,WAAW;QAAAjC,QAAA,EAGnBtK,gBAAgB,KAAK,cAAc,IAAIA,gBAAgB,KAAK,SAAS,IAAIA,gBAAgB,KAAK,SAAS,IAAMC,oBAAoB,KAAK,cAAc,IAAIA,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAU,gBAE/NtB,OAAA,CAACT,OAAO;UACJsO,KAAK,eACD7N,OAAA,CAAAE,SAAA;YAAAyL,QAAA,gBACI3L,OAAA,CAACR,UAAU;cACPsO,IAAI,EAAC,OAAO;cACZC,OAAO,EAAEA,CAAA,KAAMtG,oBAAoB,CAACmE,IAAI,CAAC3H,EAAE,CAAE;cAC7C+J,QAAQ,EAAEpN,eAAgB;cAC1BiN,KAAK,EAAEjN,eAAe,GAAGG,SAAS,CAAC,+CAA+C,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAE;cACjHuL,EAAE,EAAE;gBACA,SAAS,EAAE;kBACP2B,eAAe,EAAE;gBACrB,CAAC;gBACDC,GAAG,EAAE;kBACD7E,MAAM,EAAE,MAAM;kBACd8E,IAAI,EAAE;oBACFC,IAAI,EAAC;kBACT;gBACJ;cACA,CAAE;cAAAzC,QAAA,eAEN3L,OAAA;gBACIqO,uBAAuB,EAAE;kBAAEC,MAAM,EAAE3O;gBAAS,CAAE;gBAC9C4O,KAAK,EAAE;kBACHC,OAAO,EAAE5N,eAAe,GAAG,GAAG,GAAG,CAAC;kBAClCyI,MAAM,EAAE;gBACZ;cAAE;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACb5O,OAAA,CAACR,UAAU;cAACsO,IAAI,EAAC,OAAO;cAACC,OAAO,EAAEA,CAAA,KAAMrG,mBAAmB,CAACkE,IAAI,CAAC3H,EAAE,EAAExB,KAAK,CAAE;cAC5E6J,EAAE,EAAE;gBACA,SAAS,EAAE;kBACP2B,eAAe,EAAE;gBACjB,CAAC;gBACDC,GAAG,EAAE;kBACDC,IAAI,EAAE;oBACFC,IAAI,EAAC;kBACT;gBACJ;cACJ,CAAE;cAAAzC,QAAA,eAEF3L,OAAA;gBAAMqO,uBAAuB,EAAE;kBAAEC,MAAM,EAAE1O;gBAAW,CAAE;gBAClD2O,KAAK,EAAE;kBACHlF,MAAM,EAAE;gBACZ;cAAE;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA,eACf,CACL;UACDC,SAAS,EAAC,KAAK;UACfC,SAAS,EAAE;YACPC,OAAO,EAAE;cACLzC,EAAE,EAAE;gBACA2B,eAAe,EAAE,OAAO;gBACxBe,KAAK,EAAE,OAAO;gBACdxB,YAAY,EAAE,KAAK;gBACnBE,OAAO,EAAE,SAAS;gBAClBT,MAAM,EAAE,gCAAgC;gBACxCgC,YAAY,EAAE;cAClB;YACJ;UACJ,CAAE;UACFC,WAAW,EAAE;YACTC,SAAS,EAAE,CACP;cACI1F,IAAI,EAAE,iBAAiB;cACvB2F,OAAO,EAAE;gBAAEC,QAAQ,EAAE;cAAW;YACpC,CAAC,EACD;cACI5F,IAAI,EAAE,MAAM;cACZ2F,OAAO,EAAE;gBAAEE,OAAO,EAAE;cAAK;YAC7B,CAAC;UAET,CAAE;UAAA3D,QAAA,eAEF3L,OAAA;YAAKuO,KAAK,EAAE;cAAE3B,KAAK,EAAE,MAAM;cAAEH,QAAQ,EAAE;YAAW,CAAE;YAAAd,QAAA,gBAChD3L,OAAA,CAACP,WAAW;cACRoB,GAAG,EAAEuL,gBAAiB;cACtBmD,KAAK,EAAE1D,OAAQ;cACf/C,MAAM,EAAEA,MAAO;cACf0G,QAAQ,EAAG3J,UAAU,IAAKD,YAAY,CAACC,UAAU,EAAEpD,KAAK,EAAEwB,EAAE;YAAE;cAAAwK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eAEF5O,OAAA,CAACR,UAAU;cACPsO,IAAI,EAAC,OAAO;cACZC,OAAO,EAAG0B,CAAC,IAAK;gBACZA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnBnH,aAAa,CAACtE,EAAE,CAAC;cACrB,CAAE;cACFqI,EAAE,EAAE;gBACAG,QAAQ,EAAE,UAAU;gBACpB;gBACAkD,MAAM,EAAEtD,YAAY,CAAC7G,OAAO,GAAG,KAAK,GAAI6G,YAAY,CAAC5G,YAAY,GAAG,KAAK,GAAG,KAAM;gBAClFmK,KAAK,EAAEvD,YAAY,CAAC7G,OAAO,GAAG,MAAM,GAAG,KAAK;gBAC5C4H,IAAI,EAAEf,YAAY,CAAC7G,OAAO,GAAG,mBAAmB,GAAG,MAAM;gBACzD6H,SAAS,EAAEhB,YAAY,CAAC7G,OAAO,GAAG,iBAAiB,GAAG,MAAM;gBAC5DoH,KAAK,EAAE,MAAM;gBACbvD,MAAM,EAAE,MAAM;gBACd4E,eAAe,EAAE,0BAA0B;gBAC3C5D,MAAM,EAAEgC,YAAY,CAAC5G,YAAY,GAAG,IAAI,GAAG,IAAI;gBAAE;gBACjD,SAAS,EAAE;kBACPwI,eAAe,EAAE;gBACrB,CAAC;gBACD,OAAO,EAAE;kBACLrB,KAAK,EAAE,MAAM;kBACbvD,MAAM,EAAE;gBACZ;cACJ,CAAE;cACFwE,KAAK,EAAE9M,SAAS,CAAC,gBAAgB,CAAE;cAAA4K,QAAA,eAEnC3L,OAAA;gBACIqO,uBAAuB,EAAE;kBAAEC,MAAM,EAAEzO;gBAAS,CAAE;gBAC9C0O,KAAK,EAAE;kBAAElF,MAAM,EAAE,MAAM;kBAAEuD,KAAK,EAAE;gBAAO;cAAE;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,gBAEV5O,OAAA;UAAKuO,KAAK,EAAE;YAAE3B,KAAK,EAAE,MAAM;YAAEH,QAAQ,EAAE;UAAW,CAAE;UAAAd,QAAA,gBAChD3L,OAAA,CAACP,WAAW;YACRoB,GAAG,EAAEuL,gBAAiB;YACtBmD,KAAK,EAAE1D,OAAQ;YACf/C,MAAM,EAAEA,MAAO;YACf0G,QAAQ,EAAG3J,UAAU,IAAKD,YAAY,CAACC,UAAU,EAAEpD,KAAK,EAAEwB,EAAE;UAAE;YAAAwK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eAEF5O,OAAA,CAACR,UAAU;YACPsO,IAAI,EAAC,OAAO;YACZC,OAAO,EAAG0B,CAAC,IAAK;cACZA,CAAC,CAACC,eAAe,CAAC,CAAC;cACnBnH,aAAa,CAACtE,EAAE,CAAC;YACrB,CAAE;YACFqI,EAAE,EAAE;cACAG,QAAQ,EAAE,UAAU;cACpB;cACAkD,MAAM,EAAEtD,YAAY,CAAC7G,OAAO,GAAG,KAAK,GAAI6G,YAAY,CAAC5G,YAAY,GAAG,KAAK,GAAG,KAAM;cAClFmK,KAAK,EAAEvD,YAAY,CAAC7G,OAAO,GAAG,MAAM,GAAG,KAAK;cAC5C4H,IAAI,EAAEf,YAAY,CAAC7G,OAAO,GAAG,mBAAmB,GAAG,MAAM;cACzD6H,SAAS,EAAEhB,YAAY,CAAC7G,OAAO,GAAG,iBAAiB,GAAG,MAAM;cAC5DoH,KAAK,EAAE,MAAM;cACbvD,MAAM,EAAE,MAAM;cACd4E,eAAe,EAAE,0BAA0B;cAC3C5D,MAAM,EAAEgC,YAAY,CAAC5G,YAAY,GAAG,IAAI,GAAG,IAAI;cAAE;cACjD,SAAS,EAAE;gBACPwI,eAAe,EAAE;cACrB,CAAC;cACD,OAAO,EAAE;gBACLrB,KAAK,EAAE,MAAM;gBACbvD,MAAM,EAAE;cACZ;YACJ,CAAE;YACFwE,KAAK,EAAE9M,SAAS,CAAC,gBAAgB,CAAE;YAAA4K,QAAA,eAEnC3L,OAAA;cACIqO,uBAAuB,EAAE;gBAAEC,MAAM,EAAEzO;cAAS,CAAE;cAC9C0O,KAAK,EAAE;gBAAElF,MAAM,EAAE,MAAM;gBAAEuD,KAAK,EAAE;cAAO;YAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MACR,GAjQI3K,EAAE;QAAAwK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkQN,CAAC;IAEd,CAAC;EAAC,gBACJ,CAAC;AAEX,CAAC;EAAA,QAxqB4B9O,cAAc,EAgBnCJ,cAAc;AAAA,EAypB1B,CAAC;EAAA,QAzqBgCI,cAAc,EAgBnCJ,cAAc;AAAA,EAypBzB;AAACmQ,GAAA,GA3qBI1P,UAAqC;AA6qB3C,eAAeA,UAAU;AAAC,IAAAE,EAAA,EAAAwP,GAAA;AAAAC,YAAA,CAAAzP,EAAA;AAAAyP,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}