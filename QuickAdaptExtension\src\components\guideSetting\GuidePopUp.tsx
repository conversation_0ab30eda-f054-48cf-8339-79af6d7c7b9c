import React, { useMemo, useState, useEffect, useRef } from "react";
import { TB<PERSON>onAction, TInteractionValue, TSectionType } from "../../store/drawerStore";
import {
	Dialog,
	DialogContent,
	useMediaQuery,
	useTheme,
	Box,
	IconButton,
	Popover,
	Typography,
	Button,
	FormControl,
	Select,
	MenuItem,
	TextField,
	SelectChangeEvent,
	RadioGroup,
	Radio,
	FormControlLabel,
	Input,
	ToggleButton,
	ToggleButtonGroup,
	Autocomplete,
	CircularProgress,
	DialogTitle,
	Tooltip,
	LinearProgress,
	MobileStepper,
	Breadcrumbs,
	DialogActions
} from "@mui/material";
import ImageSection from "./PopupSections/Imagesection";
import RTEsection from "./PopupSections/RTEsection";
import ButtonSection from "./PopupSections/Button";
import AddIcon from "@mui/icons-material/Add";
import DragIndicatorIcon from "@mui/icons-material/DragIndicator";
import { Image, TextFormat, Code, VideoLibrary, GifBox, Link, Opacity } from "@mui/icons-material";
import HtmlSection from "./PopupSections/HtmlSection";
import VideoSection from "./PopupSections/VideoSection";
import useDrawerStore from "../../store/drawerStore";
import CloseIcon from "@mui/icons-material/Close";
import "../guideDesign/Canvas.module.css";
import PerfectScrollbar from "react-perfect-scrollbar";
import "react-perfect-scrollbar/dist/css/styles.css";
import WarningIcon from "@mui/icons-material/Warning";
import AlertPopup from "../drawer/AlertPopup";
import { color } from "jodit/esm/plugins/color/color";
import { useTranslation } from 'react-i18next';
export const TOOLTIP_MN_WIDTH = 500;

// Helper function to convert hex color to rgba with opacity
const hexToRgba = (hex: string, opacity: number): string => {
	// Remove # if present
	hex = hex.replace('#', '');

	// Parse hex values
	const r = parseInt(hex.substring(0, 2), 16);
	const g = parseInt(hex.substring(2, 4), 16);
	const b = parseInt(hex.substring(4, 6), 16);

	return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

type SectionType = { type: "image" | "button" | "video" | "gif" | "html" } | { type: "text" }; // Only text sections have IDs
const GuidePopup = ({
	selectedStepType,
	guideStep,
	setImageSrc,
	imageSrc,
	textBoxRef,
	htmlContent,
	setHtmlContent,
	buttonColor,
	setButtonColor,
	setImageName,
	imageName,
	openStepDropdown,
	openWarning,
	setopenWarning,
	isUnSavedChanges,
	handleLeave,
}: any) => {
	const { t: translate } = useTranslation();
	const {
		addNewButton,
		updateButton,
		guideListByOrg,
		getGuildeListByOrg,
		updateButtonInteraction,
		addNewImageContainer,
		dismissData,
		setSelectActions,
		currentButtonName,
		setCurrentButtonName,
		targetURL,
		setTargetURL,
		selectedInteraction,
		setSelectedInteraction,
		openInteractionList,
		setOpenInteractionList,
		selectedTab,
		setSelectedTab,
		loading,
		setLoading,
		addNewRTEContainer,
		dismiss,
		currentStepIndex,
		selectedOption,
		progress,
		steps,
		setProgress,
		selectedTemplate,
		updateTooltipButtonAction,
		updateTooltipButtonInteraction,
		selectedTemplateTour,
		setIsUnSavedChanges,
		ProgressColor,
		setProgressColor,
		createWithAI
	} = useDrawerStore((state) => state);
	const [open, setOpen] = useState(true);
	const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
	const [sections, setSections] = useState<SectionType[]>([{ type: "image" }, { type: "text" }, { type: "button" }]);
	const [draggingIndex, setDraggingIndex] = useState<number | null>(null);

	const [sectionCounts, setSectionCounts] = useState({
		image: 1, // Start with one of each as per initial sections state
		text: 1,
		button: 1,
		video: 0,
		gif: 0,
		html: 0
	});

	// Maximum allowed sections of each type
	const MAX_SECTIONS = {
		image: 3,
		text: 3, // RTE sections
		button: 3,
		video: 3,
		gif: 3,
		html: 3
	};

	// Helper function to check if a section type has reached its limit
	const hasReachedLimit = (type: SectionType["type"]): boolean => {
		// Map "text" to "text" for the check
		const checkType = type === "text" ? "text" : type;
		return sectionCounts[checkType] >= MAX_SECTIONS[checkType];
	};

	//const [imageSrc, setImageSrc] = useState<string>("");
	//const [imageName, setImageName] = useState<string>("");
	//const [selectedActions, setSelectActions] = useState<string>("close");
	//const [selectedTab, setSelectedTab] = useState<string>("new-tab");
	//const [targetURL, setTargetURL] = useState<string>("");
	//const [selectedInteraction, setSelectedInteraction] = useState(null);
	//const [currentButtonName, setCurrentButtonName] = useState("");
	//const [openInteractionList, setOpenInteractionList] = useState(false);
	//const [loading, setLoading] = useState(false);
	const [action, setAction] = useState("close");
	const userInfo = localStorage.getItem("userInfo");
	const userInfoObj = JSON.parse(userInfo || "{}");
	const orgDetails = JSON.parse(userInfoObj.orgDetails || "{}");
	const organizationId = orgDetails.OrganizationId;
	const overlayEnabled = useDrawerStore((state) => state.overlayEnabled);
	const guidePopUpRef = useRef<HTMLDivElement | null>(null);

	//Added Zustand here
	const {
		designPopup,
		announcementJson,
		currentStep,
		setSettingAnchorEl,
		settingAnchorEl,
		updateButtonAction,
		getCurrentButtonInfo,
		buttonsContainer,
		buttonId,
		setButtonId,
		cuntainerId,
		setCuntainerId,
		btnname,
		setBtnName,
		rtesContainer,
		imagesContainer,
	} = useDrawerStore((state) => state);

	const theme = useTheme();
	const isFullScreen = useMediaQuery(theme.breakpoints.down("sm"));

	// Synchronize local sectionCounts with actual store state
	useEffect(() => {
		setSectionCounts({
			image: imagesContainer.length,
			text: rtesContainer.length,
			button: buttonsContainer.length,
			video: 0,
			gif: 0,
			html: 0
		});
	}, [buttonsContainer.length, rtesContainer.length, imagesContainer.length]);

	const handleCloseInteraction = () => {
		setOpenInteractionList(false);
	};

	const handleOpenInteraction = () => {
		setOpenInteractionList(true);
		if (organizationId && !guideListByOrg.length) {
			(async () => {
				setLoading(true);
				await getGuildeListByOrg(organizationId);
				setLoading(false);
			})();
		}
	};

	const handleClose = (_event?: object, reason?: string) => {
		if (reason === "backdropClick" || reason === "escapeKeyDown") {
			return;
		}
		setOpen(false);
	};

	const handleAddIconClick = (event: React.MouseEvent<HTMLElement>) => {
		if (hasReachedLimit("text") && hasReachedLimit("button") && hasReachedLimit("image")) {
			return;
		}
		setAnchorEl(event.currentTarget);
	};

	const handlePopoverClose = () => {
		setAnchorEl(null);
	};
	const handleAddSection = (type: SectionType["type"]) => {
		// Check if we've reached the limit for this section type
		if (hasReachedLimit(type)) {
			// Don't add more sections if limit is reached
			setAnchorEl(null);
			return;
		}

		if (type === "button") {
			// Create and add a new button with default values
			addNewButton(
				{
					id: crypto.randomUUID(),
					name: "Button 1",
					position: "center",
					type: "primary",
					isEditing: false,
					index: 0,
					style: { ...defaultButtonColors },
					actions: {
						value: "close", // Default action is "close"
						targetURL: targetURL, // Default empty target URL
						tab: "same-tab", // Default tab behavior
					},
				},
				""
			);

			// Set the temporary colors (this might be for styling the new button)
			setTempColors(defaultButtonColors);

			// Optionally, set the selected actions (if needed outside of the button creation)
			setSelectedActions({
				value: "close", // Default action is "close"
				targetURL: targetURL, // Default empty target URL
				tab: "same-tab", // Default tab behavior
			});
		} else if (type === "image") {
			addNewImageContainer();
		} else if (type === "text") {
			addNewRTEContainer();
		} else {
			// For other section types
			setSections((prevSections) => [...prevSections, { type } as SectionType]);
		}
		setAnchorEl(null);
	};

	const handleDragStart = (index: number) => {
		setDraggingIndex(index);
	};

	const handleDragEnter = (index: number) => {
		if (draggingIndex !== null && draggingIndex !== index) {
			const reorderedSections = [...sections];
			const [removed] = reorderedSections.splice(draggingIndex, 1);
			reorderedSections.splice(index, 0, removed);
			setSections(reorderedSections);
			setDraggingIndex(index);
		}
	};

	const handleDragEnd = () => {
		setDraggingIndex(null);
	};

	const handleDeleteRTESection = (index: number) => {
		// RTE section deletion is handled by the store
		// Section counts will be automatically updated via useEffect
	};

	// Function to handle deletion of image sections
	const handleDeleteImageSection = () => {
		// Image section deletion is handled by the store
		// Section counts will be automatically updated via useEffect
	};

	// Function to handle deletion of button sections
	const handleDeleteButtonSection = () => {
		// Button section deletion is handled by the store
		// Section counts will be automatically updated via useEffect
	};

	// Function to handle cloning of RTE sections
	const handleCloneRTESection = () => {
		// Check if we've reached the limit for RTE sections
		if (hasReachedLimit("text")) {
			return; // Don't clone if limit is reached
		}
		// RTE section cloning is handled by the store
		// Section counts will be automatically updated via useEffect
	};

	// Function to handle cloning of image sections
	const handleCloneImageSection = () => {
		// Check if we've reached the limit for image sections
		if (hasReachedLimit("image")) {
			return; // Don't clone if limit is reached
		}
		// Image section cloning is handled by the store
		// Section counts will be automatically updated via useEffect
	};

	// Function to handle cloning of button sections
	const handleCloneButtonSection = () => {
		// Check if we've reached the limit for button sections
		if (hasReachedLimit("button")) {
			return; // Don't clone if limit is reached
		}
		// Button section cloning is handled by the store
		// Section counts will be automatically updated via useEffect
	};
	// State to track if scrolling is needed
	const [needsScrolling, setNeedsScrolling] = useState(false);
	const scrollbarRef = useRef<any>(null);
	const renderSection = (section: SectionType, index: number) => {
		switch (section.type) {
			case "image":
				return (
					<ImageSection
						key={index}
						setImageSrc={setImageSrc}
						imageSrc={imageSrc}
						setImageName={setImageName}
						imageName={imageName}
						onDelete={handleDeleteImageSection}
						onClone={handleCloneImageSection}
						isCloneDisabled={hasReachedLimit("image")}
					/>
				);
			case "text":
				return (
					<RTEsection
						key={index} // Use unique ID as the key for RTESection
						textBoxRef={textBoxRef}
						isBanner={false}
						handleDeleteRTESection={() => handleDeleteRTESection(index)}
						index={index}
						// @ts-ignore
						ref={textBoxRef}
						guidePopUpRef={guidePopUpRef}
						onClone={handleCloneRTESection}
						isCloneDisabled={hasReachedLimit("text")}
					/>
				);
			case "button":
				return (
					<ButtonSection
						key={index}
						buttonColor={buttonColor}
						setButtonColor={setButtonColor}
						isBanner={false}
						onDelete={handleDeleteButtonSection}
						onClone={handleCloneButtonSection}
						isCloneDisabled={hasReachedLimit("button")}
					/>
				);
			case "video":
				return <VideoSection key={index} />;
			case "html":
				return (
					<HtmlSection
						key={index}
						htmlContent={htmlContent}
						setHtmlContent={setHtmlContent}
						isBanner={false}
					/>
				);
			default:
				return null;
		}
	};

	const style = announcementJson.GuideStep.find((step) => step.stepName === currentStep)?.Canvas as
		| Record<string, unknown>
		| undefined;

	const popupStyle: React.CSSProperties = {
		//maxWidth: "533px",
		//	minWidth: TOOLTIP_MN_WIDTH,
		maxWidth: `${style?.Width} !important` || "500px !important",
		// maxHeight: "400px",
		width: `${style?.Width || 500}px`,
		borderRadius: `${style?.Radius ?? 8}px`,
		borderWidth: `${style?.BorderWidth || 0}px`,
		display: "flex" as const,
		flexDirection: "column" as const,
		borderColor: `${style?.BorderColor || "transparent"}`,
		backgroundColor: `${style?.BackgroundColor || "#fff"}`,
		border: `${style?.BorderSize || "0"}px solid ${style?.BorderColor || "none"}`,
		overflow: "visible",
	};

	const sectionStyle = {
		width: "100%",
		display: "flex",
		flexDirection: "column",
		position: "relative" as const,
		"&:hover .add-icon": {
			display: "flex",
		},
		"&:hover .side-add-icon": {
			display: "flex",
		},
		"&:hover .add-section": {
			opacity: "1",
		},
	};

	const dragButtonStyle = {
		position: "absolute" as const,
		left: "-60px",
		top: "50%",
		transform: "translateY(-50%)",
		cursor: "move",
		zIndex: 1000,
	};

	const sideAddButtonStyle = {
		position: "absolute" as const,
		right: "-38px",
		top: "50%",
		transform: "translateY(-50%)",
		width: "18px",
		height: "100%",
		borderRadius: "6px",
		display: "none",
		alignItems: "center",
		justifyContent: "center",
		backgroundColor: "#5F9EA0",
		cursor: "pointer",
		zIndex: 1000,
		"&:hover": {
			backgroundColor: "#70afaf",
		},
	};

	const handleChangeTabs = (event: React.MouseEvent<HTMLElement>) => {
		setSelectedTab((event.target as HTMLInputElement).value);
	};

	const handleCloseSettingPopup = (containerId: string, buttonId: string) => {
		updateButtonAction(containerId, buttonId, selectedActions);
		updateButtonInteraction(containerId, buttonId, selectedInteraction);

		setSettingAnchorEl({
			containerId: "",
			buttonId: "",
			value: null,
		});
	};
	const [targetURLError, setTargetURLError] = useState("");

	const validateTargetURL = (url: string) => {
		if (selectedActions.value === "open-url") {
			if (!url) {
				return "URL is required";
			}
			try {
				new URL(url);
				return "";
			} catch (error) {
				return "Invalid URL";
			}
		}
		return "";
	};

	const handleApplyChanges = (containerId: string, buttonId: string) => {
		const error = validateTargetURL(targetURL);
		setTargetURLError(error); // Set the error message for display

		if (error) {
			return; // Prevent applying changes if there's a validation error
		}

		const buttonNameToUpdate = !currentButtonName || !currentButtonName.trim()
			? curronButtonInfo.title // Retain the previously saved button name
			: currentButtonName;
		setCurrentButtonName(buttonNameToUpdate);

		updateButton(containerId, buttonId, "style", tempColors);
		updateButtonAction(containerId, buttonId, selectedActions); // Update the selected actions
		updateButtonInteraction(containerId, buttonId, selectedInteraction);
		updateButton(containerId, buttonId, "name", buttonNameToUpdate);
		updateButton(containerId, buttonId, "actions", selectedActions); // Update button actions
		setSettingAnchorEl({ containerId: "", buttonId: "", value: null });
		setIsUnSavedChanges(true);
	};
	const handleURLChange = (e: any) => {
		const newURL = e.target.value;
		setTargetURL(newURL);

		// Validate the URL and update the error state
		const error = validateTargetURL(newURL);
		setTargetURLError(error);

		setSelectedActions({
			value: selectedActions.value,
			targetURL: newURL,
			tab: selectedTab as "same-tab" | "new-tab",
		});
	};

	const curronButtonInfo = useMemo(() => {
		const result = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);
		setCurrentButtonName(result.title);
		setBtnName(result.title);
		setAction(result.value);
		return result;
	}, [settingAnchorEl.containerId, settingAnchorEl.buttonId]);

	const defaultButtonColors = {
		backgroundColor: "#5F9EA0",
		borderColor: "#70afaf",
		color: "#ffffff",
	};

	const [selectedActions, setSelectedActions] = useState<TButtonAction>({
		value: "close", // Default action
		targetURL: "", // Default empty target URL
		tab: "same-tab", // Default tab (same-tab)
	});
	const [tempColors, setTempColors] = useState(defaultButtonColors);
	const selectedButton = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);

	useEffect(() => {
		const handleSelectButton = (containerId: any, buttonId: any) => {
			const selectedButton = getCurrentButtonInfo(containerId, buttonId);
			if (selectedButton) {
				setTargetURL(selectedButton.targetURL || "");
				setTempColors({
					backgroundColor: selectedButton.bgColor || defaultButtonColors.backgroundColor,
					borderColor: selectedButton.borderColor || defaultButtonColors.borderColor,
					color: selectedButton.textColor || defaultButtonColors.color,
				});
				setSelectedActions({
					value: selectedButton.selectedActions || "close", // Default to "close" if no action is set
					targetURL: selectedButton.targetURL || targetURL, // Can be updated later if needed
					tab: "same-tab", // Default tab behavior
				});
			}
		};
		handleSelectButton(settingAnchorEl.containerId, settingAnchorEl.buttonId);
	}, [settingAnchorEl.containerId, settingAnchorEl.buttonId]);

	// Function to handle color changes in the color picker
	const handleColorChange = (e: any, targetName: any) => {
		const value = e.target.value;
		setTempColors((prev) => ({
			...prev,
			[targetName]: value,
		}));
	};
useEffect(() => {

		setSelectedActions({
			value: selectedActions.value, // Default action
			targetURL: targetURL, // Default empty target URL
			tab: "same-tab", // Default tab (same-tab)
		});
}, []);
// Check if content needs scrolling with improved detection
useEffect(() => {
	const checkScrollNeeded = () => {
		if (guidePopUpRef.current) {
			// Force a reflow to get accurate measurements
			guidePopUpRef.current.style.height = 'auto';
			const contentHeight = guidePopUpRef.current.scrollHeight;
			const containerHeight = 320; // max-height value
			const shouldScroll = contentHeight > containerHeight;


			setNeedsScrolling(shouldScroll);

			// Force update scrollbar
			if (scrollbarRef.current) {
				// Try multiple methods to update the scrollbar
				if (scrollbarRef.current.updateScroll) {
					scrollbarRef.current.updateScroll();
				}
				// Force re-initialization if needed
				setTimeout(() => {
					if (scrollbarRef.current && scrollbarRef.current.updateScroll) {
						scrollbarRef.current.updateScroll();
					}
				}, 10);
			}
		}
	};

	
	checkScrollNeeded();

	
	const timeouts = [
		setTimeout(checkScrollNeeded, 50),
		setTimeout(checkScrollNeeded, 100),
		setTimeout(checkScrollNeeded, 200),
		setTimeout(checkScrollNeeded, 500)
	];

	
	let resizeObserver: ResizeObserver | null = null;
	let mutationObserver: MutationObserver | null = null;

	if (guidePopUpRef.current && window.ResizeObserver) {
		resizeObserver = new ResizeObserver(() => {
			setTimeout(checkScrollNeeded, 10);
		});
		resizeObserver.observe(guidePopUpRef.current);
	}

	
	if (guidePopUpRef.current && window.MutationObserver) {
		mutationObserver = new MutationObserver(() => {
			setTimeout(checkScrollNeeded, 10);
		});
		mutationObserver.observe(guidePopUpRef.current, {
			childList: true,
			subtree: true,
			attributes: true,
			attributeFilter: ['style', 'class']
		});
	}

	return () => {
		timeouts.forEach(clearTimeout);
		if (resizeObserver) {
			resizeObserver.disconnect();
		}
		if (mutationObserver) {
			mutationObserver.disconnect();
		}
	};
}, [ currentStep]);
	const handleChangeActions = (e: SelectChangeEvent) => {
		const v: TInteractionValue = e.target.value as TInteractionValue; // Casting to TInteractionValue
		setSelectedActions({
			value: v, // Ensure that selectedActions.value is of type TInteractionValue
			targetURL: targetURL,
			tab: selectedTab as "same-tab" | "new-tab", // Ensure tab is a valid value
		});
	};
	// useEffect(() => {
	// 	if (selectedButton) {
	// 	  selectedButton.targetURL = targetURL;  // Update selectedButton's targetURL whenever targetURL state changes
	// 	}
	//   }, [targetURL]);  // Dependency on `targetURL`
	// Dependency on `targetURL`


	return (
		<div className="qadpt-annpopop">
			{overlayEnabled && (
				<Box
					sx={{
						position: "fixed",
						top: 0,
						left: 0,
						width: "100vw",
						height: "100vh",
						backgroundColor: "rgba(0, 0, 0, 0.5)",
						zIndex: 9,
					}}
				/>
			)}
			{isUnSavedChanges && openWarning &&(
				<AlertPopup
					openWarning={openWarning}
					setopenWarning={setopenWarning}
					handleLeave={handleLeave}
				/>
			)}

			<Dialog
				className="qadpt-guide-popup"
				open={open}
				onClose={handleClose}
				fullScreen={isFullScreen}
				PaperProps={{
					style: popupStyle,
				}}
				maxWidth={false}
				disableEnforceFocus={true}
			>
				{/* <div style={{placeContent:"end",display:"flex"}}> */}
					{dismiss && (
						<IconButton className="qadpt-dismiss"
							//onClick={handleCloseBanner}
						>
<CloseIcon sx={{ zoom: 1,color:"#000"}} />
						</IconButton>
						)}
				{/* </div> */}
					<div style={{ padding: `${style?.Padding || 12}px`, paddingRight: 0 }}>
				<PerfectScrollbar
				key={`scrollbar-${needsScrolling}`}
				ref={scrollbarRef}
				style={{ maxHeight: "400px" }}
				options={{
					suppressScrollY: false,
					suppressScrollX: true,
					wheelPropagation: false,
					swipeEasing: true,
					minScrollbarLength: 20,
					scrollingThreshold: 1000,
					scrollYMarginOffset: 0
				}}
			>
				<DialogContent
					sx={{
						padding: 0,
						overflow: "hidden",
						display: "flex",
						flexDirection: "column",
						position: "relative",
					}}
						ref={guidePopUpRef}
						
					id="guide-popup"
				>
					{/* {dismissData?.dismisssel ? (
						<DialogTitle
							sx={{
								display: "flex",
								justifyContent: "end",
								padding: "5px",
							}}
						>
							<IconButton
								// sx={{ position: "absolute", right: 8, top: 8, color: `${dissmissIconColor}` }}
								//onClick={handleClose}
							>
								<CloseIcon
									// color={dismissData.Color}
									htmlColor={dismissData.Color}
									// fontSize="medium"
									sx={{ fontSize: "1.5rem !important" }}
								/>
							</IconButton>
						</DialogTitle>
					) : null} */}

					
					{/* <PerfectScrollbar> */}
						<Box
							sx={{
								paddingRight: `${style?.Padding || 12}px`,
}}>
						{sections.map((section, index) => (
							<Box
								key={index}
								sx={{
									...sectionStyle,
									height: "auto",
									"&:hover": {
										borderTopWidth: index !== 0 ? "1px" : "0px",
										borderTopColor: index !== 0 ? "var(--primarycolor)" : "transparent",
										borderTopStyle: index !== 0 ? "dotted" : "none",
									},
								}}
								draggable
								onDragStart={() => handleDragStart(index)}
								onDragEnter={() => handleDragEnter(index)}
								onDragEnd={handleDragEnd}
							>
								<IconButton
									className="drag-icon"
									sx={dragButtonStyle}
								>
									<DragIndicatorIcon
										fontSize="small"
										sx={{ color: "#5F9EA0" }}
									/>
								</IconButton>

								{renderSection(section, index)}
								{index !== 0 && (
									<Tooltip arrow
										title={
											hasReachedLimit("text") && hasReachedLimit("button") && hasReachedLimit("image")
												? translate("Maximum limit reached for all section types")
												: translate("Add Section")
										}
									>
										<IconButton
											className="add-section"
											onClick={handleAddIconClick}
											sx={{
												backgroundColor: "#5F9EA0",
												"&:hover": {
													backgroundColor: "#70afaf",
												},
												borderRadius: "4px",
												padding: "5px !important",
												position: "absolute",
												top: "auto",
												left: "50%",
												transform: "translate(-50%, -50%)",
												opacity: "0",
												cursor: hasReachedLimit("text") && hasReachedLimit("button") && hasReachedLimit("image")
													? "not-allowed"
													: "pointer",
											}}
											disabled={hasReachedLimit("text") && hasReachedLimit("button") && hasReachedLimit("image")}
										>
											<AddIcon
												fontSize="small"
												sx={{ color: "#fff" }}
											/>
										</IconButton>
									</Tooltip>
								)}
							</Box>
						))}

						{Boolean(settingAnchorEl.value) ? (
							<div
								id="qadpt-designpopup"
								className="qadpt-designpopup qadpt-btnprop"
							>
								<div className="qadpt-content">
									<div className="qadpt-design-header">
											<div className="qadpt-title">{translate("Properties")}</div>
										<IconButton
											size="small"
											aria-label="close"
											onClick={() => handleCloseSettingPopup(settingAnchorEl.containerId, settingAnchorEl.buttonId)}
										>
											<CloseIcon />
										</IconButton>
									</div>
									<div className="qadpt-canblock qadpt-btnpro">
									<div className="qadpt-controls">
										<FormControl
											fullWidth
											sx={{ marginBottom: "16px" }}
										>
													<Typography sx={{ fontSize: "14px", fontWeight: "bold", my: "5px" }}>{translate("Button Name")}</Typography>
											<TextField
												value={currentButtonName}
												size="small"
													sx={{
									mb: "5px",
									border: "1px solid #ccc",
									borderRadius: "4px",
						
  "& .MuiOutlinedInput-root": {
    height: "35px",
    "&:hover .MuiOutlinedInput-notchedOutline": {
      border: "none !important",
    },
    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
      border: "none !important",
    },
  },
  "& .MuiOutlinedInput-notchedOutline": {
    border: "none !important",
  }

						}}
														placeholder={translate("Button Name")}
												onChange={(e) => setCurrentButtonName(e.target.value)}
											/>
													<Typography sx={{ fontSize: "14px", fontWeight: "bold", mb: "5px" }}>{translate("Button Action")}</Typography>
											<Select
												value={selectedActions.value}
												defaultValue="close"
												onChange={handleChangeActions}
														sx={{
									mb: "5px",
									border: "1px solid #ccc",
									borderRadius: "4px",
									textAlign: "left",
									"& .MuiSelect-select": {
								padding: "8px",
							},
						
  "& .MuiOutlinedInput-root": {
    height: "35px",
    "&:hover .MuiOutlinedInput-notchedOutline": {
      border: "none !important",
    },
    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
      border: "none !important",
    },
  },
  "& .MuiOutlinedInput-notchedOutline": {
    border: "none !important",
  }

						}}
											>
														<MenuItem value="close">{translate("Close")}</MenuItem>
														<MenuItem value="open-url">{translate("Open URL")}</MenuItem>
														<MenuItem value="Previous">{translate("Previous")}</MenuItem>
														<MenuItem value="Next">{translate("Next")}</MenuItem>
														<MenuItem value="Restart">{translate("Restart")}</MenuItem>
											</Select>
											{selectedActions.value === "open-url" ? (
												<>
															<Typography sx={{ fontSize: "14px", fontWeight: "bold", my: "5px" }}>{translate("Enter URL")}</Typography>
													<TextField
														value={targetURL}
														size="small"
														placeholder="https://quixy.com"
														onChange={(e) => {
															const newURL = e.target.value;
															setTargetURL(newURL); // Update the `targetURL` state with the new value
															handleURLChange(e); // Update the selectedButton.targetURL with the new value
															}}
															error={!!targetURLError}
															helperText={targetURLError}
													/>

													<ToggleButtonGroup
														value={selectedTab}
														onChange={handleChangeTabs}
														exclusive
																aria-label={translate("open in tab")}
														sx={{
															gap: "5px",
															marginY: "5px",
															height: "35px",
														}}
													>
														{["new-tab", "same-tab"].map((tab) => {
															return (
																<ToggleButton
																	value={tab}
																	aria-label="new tab"
																	sx={{
																		border: "1px solid #7EA8A5",
																		textTransform: "capitalize",
																		color: "#000",
																		borderRadius: "4px",
																		flex: 1,
																		padding: "0 !important",

																		"&.Mui-selected": {
																			backgroundColor: "var(--border-color)",
																			color: "#000",
																			border: "2px solid #7EA8A5",
																		},
																		"&:hover": {
																			backgroundColor: "#f5f5f5",
																		},
																		"&:last-child": {
																			borderLeft: "1px solid var(--primarycolor) !important", // Remove left border for the last button
																		},
																	}}
																>
																	{tab}
																</ToggleButton>
															);
														})}
													</ToggleButtonGroup>
												</>
											) : null}
											{/* {selectedActions === "start-interaction" ? (
												<>
													<Typography sx={{ fontSize: "14px", fontWeight: "bold", my: "5px" }}>
														Choose Interaction
													</Typography>

													<Autocomplete
														// sx={{ width: 300 }}
														open={openInteractionList}
														value={selectedInteraction}
														onChange={(event, newValue) => {
															setSelectedInteraction(newValue);
														}}
														onOpen={handleOpenInteraction}
														onClose={handleCloseInteraction}
														isOptionEqualToValue={(option, value) => {
															return option.guideId === value.guideId;
														}}
														getOptionLabel={(option) => {
															return option.title;
														}}
														size="small"
														freeSolo
														options={guideListByOrg}
														loading={loading}
														renderInput={(params) => (
															<TextField
																{...params}
																placeholder="Select Interaction"
																variant="outlined"
																slotProps={{
																	inputLabel: {
																		shrink: false,
																	},
																}}
															/>
														)}
													/>
												</>
											) : null} */}
										</FormControl>
										{/* <Typography sx={{ fontSize: "14px", fontWeight: "bold", mb: "5px" }}>Button Color</Typography> */}

										<Box
											className="qadpt-control-box"
											sx={{ borderRadius: "5px" }}
										>
													<Typography className="qadpt-control-label">{translate("Background")}</Typography>
											<input
												type="color"
												value={tempColors.backgroundColor}
												onChange={(e) => handleColorChange(e, "backgroundColor")}
												className="qadpt-color-input"
											/>
										</Box>

										<Box
											className="qadpt-control-box"
											sx={{ borderRadius: "5px" }}
										>
													<Typography className="qadpt-control-label">{translate("Border")}</Typography>
											<input
												type="color"
												value={tempColors.borderColor}
												onChange={(e) => handleColorChange(e, "borderColor")}
												className="qadpt-color-input"
											/>
										</Box>

										<Box
											className="qadpt-control-box"
											sx={{ borderRadius: "5px" }}
										>
													<Typography className="qadpt-control-label">{translate("Text")}</Typography>
											<input
												type="color"
												value={tempColors.color}
												onChange={(e) => handleColorChange(e, "color")}
												className="qadpt-color-input"
											/>
										</Box>
										</div>
										</div>

									<div className="qadpt-drawerFooter">
										<Button
											variant="contained"
											onClick={() => handleApplyChanges(settingAnchorEl.containerId, settingAnchorEl.buttonId)}
											className="qadpt-btn"
										>
												{translate("Apply")}
										</Button>
									</div>
								</div>
							</div>
						) : null}
					</Box>
					
					{/* </PerfectScrollbar> */}
					</DialogContent>
				</PerfectScrollbar>
				<Box sx={{		borderRadius: `${style?.Radius ?? 8}px`,
}}>
				{progress  &&
					(selectedOption === 1 || selectedOption === "" ? (
						<DotsStepper
							activeStep={currentStep}
							steps={steps.length}
								ProgressColor={ProgressColor}
								
						/>
					)
					: selectedOption === 2 ? (
						<div>
							<LinearProgress
								variant="determinate"
									sx={{
										height: "6px",
										borderRadius: "20px",
										margin: "6px 10px",
										backgroundColor: hexToRgba(ProgressColor, 0.45),
										'& .MuiLinearProgress-bar': {
									backgroundColor: ProgressColor, // progress bar color
								  },}}
								value={(currentStep / steps.length) * 100}

							/>
						</div>
					) : selectedOption === 3 ? (
						<div style={{ padding: "8px" }}>
							<BreadCrumpStepper
							activeStep={currentStep}
							steps={steps.length}
							ProgressColor={ProgressColor}

						/>
						</div>
					): selectedOption === 4 ? (
						<Breadcrumbs
							aria-label="breadcrumb"
							sx={{ padding: "8px" }}
						>
							<Typography
							>
								Step {currentStep} of {steps.length}
							</Typography>
						</Breadcrumbs>
							) : null)}
				</Box>
				</div>
				{anchorEl && (
					<Popover
												className="qadpt-secprop"
						open={Boolean(anchorEl)}
						anchorEl={anchorEl}
						onClose={handlePopoverClose}
						anchorOrigin={{
							vertical: "bottom",
							horizontal: "center",
						}}
						transformOrigin={{
							vertical: "top",
							horizontal: "center",
						}}
						slotProps={{
							paper: {
								sx: {
									padding: "12px",
									display: "flex",
									gap: "16px",
									width: "auto",
									zIndex: 1302,
								},
							},
						}}
						sx={{
							position: "absolute",
							// top: `${anchorEl.getBoundingClientRect().bottom + 8}px`,
							// left: `${anchorEl.getBoundingClientRect().left - 150}px`,
							transform: "none",
						}}
					>
						<Box
							display="flex"
							flexDirection="row"
							gap="16px"
						>
							{/* Rich Text Section */}
							<Tooltip arrow
								title={hasReachedLimit("text") ? translate("Maximum limit of 3 Rich Text sections reached") : ""}
								PopperProps={{
									sx: {
										zIndex: 9999,
									},
								}}
							>
								<Box
									display="flex"
									flexDirection="column"
									alignItems="center"
									sx={{
										cursor: hasReachedLimit("text") ? "not-allowed" : "pointer",
										opacity: hasReachedLimit("text") ? 0.5 : 1,
										svg: {
											fontSize: "24px !important"
										}
									}}
									onClick={() => !hasReachedLimit("text") && handleAddSection("text")}
								>
									<TextFormat />
									<Typography variant="caption"
										sx={{
											fontSize: "11px !important"
										}}
									>{translate("Rich Text")}</Typography>
								</Box>
							</Tooltip>

							{/* Button Section */}
							<Tooltip arrow
								title={hasReachedLimit("button") ? translate("Maximum limit of 3 Button sections reached") : ""}
								PopperProps={{
									sx: {
										zIndex: 9999,
									},
								}}
							>
								<Box
									display="flex"
									flexDirection="column"
									alignItems="center"
									sx={{
										cursor: hasReachedLimit("button") ? "not-allowed" : "pointer",
										opacity: hasReachedLimit("button") ? 0.5 : 1,
										svg: {
											fontSize: "24px !important"
										}
									}}
									onClick={() => !hasReachedLimit("button") && handleAddSection("button")}
								>
									<Link />
									<Typography variant="caption"
										sx={{
											fontSize: "11px !important"
										}}
									>{translate("Button")}</Typography>
								</Box>
							</Tooltip>

							{/* Image Section */}
							<Tooltip arrow
								title={hasReachedLimit("image") ? translate("Maximum limit of 3 Image sections reached") : ""}
								PopperProps={{
									sx: {
										zIndex: 9999,
									},
								}}
							>
								<Box
									display="flex"
									flexDirection="column"
									alignItems="center"
									sx={{
										cursor: hasReachedLimit("image") ? "not-allowed" : "pointer",
										opacity: hasReachedLimit("image") ? 0.5 : 1,
										svg: {
											fontSize: "24px !important"
										}
									}}
									onClick={() => !hasReachedLimit("image") && handleAddSection("image")}
								>
									<Image />
									<Typography variant="caption"
										sx={{
											fontSize: "11px !important"
										}}
									>{translate("Image")}</Typography>
								</Box>
							</Tooltip>
							<Tooltip arrow
								title={translate("Coming Soon")}
								PopperProps={{
									sx: {
										zIndex: 9999,
									},
								}}
							>
								<span>
									<Box
										display="flex"
										flexDirection="column"
										alignItems="center"
										sx={{
											cursor: "pointer",
											opacity: 0.5,
											svg: {
												fontSize: "24px !important"
											}
										}}
										// onClick={() => handleAddSection("video")}
									>
										<VideoLibrary />
										<Typography variant="caption"
											sx={{
												fontSize: "11px !important"
											}}
										>{translate("Video")}</Typography>
									</Box>
								</span>
							</Tooltip>
							{/* {<Box
								display="flex"
								flexDirection="column"
								alignItems="center"
								sx={{ cursor: "pointer", opacity: 0.5 }}
								onClick={() => handleAddSection("gif")}
							>
								<GifBox />
								<Typography variant="caption">Gif</Typography>
							</Box>*/}
							<Tooltip arrow
								title={translate("Coming Soon")}
								PopperProps={{
									sx: {
										zIndex: 9999,
									},
								}}
							>
								<span>
									<Box
										display="flex"
										flexDirection="column"
										alignItems="center"
										sx={{
											cursor: "pointer",
											opacity: 0.5,
											svg: {
												fontSize: "24px !important"
											}
										}}
										onClick={() => handleAddSection("html")}
									>
										<Code />
										<Typography variant="caption"
											sx={{
												fontSize: "11px !important"
											}}
										>{translate("HTML")}</Typography>
									</Box>
								</span>
							</Tooltip>
						</Box>
					</Popover>
				)}

			</Dialog>
		</div>
	);
};
export default GuidePopup;
const DotsStepper = ({ steps, activeStep, ProgressColor }: { steps: number; activeStep: number; ProgressColor:any }) => {
	return (
		<MobileStepper
			variant="dots"
			steps={steps}
			position="static"
			activeStep={activeStep - 1}
			sx={{ flexGrow: 1, display: "flex", justifyContent: "center",background:"inherit","& .MuiMobileStepper-dotActive": {
				backgroundColor: ProgressColor, // active dot color
			  } }}
			nextButton={<></>}
			backButton={<></>}

		/>
	);
};
const BreadCrumpStepper = ({ steps, activeStep,ProgressColor }: { steps: number; activeStep: number; ProgressColor:any }) => {
	return (
		<Box sx={{ flexGrow: 1 }}>
		  {/* Custom Step Indicators */}
		  <Box
			sx={{
			  display: 'flex',
			  justifyContent: 'center',
			  gap: "4px", // Adjust space between steps
			//   paddingTop: '15px',
			}}
		  >
			{Array.from({ length: steps }).map((_, index) => (
			  <div
				key={index}
				style={{
				  width: '14px',
				  height: '4px',
				  backgroundColor: index === activeStep - 1 ? ProgressColor : hexToRgba(ProgressColor, 0.45), // Active color and inactive color
				  borderRadius: '100px',
				}}
			  />
			))}
		  </Box>
		</Box>
	  );
};
