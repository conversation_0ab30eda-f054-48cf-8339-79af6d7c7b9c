import React from 'react';
import { <PERSON>, <PERSON>po<PERSON>, <PERSON>ton, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { errorIcon } from '../../assets/icons/icons';

// Define the types for the props
interface LogoutPopupProps {
  onClose: () => void;
  onOk: () => void;
  title: string;
    description: string;
  button1: string;
  button2: string;
}

const LogoutPopup: React.FC<LogoutPopupProps> = ({ onClose, onOk, title, description, button1, button2 }) => {
  const { t: translate } = useTranslation();
  return (
    <Dialog
      open
      onClose={onClose}
      sx={{ zIndex: 99999 }}
      PaperProps={{
        style: {
          borderRadius: "4px",
          maxWidth: "400px",
          textAlign: "center",
          maxHeight: "300px",
          boxShadow: "none",
        },
      }}
    >
      <DialogTitle sx={{ padding: 0 }}>
					<div style={{ display: "flex", justifyContent: "center", padding: "10px" }}>
						<div
							style={{
								// backgroundColor: "#e4b6b0",
								borderRadius: "50%",
								width: "40px",
								height: "40px",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
							}}
						>
            <span dangerouslySetInnerHTML={{ __html: errorIcon }} />
						</div>
					</div>
					<Typography sx={{ fontSize: "16px !important", fontWeight: 600, padding: "0 10px" }} >
          {translate(title)}
					</Typography>
				</DialogTitle>
      {/* <DialogTitle sx={{ padding: "0px !important" }}>
        <Typography
          sx={{
            fontFamily: 'Poppins',
            fontSize: '16px !important',
            fontWeight: 'bold',
            lineHeight: '30px',
            textAlign: 'center',
          }}
        >
          {translateitle}
        </Typography>
      </DialogTitle> */}
      <DialogContent sx={{ padding: "20px !important" }}>
        <Typography
          sx={{
            fontSize: '14px',
          }}
        >
          {translate(description)}
              </Typography>
              {/* <Typography
          sx={{
            fontFamily: 'Poppins',
            fontSize: '14px',
            fontWeight: 200,
            lineHeight: '20px',
            textAlign: 'center',
            whiteSpace: 'nowrap', 
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {description1}
              </Typography> */}
      </DialogContent>
      <DialogActions sx={{ justifyContent: "space-between", borderTop: "1px solid var(--border-color)" }}>
        <Button
          onClick={onClose}
          sx={{
            color: "var(--primarycolor)",
            border:"1px solid var(--primarycolor)",
            borderRadius: "4px",
            textTransform: "capitalize",
            padding: "var(--button-padding)",
            lineHeight: "var(--button-lineheight)",
            fontSize:"14px"
          }}
        >
          {translate(button1)}
        </Button>
        <Button
          onClick={onOk}
          
          sx={{
              color: "var( --white-color)",
							background: "var(--primarycolor)",
							borderRadius: "4px",
							textTransform: "capitalize",
							padding: "var(--button-padding)",
            lineHeight: "var(--button-lineheight)",
                          fontSize:"14px"
          }}
        >
          {translate(button2)}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default LogoutPopup;
